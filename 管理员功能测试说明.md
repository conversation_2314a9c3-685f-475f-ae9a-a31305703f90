# 管理员功能测试说明

## 测试环境准备

### 1. 数据库准备
确保数据库中有以下测试数据：

```sql
-- 创建管理员用户（userLevelId = 1）
UPDATE weshop_user SET user_level_id = 1 WHERE id = 1;

-- 创建一些测试订单
INSERT INTO weshop_order (order_sn, user_id, order_status, pay_status, consignee, mobile, address, order_price, actual_price, create_time) VALUES
('WJ202501260001', 1, 1, 1, '张三', '138****8888', '广东省深圳市南山区科技园', 299.00, 299.00, NOW()),
('WJ202501260002', 2, 0, 0, '李四', '139****9999', '广东省广州市天河区', 158.00, 158.00, NOW()),
('WJ202501260003', 3, 2, 1, '王五', '137****7777', '广东省东莞市', 568.00, 568.00, NOW());

-- 创建一些测试用户
INSERT INTO weshop_user (nickname, username, mobile, register_time, user_level_id) VALUES
('测试用户1', 'testuser1', '138****1111', NOW(), 0),
('测试用户2', 'testuser2', '139****2222', NOW(), 1),
('测试用户3', 'testuser3', '137****3333', NOW(), 0);
```

### 2. 后端服务启动
确保后端服务正常运行，管理员接口可访问：
- `/wechat/admin/stats` - 统计数据接口
- `/wechat/admin/order/list` - 订单列表接口
- `/wechat/admin/user/list` - 用户列表接口
- `/wechat/admin/order/deliver` - 发货接口
- `/wechat/admin/order/cancel` - 取消订单接口

## 功能测试步骤

### 1. 权限测试

#### 1.1 非管理员用户测试
1. 使用普通用户（userLevelId != 1）登录小程序
2. 进入个人中心页面
3. **预期结果**：在服务中心部分看不到"管理员中心"入口

#### 1.2 管理员用户测试
1. 使用管理员用户（userLevelId = 1）登录小程序
2. 进入个人中心页面
3. **预期结果**：在服务中心部分可以看到"管理员中心"入口

### 2. 管理员中心主页测试

#### 2.1 统计数据显示
1. 点击"管理员中心"进入主页
2. **预期结果**：
   - 显示总订单数、总用户数、总交易额、今日订单等统计数据
   - 数据来自真实的数据库查询
   - 统计卡片布局美观

#### 2.2 快速导航测试
1. 点击"所有订单"按钮
2. **预期结果**：跳转到订单管理页面
3. 返回主页，点击"所有用户"按钮
4. **预期结果**：跳转到用户管理页面

### 3. 订单管理功能测试

#### 3.1 订单列表显示
1. 进入订单管理页面
2. **预期结果**：
   - 显示所有订单列表
   - 包含订单号、用户信息、商品信息、订单金额等
   - 显示正确的订单状态

#### 3.2 状态筛选测试
1. 点击"待支付"标签
2. **预期结果**：只显示待支付订单
3. 点击"待发货"标签
4. **预期结果**：只显示待发货订单
5. 点击"全部"标签
6. **预期结果**：显示所有订单

#### 3.3 搜索功能测试
1. 在搜索框输入订单号
2. 点击搜索按钮
3. **预期结果**：显示匹配的订单
4. 在搜索框输入用户昵称
5. 点击搜索按钮
6. **预期结果**：显示该用户的订单

#### 3.4 订单操作测试
1. 找到一个待发货订单，点击"发货"按钮
2. 确认发货操作
3. **预期结果**：
   - 显示"发货成功"提示
   - 订单状态更新为"已发货"
   - 列表自动刷新

4. 找到一个待支付订单，点击"取消订单"按钮
5. 确认取消操作
6. **预期结果**：
   - 显示"订单已取消"提示
   - 订单状态更新为"已取消"
   - 列表自动刷新

#### 3.5 订单详情测试
1. 点击任意订单的"查看详情"按钮
2. **预期结果**：
   - 弹出订单详情窗口
   - 显示完整的订单信息、收货信息、金额信息
   - 数据来自真实的订单详情接口

### 4. 用户管理功能测试

#### 4.1 用户列表显示
1. 进入用户管理页面
2. **预期结果**：
   - 显示所有用户列表
   - 包含用户头像、昵称、手机号、等级等信息
   - 显示用户的订单数、消费金额等统计数据

#### 4.2 用户类型筛选测试
1. 点击"新用户"标签
2. **预期结果**：显示最近7天注册的用户
3. 点击"VIP用户"标签
4. **预期结果**：显示VIP等级用户
5. 点击"活跃用户"标签
6. **预期结果**：显示最近活跃的用户

#### 4.3 用户搜索测试
1. 在搜索框输入用户昵称
2. 点击搜索按钮
3. **预期结果**：显示匹配的用户
4. 在搜索框输入手机号
5. 点击搜索按钮
6. **预期结果**：显示匹配的用户

#### 4.4 用户详情测试
1. 点击任意用户的"查看详情"按钮
2. **预期结果**：
   - 弹出用户详情窗口
   - 显示用户基本信息、消费数据、推广数据
   - 数据准确完整

### 5. 分页功能测试

#### 5.1 订单分页测试
1. 在订单管理页面向下滚动
2. **预期结果**：自动加载更多订单数据
3. 下拉刷新页面
4. **预期结果**：重新加载第一页数据

#### 5.2 用户分页测试
1. 在用户管理页面向下滚动
2. **预期结果**：自动加载更多用户数据
3. 下拉刷新页面
4. **预期结果**：重新加载第一页数据

## 性能测试

### 1. 接口响应时间
- 统计数据接口响应时间 < 2秒
- 订单列表接口响应时间 < 3秒
- 用户列表接口响应时间 < 3秒
- 订单操作接口响应时间 < 1秒

### 2. 数据准确性
- 统计数据与数据库实际数据一致
- 订单状态更新及时反映到界面
- 搜索结果准确无误
- 分页数据无重复或遗漏

## 异常情况测试

### 1. 网络异常
1. 断开网络连接
2. 尝试加载数据
3. **预期结果**：显示友好的错误提示，提供重试机制

### 2. 权限异常
1. 在使用过程中修改用户权限
2. 尝试访问管理员功能
3. **预期结果**：提示权限不足，自动跳转

### 3. 数据异常
1. 删除测试数据
2. 尝试查看详情
3. **预期结果**：显示友好的错误提示

## 测试通过标准

- ✅ 所有权限控制正常工作
- ✅ 统计数据显示准确
- ✅ 订单管理功能完整可用
- ✅ 用户管理功能完整可用
- ✅ 搜索和筛选功能正常
- ✅ 分页加载功能正常
- ✅ 订单操作功能正常
- ✅ 界面美观，用户体验良好
- ✅ 异常情况处理得当
- ✅ 性能满足要求
