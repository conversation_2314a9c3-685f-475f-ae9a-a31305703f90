# 优惠券退回功能部署检查清单

## 📋 部署前检查

### 1. 数据库修改
- [ ] 执行 `database/alter_user_coupon_add_history_fields.sql` 脚本
- [ ] 验证新字段已成功添加到 `weshop_user_coupon` 表
- [ ] 检查索引是否正确创建

### 2. 后端代码编译
- [ ] 确认 `UserCoupon.java` 实体类包含新字段
- [ ] 确认 `UserCouponServiceImpl.java` 逻辑修改正确
- [ ] 确认 `OrderService.java` 查询逻辑正确
- [ ] 确认 `OrderDetailVO.java` 包含新的VO字段
- [ ] 编译通过，无语法错误

### 3. 前端页面
- [ ] 确认 `orderDetail.wxml` 模板修改正确
- [ ] 确认 `orderDetail.wxss` 样式添加正确
- [ ] 前端构建无错误

## 🧪 功能测试

### 1. 数据库测试
```sql
-- 执行测试脚本
source database/test_coupon_refund_simple.sql;
```

### 2. 接口测试
- [ ] 测试优惠券使用接口
- [ ] 测试订单取消接口
- [ ] 测试订单详情查询接口

### 3. 前端测试
- [ ] 创建使用优惠券的订单
- [ ] 查看订单详情页面显示优惠券信息
- [ ] 取消订单
- [ ] 再次查看订单详情，确认显示"已退回"标识
- [ ] 验证优惠券已退回到用户账户

## 🔍 验证要点

### 数据库层面
```sql
-- 检查优惠券表结构
DESCRIBE weshop_user_coupon;

-- 验证历史记录保存
SELECT id, title, status, order_id, last_used_order_id, is_refunded 
FROM weshop_user_coupon 
WHERE last_used_order_id IS NOT NULL;
```

### 应用层面
1. **使用优惠券时**：
   - `status` = 1
   - `order_id` = 订单ID
   - `last_used_order_id` = 订单ID
   - `is_refunded` = false

2. **取消订单后**：
   - `status` = 0
   - `order_id` = null
   - `last_used_order_id` = 订单ID（保持不变）
   - `is_refunded` = true

3. **订单详情查询**：
   - 能正确显示优惠券标题
   - 已取消订单显示"已退回"标识

## 🚨 回滚方案

如果出现问题，可以执行以下回滚操作：

### 数据库回滚
```sql
-- 删除新增字段（谨慎操作）
ALTER TABLE `weshop_user_coupon` 
DROP COLUMN `last_used_order_id`,
DROP COLUMN `last_use_time`,
DROP COLUMN `is_refunded`,
DROP COLUMN `refund_time`;
```

### 代码回滚
- 恢复 `UserCoupon.java` 实体类
- 恢复 `UserCouponServiceImpl.java` 原始逻辑
- 恢复 `OrderService.java` 原始查询逻辑
- 恢复前端页面原始代码

## 📊 监控指标

部署后需要监控的指标：
- [ ] 优惠券使用成功率
- [ ] 订单取消后优惠券退回成功率
- [ ] 订单详情页面加载性能
- [ ] 用户反馈和投诉

## ✅ 部署完成确认

- [ ] 数据库修改完成
- [ ] 后端代码部署完成
- [ ] 前端代码部署完成
- [ ] 功能测试通过
- [ ] 性能测试通过
- [ ] 用户验收测试通过

---

**注意事项**：
1. 建议在测试环境先完整测试后再部署到生产环境
2. 部署时建议先备份相关数据
3. 监控部署后的系统运行状况
4. 准备好快速回滚方案
