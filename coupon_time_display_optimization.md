# 优惠券时间显示换行问题修复

## 问题描述
优惠券有效期显示内容太长时出现换行现象，影响页面美观和用户体验。

## 问题分析
1. **原始时间格式过长**：使用 `YYYY-MM-DD - YYYY-MM-DD` 格式，在小屏幕上容易换行
2. **CSS布局限制**：原有的flex布局没有很好地处理长文本的显示
3. **缺乏响应式设计**：没有考虑不同屏幕尺寸下的显示效果

## 解决方案

### 1. 优化时间格式
**采用更简洁的日期格式**：
- 当年日期：`3/15` 或 `12/25`
- 跨年日期：`2025/3/15` 或 `2024/12/25`
- 时间范围：`3/15-12/25` 或 `至12/25`

### 2. 改进CSS布局
**优化 `.coupon-time` 样式**：
```css
.coupon-time {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
  line-height: 1.4;
  margin-top: 4rpx;
}

.time-label {
  margin-right: 8rpx;
  flex-shrink: 0;
  white-space: nowrap;
  font-size: 22rpx;
}

.time-content {
  flex: 1;
  min-width: 0;
}

.time-value {
  display: inline-block;
  font-size: 22rpx;
  line-height: 1.3;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
```

### 3. 优化WXML结构
**简化模板结构**：
```xml
<view class="coupon-time">
  <text class="time-label">有效期：</text>
  <view class="time-content">
    <text class="time-value">{{item.timeDisplay || '永久有效'}}</text>
  </view>
</view>
```

### 4. 增强JavaScript处理逻辑
**添加智能时间格式化**：
```javascript
// 格式化日期 - 使用简洁格式
formatDate: function (dateStr) {
  if (!dateStr) return '';
  
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    
    const now = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    // 如果是当年，只显示月/日
    if (year === now.getFullYear()) {
      return `${month}/${day}`;
    } else {
      // 如果是其他年份，显示年/月/日
      return `${year}/${month}/${day}`;
    }
  } catch (e) {
    console.error('日期格式化失败:', e);
    return dateStr;
  }
}

// 格式化优惠券时间显示 - 使用更简洁的格式
formatCouponTimeDisplay: function (startTimeStr, endTimeStr) {
  if (startTimeStr && endTimeStr) {
    const startFormatted = this.formatDate(startTimeStr);
    const endFormatted = this.formatDate(endTimeStr);
    return `${startFormatted}-${endFormatted}`;
  } else if (endTimeStr) {
    return `至${this.formatDate(endTimeStr)}`;
  } else {
    return '永久有效';
  }
}
```

## 修复效果

### 修复前
- 显示格式：`2025-07-27 - 2025-10-25`
- 问题：文本过长，容易换行，占用空间大

### 修复后
- 显示格式：`7/27-10/25`（当年）或 `2025/7/27-10/25`（跨年）
- 优势：
  - 文本更简洁，不易换行
  - 视觉效果更好，有背景色标识
  - 支持文本溢出省略
  - 响应式设计，适配不同屏幕

## 涉及文件

### 前端文件
1. **`app/wjhx/pages/ucenter/coupon/coupon.wxml`**
   - 简化时间显示结构
   - 使用统一的 `timeDisplay` 字段

2. **`app/wjhx/pages/ucenter/coupon/coupon.wxss`**
   - 优化 `.coupon-time` 布局
   - 添加 `.time-content` 和 `.time-value` 样式
   - 支持文本溢出处理

3. **`app/wjhx/pages/ucenter/coupon/coupon.js`**
   - 添加 `formatDate()` 简洁日期格式化方法
   - 添加 `formatCouponTimeDisplay()` 时间显示格式化方法
   - 优化优惠券数据处理逻辑

4. **`app/wjhx/pages/shopping/coupon-select/coupon-select.js`**
   - 同步优化优惠券选择页面的时间格式化
   - 保持各页面时间显示的一致性

## 兼容性考虑

### 数据兼容性
- 支持原有的 `remark` 字段格式
- 支持新的简洁时间格式
- 向后兼容，不影响现有数据

### 显示兼容性
- 支持不同长度的时间文本
- 支持永久有效的优惠券
- 支持只有结束时间的优惠券

### 设备兼容性
- 适配小屏幕设备
- 支持不同分辨率
- 响应式布局设计

## 测试建议

### 功能测试
1. 测试不同时间格式的优惠券显示
2. 测试当年和跨年日期的显示效果
3. 测试永久有效优惠券的显示
4. 测试异常数据的处理

### 视觉测试
1. 测试不同屏幕尺寸下的显示效果
2. 测试文本是否还会换行
3. 测试背景色和圆角效果
4. 测试文本溢出省略功能

### 性能测试
1. 测试时间格式化的性能
2. 测试页面渲染速度
3. 测试内存使用情况

## 总结
通过采用更简洁的时间格式、优化CSS布局和增强JavaScript处理逻辑，成功解决了优惠券时间显示换行的问题。新的设计不仅解决了换行问题，还提升了整体的视觉效果和用户体验。
