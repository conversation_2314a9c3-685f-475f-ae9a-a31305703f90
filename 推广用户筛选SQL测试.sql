-- 推广用户筛选SQL测试脚本

-- 1. 测试修正后的推广用户查询
-- 这个查询应该与MyBatis-Plus生成的SQL一致
SELECT 
    id, username, nickname, mobile, register_time, 
    last_login_time, user_level_id, avatar, balance, points,
    promoter_id, promotion_count
FROM weshop_user 
WHERE EXISTS (SELECT 1 FROM weshop_user u2 WHERE u2.promoter_id = weshop_user.id)
ORDER BY register_time DESC 
LIMIT 0, 10;

-- 2. 验证推广用户统计查询
SELECT COUNT(*) as promoter_user_count
FROM weshop_user 
WHERE EXISTS (SELECT 1 FROM weshop_user u2 WHERE u2.promoter_id = weshop_user.id);

-- 3. 检查推广关系数据
SELECT 
    '推广关系统计' as description,
    COUNT(*) as total_users,
    COUNT(CASE WHEN promoter_id IS NOT NULL THEN 1 END) as users_with_promoter,
    COUNT(DISTINCT promoter_id) as unique_promoters
FROM weshop_user;

-- 4. 查看推广用户详情（前10个）
SELECT 
    u1.id,
    u1.nickname,
    u1.mobile,
    u1.register_time,
    COUNT(u2.id) as promoted_count
FROM weshop_user u1
LEFT JOIN weshop_user u2 ON u2.promoter_id = u1.id
WHERE EXISTS (SELECT 1 FROM weshop_user u3 WHERE u3.promoter_id = u1.id)
GROUP BY u1.id, u1.nickname, u1.mobile, u1.register_time
ORDER BY promoted_count DESC, u1.register_time DESC
LIMIT 10;

-- 5. 如果没有推广关系数据，创建测试数据
-- 注意：请根据实际的用户ID调整以下数据

-- 查看现有用户ID
SELECT id, nickname FROM weshop_user ORDER BY id LIMIT 10;

-- 创建测试推广关系（请根据实际ID调整）
-- 假设用户ID 1 推广了用户ID 2 和 3
-- UPDATE weshop_user SET promoter_id = 1 WHERE id IN (2, 3);

-- 假设用户ID 4 推广了用户ID 5
-- UPDATE weshop_user SET promoter_id = 4 WHERE id = 5;

-- 6. 验证其他筛选条件（确保不冲突）

-- 新用户筛选测试
SELECT COUNT(*) as new_users_count
FROM weshop_user 
WHERE register_time >= DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 活跃用户筛选测试
SELECT COUNT(*) as active_users_count
FROM weshop_user 
WHERE last_login_time >= DATE_SUB(NOW(), INTERVAL 3 DAY);

-- VIP用户筛选测试
SELECT COUNT(*) as vip_users_count
FROM weshop_user 
WHERE user_level_id > 0;

-- 7. 组合查询测试（推广用户 + 关键词搜索）
-- 这个查询模拟前端搜索推广用户的场景
SELECT 
    id, nickname, mobile, register_time
FROM weshop_user 
WHERE EXISTS (SELECT 1 FROM weshop_user u2 WHERE u2.promoter_id = weshop_user.id)
  AND (nickname LIKE '%test%' OR mobile LIKE '%test%')
ORDER BY register_time DESC 
LIMIT 10;

-- 8. 性能测试查询
-- 检查查询执行计划
EXPLAIN SELECT 
    id, nickname, mobile, register_time
FROM weshop_user 
WHERE EXISTS (SELECT 1 FROM weshop_user u2 WHERE u2.promoter_id = weshop_user.id)
ORDER BY register_time DESC 
LIMIT 10;

-- 9. 建议的索引（如果性能有问题）
-- CREATE INDEX idx_weshop_user_promoter_id ON weshop_user(promoter_id);
-- CREATE INDEX idx_weshop_user_register_time ON weshop_user(register_time);

-- 10. 数据完整性检查
-- 检查是否有无效的推广关系
SELECT 
    u1.id as user_id,
    u1.nickname as user_name,
    u1.promoter_id,
    u2.nickname as promoter_name
FROM weshop_user u1
LEFT JOIN weshop_user u2 ON u1.promoter_id = u2.id
WHERE u1.promoter_id IS NOT NULL AND u2.id IS NULL;