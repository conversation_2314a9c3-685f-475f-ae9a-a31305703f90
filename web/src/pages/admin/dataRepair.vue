<template>
  <div class="data-repair-container">
    <Card>
      <p slot="title">
        <Icon type="ios-hammer"></Icon>
        数据修复工具
      </p>
      <div class="repair-content">
        <Alert type="info" show-icon>
          <span slot="desc">此工具用于修复数据库中编码损坏的中文字符。请谨慎操作，建议先备份数据库。</span>
        </Alert>
        
        <div class="repair-section">
          <h3>商品数据修复</h3>
          <Button type="primary" @click="loadCorruptedProducts" :loading="loading">
            <Icon type="ios-search"></Icon>
            查找损坏的商品数据
          </Button>
          
          <div v-if="corruptedProducts.length > 0" class="corrupted-list">
            <h4>发现 {{ corruptedProducts.length }} 个损坏的商品记录：</h4>
            <Table :columns="productColumns" :data="corruptedProducts" border>
              <template slot-scope="{ row, index }" slot="action">
                <Button type="success" size="small" @click="showRepairModal(row)">
                  <Icon type="ios-build"></Icon>
                  修复
                </Button>
              </template>
            </Table>
          </div>
          
          <div v-else-if="searchCompleted" class="no-data">
            <Alert type="success" show-icon>
              <span slot="desc">未发现损坏的商品数据，所有数据编码正常。</span>
            </Alert>
          </div>
        </div>
      </div>
    </Card>
    
    <!-- 修复对话框 -->
    <Modal v-model="repairModal" title="修复商品数据" width="600" :mask-closable="false">
      <Form ref="repairForm" :model="repairData" :label-width="100">
        <FormItem label="商品ID">
          <Input v-model="repairData.id" disabled></Input>
        </FormItem>
        <FormItem label="商品名称">
          <Input v-model="repairData.name" placeholder="请输入正确的商品名称"></Input>
        </FormItem>
        <FormItem label="关键词">
          <Input v-model="repairData.keywords" placeholder="请输入正确的关键词"></Input>
        </FormItem>
        <FormItem label="商品简介">
          <Input v-model="repairData.goodsBrief" type="textarea" :rows="3" placeholder="请输入正确的商品简介"></Input>
        </FormItem>
        <FormItem label="商品描述">
          <Input v-model="repairData.goodsDesc" type="textarea" :rows="5" placeholder="请输入正确的商品描述"></Input>
        </FormItem>
        <FormItem label="商品单位">
          <Input v-model="repairData.goodsUnit" placeholder="请输入正确的商品单位"></Input>
        </FormItem>
      </Form>
      
      <div slot="footer">
        <Button @click="repairModal = false">取消</Button>
        <Button type="primary" @click="repairProduct" :loading="repairLoading">
          <Icon type="ios-checkmark"></Icon>
          确认修复
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'DataRepair',
  data() {
    return {
      loading: false,
      searchCompleted: false,
      corruptedProducts: [],
      repairModal: false,
      repairLoading: false,
      repairData: {
        id: '',
        name: '',
        keywords: '',
        goodsBrief: '',
        goodsDesc: '',
        goodsUnit: ''
      },
      productColumns: [
        {
          title: 'ID',
          key: 'id',
          width: 80
        },
        {
          title: '商品名称',
          key: 'name',
          width: 150,
          render: (h, params) => {
            return h('span', {
              style: {
                color: this.isCorrupted(params.row.name) ? '#ed4014' : '#2d8cf0'
              }
            }, params.row.name || '(空)');
          }
        },
        {
          title: '关键词',
          key: 'keywords',
          width: 120,
          render: (h, params) => {
            return h('span', {
              style: {
                color: this.isCorrupted(params.row.keywords) ? '#ed4014' : '#2d8cf0'
              }
            }, params.row.keywords || '(空)');
          }
        },
        {
          title: '商品简介',
          key: 'goodsBrief',
          width: 200,
          render: (h, params) => {
            const text = params.row.goodsBrief || '(空)';
            return h('span', {
              style: {
                color: this.isCorrupted(text) ? '#ed4014' : '#2d8cf0'
              }
            }, text.length > 20 ? text.substring(0, 20) + '...' : text);
          }
        },
        {
          title: '商品单位',
          key: 'goodsUnit',
          width: 100,
          render: (h, params) => {
            return h('span', {
              style: {
                color: this.isCorrupted(params.row.goodsUnit) ? '#ed4014' : '#2d8cf0'
              }
            }, params.row.goodsUnit || '(空)');
          }
        },
        {
          title: '操作',
          slot: 'action',
          width: 100,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    // 检查文本是否包含损坏的字符
    isCorrupted(text) {
      if (!text) return false;
      // 检查是否包含问号或其他乱码字符
      return /\?{2,}|[^\u4e00-\u9fa5\w\s\-_.,!@#$%^&*()+=<>?/\\|`~;:'"[\]{}]/g.test(text);
    },
    
    // 加载损坏的商品数据
    async loadCorruptedProducts() {
      this.loading = true;
      this.searchCompleted = false;
      this.corruptedProducts = [];
      
      try {
        // 获取所有商品数据
        const response = await this.$http.get('/adminapi/product', {
          params: {
            page: 1,
            limit: 1000 // 获取大量数据进行检查
          }
        });
        
        if (response.data.success) {
          const products = response.data.data.records || [];
          
          // 筛选出包含损坏字符的商品
          this.corruptedProducts = products.filter(product => {
            return this.isCorrupted(product.name) ||
                   this.isCorrupted(product.keywords) ||
                   this.isCorrupted(product.goodsBrief) ||
                   this.isCorrupted(product.goodsDesc) ||
                   this.isCorrupted(product.goodsUnit);
          });
          
          this.$Message.success(`检查完成，发现 ${this.corruptedProducts.length} 个损坏的商品记录`);
        } else {
          this.$Message.error('获取商品数据失败：' + response.data.msg);
        }
      } catch (error) {
        console.error('加载商品数据失败:', error);
        this.$Message.error('加载商品数据失败，请检查网络连接');
      } finally {
        this.loading = false;
        this.searchCompleted = true;
      }
    },
    
    // 显示修复对话框
    showRepairModal(product) {
      this.repairData = {
        id: product.id,
        name: product.name || '',
        keywords: product.keywords || '',
        goodsBrief: product.goodsBrief || '',
        goodsDesc: product.goodsDesc || '',
        goodsUnit: product.goodsUnit || ''
      };
      this.repairModal = true;
    },
    
    // 修复商品数据
    async repairProduct() {
      // 验证必填字段
      if (!this.repairData.name.trim()) {
        this.$Message.error('商品名称不能为空');
        return;
      }
      
      this.repairLoading = true;
      
      try {
        const response = await this.$http.post(`/adminapi/product/fix-encoding/${this.repairData.id}`, {
          name: this.repairData.name.trim(),
          keywords: this.repairData.keywords.trim(),
          goodsBrief: this.repairData.goodsBrief.trim(),
          goodsDesc: this.repairData.goodsDesc.trim(),
          goodsUnit: this.repairData.goodsUnit.trim()
        });
        
        if (response.data.success) {
          this.$Message.success('商品数据修复成功');
          this.repairModal = false;
          
          // 更新列表中的数据
          const index = this.corruptedProducts.findIndex(p => p.id === this.repairData.id);
          if (index !== -1) {
            this.corruptedProducts.splice(index, 1);
          }
          
          // 如果所有数据都修复完成，重新检查
          if (this.corruptedProducts.length === 0) {
            this.$Message.info('所有损坏数据已修复完成');
          }
        } else {
          this.$Message.error('修复失败：' + response.data.msg);
        }
      } catch (error) {
        console.error('修复商品数据失败:', error);
        this.$Message.error('修复失败，请检查网络连接');
      } finally {
        this.repairLoading = false;
      }
    }
  }
};
</script>

<style scoped>
.data-repair-container {
  padding: 20px;
}

.repair-content {
  margin-top: 20px;
}

.repair-section {
  margin-top: 30px;
}

.repair-section h3 {
  margin-bottom: 15px;
  color: #2d8cf0;
  font-size: 16px;
  font-weight: 600;
}

.corrupted-list {
  margin-top: 20px;
}

.corrupted-list h4 {
  margin-bottom: 15px;
  color: #ed4014;
  font-size: 14px;
}

.no-data {
  margin-top: 20px;
}
</style>
