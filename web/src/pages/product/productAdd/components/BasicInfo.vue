<template>
  <!-- 基础信息 -->
  <el-row :gutter="24">
    <el-col :span="24">
      <el-form-item label="商品类型：" props="is_virtual">
        <div
          class="virtual"
          :class="formValidate.virtual_type == item.id ? 'virtual_boder' : 'virtual_boder2'"
          v-for="(item, index) in goodsType"
          :key="index"
          v-db-click
          @click="virtualbtn(item.id, 2)"
          v-show="
            (formValidate.id && formValidate.virtual_type == item.id) ||
            (isCai == -1 && index == 0 && !formValidate.id) ||
            (isCai == 0 && !formValidate.id)
          "
        >
          <div class="virtual_top">{{ item.tit }}</div>
          <div class="virtual_bottom">({{ item.tit2 }})</div>
          <div v-if="formValidate.virtual_type == item.id" class="virtual_san"></div>
          <div v-if="formValidate.virtual_type == item.id" class="virtual_dui">✓</div>
        </div>
      </el-form-item>
    </el-col>

    <el-col :span="24">
      <el-form-item label="商品名称：" prop="store_name">
        <el-input
          class="content_width"
          v-model="formValidate.store_name"
          placeholder="请输入商品名称"
          maxlength="80"
          show-word-limit
        />
      </el-form-item>
    </el-col>

    <el-col :span="24">
      <el-form-item label="单位：" prop="unit_name">
        <el-input
          class="input_width"
          v-model="formValidate.unit_name"
          placeholder="请输入单位"
          maxlength="5"
          show-word-limit
        />
      </el-form-item>
    </el-col>

    <el-col :span="24">
      <el-form-item label="商品单价：" prop="unit_price">
        <el-input-number
          class="input_width"
          v-model="formValidate.unit_price"
          placeholder="请输入商品单价"
          :min="0"
          :max="999999.99"
          :precision="2"
          :step="0.01"
          controls-position="right"
        />
        <span class="unit-text">元</span>
        <div class="tips-info">商品的基础单价，用于价格计算和展示</div>
      </el-form-item>
    </el-col>

    <!-- 商品主图 -->
    <el-col :span="24">
      <el-form-item label="商品主图：" prop="image">
        <div class="main-image-container">
          <div class="main-image-preview" v-if="formValidate.image">
            <img :src="formatImageUrl(formValidate.image)" alt="商品主图" />
            <div class="image-actions">
              <el-button size="mini" type="primary" @click="modalPicTap('main_image')" v-db-click>
                <i class="el-icon-edit"></i> 更换
              </el-button>
              <el-button size="mini" type="danger" @click="removeMainImage" v-db-click>
                <i class="el-icon-delete"></i> 删除
              </el-button>
            </div>
          </div>
          <div class="main-image-upload" v-else @click="modalPicTap('main_image')" v-db-click>
            <i class="el-icon-picture-outline" style="font-size: 48px; color: #ddd;"></i>
            <p style="margin-top: 10px; color: #999;">点击上传商品主图</p>
          </div>
        </div>
        <div class="tips-info">建议尺寸：800*800，商品主图将在商品列表和详情页显示</div>
        <el-input v-model="formValidate.image" style="display: none"></el-input>
      </el-form-item>
    </el-col>

    <el-col :span="24">
      <el-form-item label="商品轮播图：" prop="slider_image">
        <div class="acea-row">
          <div
            class="pictrue"
            v-for="(item, index) in formValidate.slider_image"
            :key="index"
            draggable="true"
            @dragstart="handleDragStart($event, item)"
            @dragover.prevent="handleDragOver($event, item)"
            @dragenter="handleDragEnter($event, item)"
            @dragend="handleDragEnd($event, item)"
          >
            <img v-lazy="formatImageUrl(item)" />
            <div class="slider-image-actions">
              <el-button size="mini" type="text" @click="setAsMainImage(item)" v-db-click title="设为主图">
                <i class="el-icon-star-on"></i>
              </el-button>
              <i class="el-icon-error btndel" v-db-click @click="handleRemove(index)" title="删除"></i>
            </div>
          </div>
          <div
            v-if="formValidate.slider_image.length < 10"
            class="upLoad acea-row row-center-wrapper"
            v-db-click
            @click="modalPicTap('duo')"
          >
            <i class="el-icon-picture-outline" style="font-size: 24px"></i>
          </div>
          <el-input v-model="formValidate.slider_image[0]" style="display: none"></el-input>
        </div>

        <div class="tips-info">建议尺寸：800*800，可拖拽改变图片顺序，用于商品详情页轮播展示，最多上传10张</div>

        <!-- <div class="tips">(最多10张<br />750*750)</div> -->
      </el-form-item>
    </el-col>
    <el-col :span="24" id="selectvideo">
      <el-form-item label="添加视频：" prop="video_link">
        <div v-if="!formValidate.video_link" class="videbox" @click="addVideo">
          <i class="el-icon-video-camera"></i>
        </div>
        <div class="box-video-style" v-if="formValidate.video_link">
          <video style="width: 100%; height: 100%" :src="formValidate.video_link" controls="controls">
            您的浏览器不支持 video 标签。
          </video>
          <div class="mark"></div>
          <i class="el-icon-delete iconv" v-db-click @click="delVideo"></i>
        </div>
        <Progress class="progress" :percent="progress" :stroke-width="5" v-if="upload.videoIng" />
        <div class="tips-info">建议时长：9～30秒，视频宽高比16:9</div>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="商品分类：" prop="cate_id">
        <el-cascader
          class="content_width"
          v-model="formValidate.cate_id"
          filterable
          size="small"
          :options="treeSelect"
          :props="{ multiple: true, checkStrictly: true, emitPath: false }"
          clearable
        ></el-cascader>
        <span class="addfont" v-db-click @click="addCate">新增分类</span>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="商品标签：">
        <div class="flex">
          <useLabel
            v-if="tileLabelList.length"
            :activeId.sync="formValidate.label_list"
            :listData="tileLabelList"
          ></useLabel>
          <el-button v-db-click @click="addGoodsTag">选择标签</el-button>
        </div>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="展示系统：" prop="display_systems">
        <el-checkbox-group v-model="formValidate.display_systems" class="display-systems-group">
          <el-checkbox
            v-for="system in displaySystems"
            :key="system.id"
            :label="system.id"
            class="display-system-item"
          >
            <i :class="system.icon" :style="{ color: system.color }"></i>
            <span class="system-name">{{ system.name }}</span>
          </el-checkbox>
        </el-checkbox-group>
        <div class="tips-info">选择商品在哪些系统中展示，至少选择一个系统</div>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="详情标签：" prop="detail_tag">
        <div class="detail-tag-container">
          <div class="tag-input-wrapper">
            <el-input
              v-model="tagInput"
              placeholder="输入标签内容，按回车添加"
              @keyup.enter.native="addTag"
              @blur="addTag"
              class="tag-input"
              maxlength="50"
              show-word-limit
            />
            <el-button type="primary" size="mini" @click="addTag" v-db-click>添加</el-button>
          </div>
          <div class="tags-display" v-if="detailTags.length > 0">
            <el-tag
              v-for="(tag, index) in detailTags"
              :key="index"
              closable
              @close="removeTag(index)"
              class="detail-tag-item"
            >
              {{ tag }}
            </el-tag>
          </div>
          <div class="tips-info">添加商品详情标签，用于商品详情页面展示，最多10个标签</div>
        </div>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="商品状态：">
        <el-radio-group v-model="formValidate.is_show">
          <el-radio :label="1" class="radio">上架</el-radio>
          <el-radio :label="0">下架</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-col>
  </el-row>
</template>

<style lang="scss" scoped>
.detail-tag-container {
  .tag-input-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;

    .tag-input {
      flex: 1;
    }
  }

  .tags-display {
    margin-bottom: 10px;

    .detail-tag-item {
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }

  .tips-info {
    color: #909399;
    font-size: 12px;
    line-height: 1.4;
  }
}

.display-systems-group {
  .display-system-item {
    margin-right: 20px;
    margin-bottom: 10px;

    .system-name {
      margin-left: 5px;
    }
  }
}
</style>

<script>
import useLabel from '@/components/goodsLabel/useLabel';
import { formatImageUrl } from "@/utils";
import { getDisplaySystems } from '@/api/product';

export default {
  name: 'BasicInfo',
  components: {
    useLabel,
  },
  props: {
    formValidate: {
      type: Object,
      required: true,
    },
    goodsType: {
      type: Array,
      required: true,
    },
    treeSelect: {
      type: Array,
      required: true,
    },
    tileLabelList: {
      type: Array,
      required: true,
    },
    upload: {
      type: Object,
      required: true,
    },
    isCai: {
      type: Number | String,
      required: true,
    },
  },
  data() {
    return {
      displaySystems: [], // 展示系统列表
      tagInput: '', // 标签输入框内容
      detailTags: [], // 详情标签数组
    };
  },
  mounted() {
    this.getDisplaySystemsList();
    this.initDetailTags();
  },
  watch: {
    // 监听详情标签数组变化，同步到formValidate
    detailTags: {
      handler(newTags) {
        this.formValidate.detail_tag = newTags.join(',');
      },
      deep: true
    },
    // 监听formValidate.detail_tag变化，同步到detailTags数组
    'formValidate.detail_tag': {
      handler(newValue) {
        if (newValue && typeof newValue === 'string') {
          this.detailTags = newValue.split(',').filter(tag => tag.trim() !== '');
        } else {
          this.detailTags = [];
        }
      },
      immediate: true
    },
    // 监听整个formValidate对象的变化，确保编辑场景下能正确回显
    formValidate: {
      handler(newVal) {
        if (newVal && newVal.detail_tag && typeof newVal.detail_tag === 'string') {
          const tags = newVal.detail_tag.split(',').filter(tag => tag.trim() !== '');
          // 只有当标签内容真的发生变化时才更新，避免无限循环
          if (JSON.stringify(tags) !== JSON.stringify(this.detailTags)) {
            this.detailTags = tags;
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    formatImageUrl,
    // 获取展示系统列表
    async getDisplaySystemsList() {
      try {
        const res = await getDisplaySystems();
        if (res.success) {
          this.displaySystems = res.data || [];
          // 只在新建商品且未设置展示系统时，默认选择所有系统
          // 编辑商品时不要覆盖已有的选择
          if ((!this.formValidate.display_systems || this.formValidate.display_systems.length === 0) &&
              (!this.formValidate.id || this.formValidate.id === 0)) {
            this.formValidate.display_systems = this.displaySystems.map(item => item.id);
          }
        }
      } catch (error) {
        console.error('获取展示系统列表失败:', error);
        this.$message.error('获取展示系统列表失败');
      }
    },
    virtualbtn(id, type) {
      this.$emit('virtualbtn', id, type);
    },
    handleDragStart(e, item) {
      this.$emit('handleDragStart', e, item);
    },
    handleDragOver(e, item) {
      this.$emit('handleDragOver', e, item);
    },
    handleDragEnter(e, item) {
      this.$emit('handleDragEnter', e, item);
    },
    handleDragEnd(e, item) {
      this.$emit('handleDragEnd', e, item);
    },
    handleRemove(index) {
      this.$emit('handleRemove', index);
    },
    modalPicTap(type) {
      this.$emit('modalPicTap', type);
    },
    addVideo() {
      this.$emit('addVideo');
    },
    delVideo() {
      this.$emit('delVideo');
    },
    addCate() {
      this.$emit('addCate');
    },
    addGoodsTag() {
      this.$emit('addGoodsTag');
    },
    // 删除主图
    removeMainImage() {
      this.$confirm('确定要删除商品主图吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.formValidate.image = '';
        this.$message.success('主图已删除');
      }).catch(() => {});
    },
    // 初始化详情标签
    initDetailTags() {
      if (this.formValidate.detail_tag && typeof this.formValidate.detail_tag === 'string') {
        this.detailTags = this.formValidate.detail_tag.split(',').filter(tag => tag.trim() !== '');
      }
    },
    // 添加标签
    addTag() {
      const tag = this.tagInput.trim();
      if (!tag) return;

      if (this.detailTags.length >= 10) {
        this.$message.warning('最多只能添加10个标签');
        return;
      }

      if (this.detailTags.includes(tag)) {
        this.$message.warning('标签已存在');
        return;
      }

      this.detailTags.push(tag);
      this.tagInput = '';
    },
    // 删除标签
    removeTag(index) {
      this.detailTags.splice(index, 1);
    },
    // 设置轮播图为主图
    setAsMainImage(imageUrl) {
      this.formValidate.image = imageUrl;
      this.$message.success('已设置为商品主图');
    },
  },
};
</script>
<style lang="scss" scoped>
@import '../productAdd.scss';

// 商品主图样式
.main-image-container {
  .main-image-preview {
    position: relative;
    display: inline-block;
    width: 150px;
    height: 150px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-actions {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      opacity: 0;
      transition: opacity 0.3s;

      .el-button {
        padding: 5px 8px;
        font-size: 12px;
      }
    }

    &:hover .image-actions {
      opacity: 1;
    }
  }

  .main-image-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
      border-color: #409eff;
    }

    p {
      margin: 0;
      font-size: 12px;
    }
  }

  // 轮播图操作按钮样式
  .slider-image-actions {
    position: absolute;
    top: 2px;
    right: 2px;
    display: flex;
    flex-direction: column;
    gap: 2px;

    .el-button {
      padding: 2px 4px;
      font-size: 12px;
      color: #409eff;
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 2px;
      min-height: auto;
      line-height: 1;

      &:hover {
        background: rgba(64, 158, 255, 0.1);
      }

      i {
        font-size: 14px;
      }
    }

    .btndel {
      color: #f56c6c;
      background: rgba(255, 255, 255, 0.9);
      padding: 2px;
      border-radius: 2px;
      font-size: 14px;
      cursor: pointer;

      &:hover {
        background: rgba(245, 108, 108, 0.1);
      }
    }
  }

  // 展示系统选择样式
  .display-systems-group {
    .display-system-item {
      display: inline-block;
      margin-right: 20px;
      margin-bottom: 10px;
      padding: 8px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background-color: #fff;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      &.is-checked {
        border-color: #409eff;
        background-color: #ecf5ff;
        color: #409eff;
      }

      .system-name {
        margin-left: 5px;
        font-size: 14px;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .tips-info {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    line-height: 1.4;
  }

  // 单价输入框样式
  .unit-text {
    margin-left: 8px;
    color: #606266;
    font-size: 14px;
  }
}
</style>
