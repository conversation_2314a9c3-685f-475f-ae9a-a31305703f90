<template>
  <div class="card-container">
    <el-card class="box-card">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="page-title">
          <i class="el-icon-tickets"></i>
          <span>卡券管理</span>
        </div>
        <div class="page-description">管理系统中的所有卡券信息，包括新增、编辑、删除和查看订单详情</div>
      </div>

      <!-- 搜索筛选区域 -->
      <div class="filter-container">
        <div class="filter-header">
          <i class="el-icon-search"></i>
          <span>筛选条件</span>
        </div>
        <el-form :inline="true" :model="listQuery" class="demo-form-inline">
          <el-form-item label="卡券编号">
            <el-input
              v-model="listQuery.no"
              placeholder="请输入卡券编号"
              clearable
              prefix-icon="el-icon-document"
            />
          </el-form-item>
          <el-form-item label="卡券代码">
            <el-input
              v-model="listQuery.code"
              placeholder="请输入卡券代码"
              clearable
              prefix-icon="el-icon-key"
            />
          </el-form-item>
          <el-form-item label="卡券名称">
            <el-input
              v-model="listQuery.name"
              placeholder="请输入卡券名称"
              clearable
              prefix-icon="el-icon-tickets"
            />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input
              v-model="listQuery.phone"
              placeholder="请输入手机号"
              clearable
              prefix-icon="el-icon-phone"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="listQuery.status" placeholder="请选择状态" clearable>
              <el-option label="未使用" :value="0">
                <span style="float: left">未使用</span>
                <span style="float: right; color: #67c23a; margin-right: 15px">●</span>
              </el-option>
              <el-option label="已使用" :value="1">
                <span style="float: left">已使用</span>
                <span style="float: right; color: #909399; margin-right: 15px">●</span>
              </el-option>
              <el-option label="已过期" :value="2">
                <span style="float: left">已过期</span>
                <span style="float: right; color: #e6a23c; margin-right: 15px">●</span>
              </el-option>
              <el-option label="已下线" :value="-1">
                <span style="float: left">已下线</span>
                <span style="float: right; color: #f56c6c; margin-right: 15px">●</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="listQuery.type" placeholder="请选择类型" clearable>
              <el-option label="折扣券" :value="0">
                <span style="float: left">折扣券</span>
                <span style="float: right; color: #67c23a; margin-right: 15px">🎫</span>
              </el-option>
              <el-option label="满减券" :value="1">
                <span style="float: left">满减券</span>
                <span style="float: right; color: #e6a23c; margin-right: 15px">🎟️</span>
              </el-option>
              <el-option label="代金券" :value="2">
                <span style="float: left">代金券</span>
                <span style="float: right; color: #409eff; margin-right: 15px">💰</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="有效期">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              @change="handleDateRangeChange"
              prefix-icon="el-icon-date"
            />
          </el-form-item>
          <el-form-item class="filter-buttons">
            <el-button type="primary" @click="handleFilter" icon="el-icon-search">查询</el-button>
            <el-button @click="resetQuery" icon="el-icon-refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="button-container">
        <div class="button-group-left">
          <el-button type="primary" @click="handleCreate" icon="el-icon-plus">
            新增卡券
          </el-button>
          <el-button type="success" @click="handleImport" icon="el-icon-upload2">
            批量导入
          </el-button>
          <el-button type="info" @click="handleBatchOnline" icon="el-icon-top" :disabled="!hasOfflineCards" :loading="batchOnlineLoading">
            批量上架
          </el-button>
          <el-button type="warning" @click="handleExport" :loading="exportLoading" icon="el-icon-download">
            导出数据
          </el-button>
        </div>
        <div class="table-info">
          <span>共 {{ total }} 条记录</span>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="listLoading"
          :data="list"
          element-loading-text="正在加载数据..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
          border
          fit
          highlight-current-row
          stripe
          style="width: 100%"
          :header-cell-style="{ background: '#f8f9fa', color: '#303133', fontWeight: '600' }"
        >
          <el-table-column align="center" label="ID" width="80" fixed="left">
            <template slot-scope="scope">
              <div class="id-cell">
                <span class="id-badge">{{ scope.row.id }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="卡券信息" min-width="300" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="card-info">
                <div class="card-name">
                  <i class="el-icon-tickets"></i>
                  <span>{{ scope.row.name }}</span>
                </div>
                <div class="card-details">
                  <span class="card-no">编号: {{ scope.row.no }}</span>
                  <span class="card-code">代码: {{ scope.row.code }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="联系方式" min-width="120">
            <template slot-scope="scope">
              <div class="phone-cell">
                <i class="el-icon-phone" v-if="scope.row.phone"></i>
                <span>{{ scope.row.phone || '未绑定' }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="类型" min-width="110">
            <template slot-scope="scope">
              <div class="type-cell">
                <el-tag :type="getCardTypeTag(scope.row.type)" effect="dark">
                  <span class="type-icon">{{ getCardTypeIcon(scope.row.type) }}</span>
                  {{ getCardTypeName(scope.row.type) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="状态" min-width="110">
            <template slot-scope="scope">
              <div class="status-cell">
                <el-tag :type="getCardStatusTag(scope.row.status)" effect="dark">
                  <span class="status-dot" :class="'status-' + scope.row.status"></span>
                  {{ getCardStatusName(scope.row.status) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="有效期" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="date-range">
                <div class="date-item">
                  <i class="el-icon-time"></i>
                  <span>{{ formatDate(scope.row.startDate) }}</span>
                </div>
                <div class="date-separator">至</div>
                <div class="date-item">
                  <i class="el-icon-time"></i>
                  <span>{{ formatDate(scope.row.endDate) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="关联商品" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="goods-info">
                <div v-if="scope.row.goodsNames && scope.row.goodsNames.length > 0" class="goods-list">
                  <el-tag
                    v-for="(goodsName, index) in scope.row.goodsNames.slice(0, 2)"
                    :key="index"
                    size="mini"
                    type="info"
                    class="goods-tag"
                  >
                    {{ goodsName }}
                  </el-tag>
                  <el-tag
                    v-if="scope.row.goodsNames.length > 2"
                    size="mini"
                    type="warning"
                    class="goods-tag"
                  >
                    +{{ scope.row.goodsNames.length - 2 }}
                  </el-tag>
                </div>
                <div v-else class="no-goods">
                  <i class="el-icon-box"></i>
                  <span>未关联商品</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="创建时间" min-width="160">
            <template slot-scope="scope">
              <div class="create-time">
                <i class="el-icon-calendar"></i>
                <span>{{ formatDateTime(scope.row.createDate) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="操作" width="250" fixed="right">
            <template slot-scope="scope">
              <div class="button-group">
                <el-tooltip content="编辑卡券" placement="top">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleUpdate(scope.row)"
                    icon="el-icon-edit"
                  >
                    编辑
                  </el-button>
                </el-tooltip>

                <el-tooltip content="关联商品" placement="top">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleLinkGoods(scope.row)"
                    icon="el-icon-link"
                    class="link-btn"
                  >
                    关联商品
                  </el-button>
                </el-tooltip>

                <el-tooltip content="上架卡券" placement="top" v-if="scope.row.status === -1">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleOnline(scope.row)"
                    icon="el-icon-top"
                    class="online-btn"
                  >
                    上架
                  </el-button>
                </el-tooltip>

                <el-tooltip content="删除卡券" placement="top">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleDelete(scope.row)"
                    :disabled="scope.row.status === 1"
                    icon="el-icon-delete"
                    class="delete-btn"
                  >
                    删除
                  </el-button>
                </el-tooltip>

                <el-tooltip content="查看关联订单" placement="top" v-if="scope.row.status === 1">
                  <el-button
                    size="small"
                    type="text"
                    @click="viewOrderDetails(scope.row)"
                    icon="el-icon-view"
                    class="view-btn"
                  >
                    查看订单
                  </el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <div v-if="!listLoading && list.length === 0" class="empty-state">
          <i class="el-icon-document-remove"></i>
          <p>暂无卡券数据</p>
          <el-button type="primary" @click="handleCreate">立即创建</el-button>
        </div>
      </div>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :current-page.sync="listQuery.page"
          :page-size.sync="listQuery.limit"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑卡券弹窗 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '新增卡券' : '编辑卡券'"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <el-form
        ref="cardFormRef"
        :model="cardForm"
        :rules="rules"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="卡券编号" prop="no">
          <el-input v-model="cardForm.no" placeholder="请输入卡券编号" />
        </el-form-item>
        <el-form-item label="卡券代码" prop="code">
          <el-input v-model="cardForm.code" placeholder="请输入卡券代码" />
        </el-form-item>
        <el-form-item label="卡券名称" prop="name">
          <el-input v-model="cardForm.name" placeholder="请输入卡券名称" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="cardForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="卡券类型" prop="type">
          <el-select v-model="cardForm.type" placeholder="请选择卡券类型">
            <el-option label="折扣券" :value="0" />
            <el-option label="满减券" :value="1" />
            <el-option label="代金券" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="有效期" prop="dateRange">
          <el-date-picker
            v-model="cardForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="cardForm.status" placeholder="请选择状态">
            <el-option label="未使用" :value="0" />
            <el-option label="已下线" :value="-1" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 商品选择对话框 -->
    <el-dialog
      title="关联商品"
      :visible.sync="goodsDialogVisible"
      width="80%"
      :close-on-click-modal="false"
      class="goods-dialog"
    >
      <div class="goods-dialog-content">
        <!-- 搜索区域 -->
        <div class="goods-search">
          <el-form :inline="true" :model="goodsQuery" class="demo-form-inline">
            <el-form-item label="商品名称">
              <el-input
                v-model="goodsQuery.name"
                placeholder="请输入商品名称"
                clearable
                @keyup.enter.native="searchGoods"
              />
            </el-form-item>
            <el-form-item label="商品分类">
              <el-select v-model="goodsQuery.categoryId" placeholder="请选择分类" clearable>
                <el-option
                  v-for="category in categoryList"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchGoods" icon="el-icon-search">搜索</el-button>
              <el-button @click="resetGoodsQuery" icon="el-icon-refresh">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 已选商品 -->
        <div class="selected-goods" v-if="selectedGoods.length > 0">
          <div class="selected-header">
            <span>已选商品 ({{ selectedGoods.length }})</span>
            <el-button type="text" @click="clearSelectedGoods" icon="el-icon-delete">清空</el-button>
          </div>
          <div class="selected-list">
            <el-tag
              v-for="goods in selectedGoods"
              :key="goods.id"
              closable
              @close="removeSelectedGoods(goods)"
              class="selected-tag"
            >
              {{ goods.name }}
            </el-tag>
          </div>
        </div>

        <!-- 商品列表 -->
        <el-table
          ref="goodsTable"
          :data="goodsList"
          v-loading="goodsLoading"
          @selection-change="handleGoodsSelectionChange"
          max-height="400"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="商品ID" prop="id" width="80" />
          <el-table-column label="商品图片" width="100">
            <template slot-scope="scope">
              <div class="goods-image">
                <img :src="formatImageUrl(scope.row.primaryPicUrl || scope.row.listPicUrl)" alt="商品图片" />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" prop="name" min-width="200" show-overflow-tooltip />
          <el-table-column label="商品分类" prop="categoryName" width="120" />
          <el-table-column label="零售价" width="100">
            <template slot-scope="scope">
              <span class="price">¥{{ scope.row.retailPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.isOnSale ? 'success' : 'danger'" size="mini">
                {{ scope.row.isOnSale ? '上架' : '下架' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="goods-pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next"
            :total="goodsTotal"
            :current-page.sync="goodsQuery.page"
            :page-size.sync="goodsQuery.limit"
            @size-change="handleGoodsSizeChange"
            @current-change="handleGoodsCurrentChange"
          />
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="goodsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmLinkGoods" :loading="linkGoodsLoading">
          确定关联 ({{ selectedGoods.length }})
        </el-button>
      </div>
    </el-dialog>

    <!-- 订单详情组件 -->
    <order-details
      ref="orderDetails"
      :orderDatalist="orderDatalist"
      :orderId="orderId"
      :modals.sync="orderDetailsVisible"
    ></order-details>
  </div>
</template>

<script>
import { formatDate, formatDateTime } from '@/utils/date'
import { commonRequestGet, commonRequestPost } from '@/api/common'
import orderDetails from '@/pages/order/orderList/handle/orderDetails'
import { formatImageUrl } from '@/utils';

export default {
  name: 'CardIndex',
  components: {
    orderDetails
  },
  data() {
    return {
      // 数据列表
      list: [],
      total: 0,
      listLoading: false,
      exportLoading: false,
      dialogFormVisible: false,
      dialogStatus: '',
      dateRange: [],
      batchOnlineLoading: false,

      // 订单详情数据
      orderDetailsVisible: false,
      orderDatalist: {
        orderInfo: {},
        orderGoods: [],
        handleOption: {}
      },
      orderId: null,

      // 查询参数
      listQuery: {
        page: 1,
        limit: 10,
        no: '',
        code: '',
        name: '',
        phone: '',
        status: '',
        type: '',
        startDateBegin: '',
        startDateEnd: '',
        endDateBegin: '',
        endDateEnd: ''
      },

      // 表单数据
      cardForm: {
        id: undefined,
        no: '',
        code: '',
        name: '',
        phone: '',
        type: 0,
        status: 0,
        dateRange: [],
        startDate: '',
        endDate: '',
        goodsId: ''
      },

      // 表单验证规则
      rules: {
        no: [{ required: true, message: '请输入卡券编号', trigger: 'blur' }],
        code: [{ required: true, message: '请输入卡券代码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入卡券名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择卡券类型', trigger: 'change' }],
        dateRange: [{ required: true, message: '请选择有效期', trigger: 'change' }]
      },

      // 商品选择相关数据
      goodsDialogVisible: false,
      goodsLoading: false,
      linkGoodsLoading: false,
      currentCard: null, // 当前操作的卡券
      goodsList: [],
      goodsTotal: 0,
      selectedGoods: [],
      categoryList: [],
      goodsQuery: {
        page: 1,
        limit: 10,
        name: '',
        categoryId: ''
      }
    }
  },
  computed: {
    // 检查是否有已下线的卡券
    hasOfflineCards() {
      return this.list.some(item => item.status === -1)
    }
  },
  created() {
    this.getList()
  },
  watch: {
    orderDetailsVisible(val) {
      console.log('orderDetailsVisible changed:', val);
      if (val) {
        console.log('订单详情抽屉已打开，数据:', this.orderDatalist);
      } else {
        // 当抽屉关闭时，重置数据
        this.orderDatalist = {
          orderInfo: {},
          orderGoods: [],
          handleOption: {}
        };
        this.orderId = null;
      }
    }
  },
  methods: {
    formatImageUrl,
    // 获取卡券列表
    getList() {
      this.listLoading = true
      commonRequestGet('card/list', this.listQuery)
        .then(res => {
          if (res.success) {
            this.list = res.data.records
            this.total = res.data.total
          } else {
            this.$message.error(res.msg || '获取卡券列表失败')
          }
          this.listLoading = false
        })
        .catch(error => {
          console.error('获取卡券列表失败:', error)
          this.listLoading = false
          this.$message.error('获取卡券列表失败')
        })
    },

    // 处理日期范围变化
    handleDateRangeChange(val) {
      if (val) {
        this.listQuery.startDateBegin = val[0]
        this.listQuery.startDateEnd = val[1]
      } else {
        this.listQuery.startDateBegin = ''
        this.listQuery.startDateEnd = ''
      }
    },

    // 查询
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    // 重置查询条件
    resetQuery() {
      Object.keys(this.listQuery).forEach(key => {
        if (key !== 'page' && key !== 'limit') {
          this.listQuery[key] = ''
        }
      })
      this.dateRange = []
      this.handleFilter()
    },

    // 处理每页数量变化
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },

    // 新增卡券
    handleCreate() {
      this.resetCardForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },

    // 编辑卡券
    handleUpdate(row) {
      this.resetCardForm()
      this.dialogStatus = 'update'

      this.cardForm.id = row.id
      this.cardForm.no = row.no
      this.cardForm.code = row.code
      this.cardForm.name = row.name
      this.cardForm.phone = row.phone
      this.cardForm.type = row.type
      this.cardForm.status = row.status
      this.cardForm.goodsId = row.goodsId

      if (row.startDate && row.endDate) {
        this.cardForm.dateRange = [formatDate(row.startDate), formatDate(row.endDate)]
      }

      this.dialogFormVisible = true
    },

    // 删除卡券
    handleDelete(row) {
      this.$confirm('确定要删除该卡券吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          commonRequestPost(`card/${row.id}/delete`, {})
            .then(res => {
              if (res.success) {
                this.$message.success('删除成功')
                this.getList()
              } else {
                this.$message.error(res.msg || '删除失败')
              }
            })
            .catch(error => {
              console.error('删除失败:', error)
              this.$message.error('删除失败')
            })
        })
        .catch(() => {})
    },

    // 提交表单
    submitForm() {
      this.$refs.cardFormRef.validate(valid => {
        if (valid) {
          // 处理日期
          if (this.cardForm.dateRange && this.cardForm.dateRange.length === 2) {
            this.cardForm.startDate = this.cardForm.dateRange[0]
            this.cardForm.endDate = this.cardForm.dateRange[1]
          }

          const url = this.dialogStatus === 'create' ? 'card/save' : 'card/update'
          const successMsg = this.dialogStatus === 'create' ? '新增成功' : '更新成功'

          commonRequestPost(url, this.cardForm)
            .then(res => {
              if (res.success) {
                this.dialogFormVisible = false
                this.$message.success(successMsg)
                this.getList()
              } else {
                this.$message.error(res.msg || '操作失败')
              }
            })
            .catch(error => {
              console.error('操作失败:', error)
              this.$message.error('操作失败')
            })
        }
      })
    },

    // 重置表单
    resetCardForm() {
      if (this.$refs.cardFormRef) {
        this.$refs.cardFormRef.resetFields()
      }

      this.cardForm.id = undefined
      this.cardForm.no = ''
      this.cardForm.code = ''
      this.cardForm.name = ''
      this.cardForm.phone = ''
      this.cardForm.type = 0
      this.cardForm.status = 0
      this.cardForm.dateRange = []
      this.cardForm.startDate = ''
      this.cardForm.endDate = ''
      this.cardForm.goodsId = ''
    },

    // 批量导入
    handleImport() {
      this.$message.info('批量导入功能开发中...')
    },

    // 导出数据
    handleExport() {
      this.$confirm('确定要导出卡券数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })
        .then(() => {
          this.exportLoading = true

          // 构建导出参数
          const exportParams = {
            ...this.listQuery,
            exportType: 'csv'
          }

          // 使用GET请求下载文件
          const params = new URLSearchParams(exportParams)
          const url = `/adminapi/card/export?${params.toString()}`

          // 创建下载链接
          const link = document.createElement('a')
          link.href = url
          link.download = `卡券数据_${new Date().toISOString().slice(0, 10)}.csv`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          this.$message.success('导出成功')
          this.exportLoading = false
        })
        .catch(() => {
          this.exportLoading = false
        })
    },

    // 格式化日期
    formatDate(date) {
      return formatDate(date)
    },

    // 格式化日期时间
    formatDateTime(date) {
      return formatDateTime(date)
    },

    // 获取卡券类型名称
    getCardTypeName(type) {
      const typeMap = {
        0: '折扣券',
        1: '满减券',
        2: '代金券'
      }
      return typeMap[type] || '未知类型'
    },

    // 获取卡券类型标签样式
    getCardTypeTag(type) {
      const typeMap = {
        0: 'success',
        1: 'warning',
        2: 'primary'
      }
      return typeMap[type] || 'info'
    },

    // 获取卡券类型图标
    getCardTypeIcon(type) {
      const iconMap = {
        0: '🎫',
        1: '🎟️',
        2: '💰'
      }
      return iconMap[type] || '🎪'
    },

    // 获取卡券状态名称
    getCardStatusName(status) {
      const statusMap = {
        '-1': '已下线',
        0: '未使用',
        1: '已使用',
        2: '已过期'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取卡券状态标签样式
    getCardStatusTag(status) {
      const statusMap = {
        '-1': 'danger',
        0: 'success',
        1: 'info',
        2: 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 单个卡券上架
    handleOnline(row) {
      this.$confirm(`确定要上架卡券"${row.name}"吗？上架后状态将变为未使用。`, '确认上架', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
        icon: 'el-icon-top'
      })
        .then(() => {
          const updateData = {
            id: row.id,
            status: 0 // 设置为未使用状态
          }

          commonRequestPost('card/updateStatus', updateData)
            .then(res => {
              if (res.success) {
                this.$message.success('上架成功')
                this.getList() // 刷新列表
              } else {
                this.$message.error(res.msg || '上架失败')
              }
            })
            .catch(error => {
              console.error('上架失败:', error)
              this.$message.error('上架失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消上架')
        })
    },

    // 批量上架已下线的卡券
    handleBatchOnline() {
      const offlineCards = this.list.filter(item => item.status === -1)

      if (offlineCards.length === 0) {
        this.$message.warning('没有已下线的卡券需要上架')
        return
      }

      this.$confirm(`确定要批量上架 ${offlineCards.length} 张已下线的卡券吗？`, '确认批量上架', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
        icon: 'el-icon-top'
      })
        .then(() => {
          this.batchOnlineLoading = true

          const cardIds = offlineCards.map(card => card.id)
          const updateData = {
            ids: cardIds,
            status: 0 // 设置为未使用状态
          }

          commonRequestPost('card/batchUpdateStatus', updateData)
            .then(res => {
              if (res.success) {
                this.$message.success(`成功上架 ${offlineCards.length} 张卡券`)
                this.getList() // 刷新列表
              } else {
                this.$message.error(res.msg || '批量上架失败')
              }
              this.batchOnlineLoading = false
            })
            .catch(error => {
              console.error('批量上架失败:', error)
              this.$message.error('批量上架失败')
              this.batchOnlineLoading = false
            })
        })
        .catch(() => {
          this.$message.info('已取消批量上架')
        })
    },

    // 查看订单详情
    viewOrderDetails(row) {
      this.orderId = row.orderId || row.id;

      // 先重置订单数据
      this.orderDatalist = {
        orderInfo: {},
        orderGoods: [],
        handleOption: {}
      };

      // 显示加载状态
      this.$message.info('正在加载订单详情...');

      // 获取卡券关联的订单详情
      commonRequestGet(`card/${row.id}/order`, {})
        .then(res => {
          if (res.success && res.data) {
            let orderInfo, orderGoods;

            if (res.data.orderInfo) {
              orderInfo = res.data.orderInfo;
              orderGoods = res.data.orderGoods || [];
            }  else {
              orderInfo = {};
              orderGoods = [];
              console.error('未知的API响应格式:', res.data);
            }

            // 设置订单数据
            this.orderDatalist = {
              orderInfo: orderInfo,
              orderGoods: orderGoods,
              handleOption: res.data.handleOption || {}
            };

            console.log('设置订单数据:', this.orderDatalist);

            // 数据加载完成后再显示抽屉
            this.$nextTick(() => {
              // 直接设置组件的modals属性
              if (this.$refs.orderDetails) {
                this.$refs.orderDetails.modals = true;
                this.orderDetailsVisible = true;
              } else {
                this.orderDetailsVisible = true;
              }
            });
          } else {
            this.$message.error(res.msg || '获取订单详情失败');
          }
        })
        .catch(error => {
          console.error('获取订单详情失败:', error);
          this.$message.error('获取订单详情失败');
        });
    },

    // ==================== 商品关联相关方法 ====================

    // 打开关联商品对话框
    handleLinkGoods(row) {
      this.currentCard = row;
      this.goodsDialogVisible = true;
      this.loadCategoryList();
      this.loadGoodsList();
      this.loadCurrentCardGoods(row);
    },

    // 加载商品分类列表
    loadCategoryList() {
      commonRequestGet('product/category', { limit: 1000 })
        .then(res => {
          if (res.success) {
            this.categoryList = res.data.records || [];
          }
        })
        .catch(error => {
          console.error('获取商品分类失败:', error);
        });
    },

    // 加载商品列表
    loadGoodsList() {
      this.goodsLoading = true;
      const params = {
        page: this.goodsQuery.page,
        limit: this.goodsQuery.limit,
        store_name: this.goodsQuery.name,
        cate_id: this.goodsQuery.categoryId
      };

      commonRequestGet('product/product', params)
        .then(res => {
          if (res.success) {
            this.goodsList = res.data.records || [];
            this.goodsTotal = res.data.total || 0;

            // 设置已选中的商品
            this.$nextTick(() => {
              this.setSelectedGoodsInTable();
            });
          } else {
            this.$message.error(res.msg || '获取商品列表失败');
          }
          this.goodsLoading = false;
        })
        .catch(error => {
          console.error('获取商品列表失败:', error);
          this.goodsLoading = false;
          this.$message.error('获取商品列表失败');
        });
    },

    // 加载当前卡券已关联的商品
    loadCurrentCardGoods(card) {
      if (card.goodsId) {
        const goodsIds = card.goodsId.split(',').filter(id => id.trim());
        if (goodsIds.length > 0) {
          // 根据商品ID获取商品信息
          commonRequestPost('card/getGoodsByIds', { ids: goodsIds })
            .then(res => {
              if (res.success) {
                this.selectedGoods = res.data || [];
              }
            })
            .catch(error => {
              console.error('获取已关联商品失败:', error);
            });
        } else {
          this.selectedGoods = [];
        }
      } else {
        this.selectedGoods = [];
      }
    },

    // 在表格中设置已选中的商品
    setSelectedGoodsInTable() {
      if (this.$refs.goodsTable && this.selectedGoods.length > 0) {
        this.goodsList.forEach(goods => {
          const isSelected = this.selectedGoods.some(selected => selected.id === goods.id);
          if (isSelected) {
            this.$refs.goodsTable.toggleRowSelection(goods, true);
          }
        });
      }
    },

    // 搜索商品
    searchGoods() {
      this.goodsQuery.page = 1;
      this.loadGoodsList();
    },

    // 重置商品查询条件
    resetGoodsQuery() {
      this.goodsQuery = {
        page: 1,
        limit: 10,
        name: '',
        categoryId: ''
      };
      this.loadGoodsList();
    },

    // 处理商品选择变化
    handleGoodsSelectionChange(selection) {
      // 合并当前页选择的商品和之前选择的商品
      const currentPageIds = this.goodsList.map(goods => goods.id);
      const previousSelected = this.selectedGoods.filter(goods => !currentPageIds.includes(goods.id));
      this.selectedGoods = [...previousSelected, ...selection];
    },

    // 移除已选商品
    removeSelectedGoods(goods) {
      const index = this.selectedGoods.findIndex(item => item.id === goods.id);
      if (index > -1) {
        this.selectedGoods.splice(index, 1);
      }

      // 如果商品在当前页面，取消选中
      const currentPageGoods = this.goodsList.find(item => item.id === goods.id);
      if (currentPageGoods && this.$refs.goodsTable) {
        this.$refs.goodsTable.toggleRowSelection(currentPageGoods, false);
      }
    },

    // 清空已选商品
    clearSelectedGoods() {
      this.selectedGoods = [];
      if (this.$refs.goodsTable) {
        this.$refs.goodsTable.clearSelection();
      }
    },

    // 商品分页大小变化
    handleGoodsSizeChange(val) {
      this.goodsQuery.limit = val;
      this.goodsQuery.page = 1;
      this.loadGoodsList();
    },

    // 商品分页页码变化
    handleGoodsCurrentChange(val) {
      this.goodsQuery.page = val;
      this.loadGoodsList();
    },

    // 确认关联商品
    confirmLinkGoods() {
      if (!this.currentCard) {
        this.$message.error('请先选择要关联的卡券');
        return;
      }

      const goodsIds = this.selectedGoods.map(goods => goods.id).join(',');

      this.linkGoodsLoading = true;
      commonRequestPost('card/linkGoods', {
        cardId: this.currentCard.id,
        goodsIds: goodsIds
      })
        .then(res => {
          if (res.success) {
            this.$message.success('关联商品成功');
            this.goodsDialogVisible = false;
            this.getList(); // 刷新列表
          } else {
            this.$message.error(res.msg || '关联商品失败');
          }
          this.linkGoodsLoading = false;
        })
        .catch(error => {
          console.error('关联商品失败:', error);
          this.linkGoodsLoading = false;
          this.$message.error('关联商品失败');
        });
    }
  }
}
</script>

<style scoped>
.card-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
  width: 100%;
  box-sizing: border-box;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
  width: 100%;
}

.box-card ::v-deep .el-card__body {
  padding: 24px;
  width: 100%;
  box-sizing: border-box;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 0;
  border-bottom: 2px solid #f0f2f5;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.page-title i {
  font-size: 28px;
  color: #409eff;
  margin-right: 12px;
}

.page-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.filter-container {
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.filter-header i {
  font-size: 18px;
  color: #409eff;
  margin-right: 8px;
}

.filter-container .demo-form-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-container .el-form-item {
  margin-bottom: 16px;
  margin-right: 0;
}

.filter-container .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.filter-container .el-input,
.filter-container .el-select {
  width: 180px;
}

.filter-container .el-date-editor {
  width: 240px;
}

.button-container {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
}

.button-group-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.button-container .table-info {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.button-container .el-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 10px 20px;
}

.button-container .el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  border: none;
}

.button-container .el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
}

.button-container .el-button--info {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
  border: none;
}

.button-container .el-button--info:disabled {
  background: #f5f7fa;
  color: #c0c4cc;
  border: 1px solid #e4e7ed;
}

.button-container .el-button--warning {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%);
  border: none;
}

.el-table {
  border-radius: 6px;
  overflow: hidden;
}

.el-table ::v-deep .el-table__header-wrapper {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.el-table ::v-deep .el-table__header th {
  background: transparent;
  color: #303133;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
}

.el-table ::v-deep .el-table__row:hover > td {
  background-color: #f5f7fa !important;
}

.el-table ::v-deep .el-table__body tr:nth-child(even) {
  background-color: #fafbfc;
}

.el-table ::v-deep .el-table__body td,
.el-table ::v-deep .el-table__header th {
  vertical-align: middle;
}

.el-table ::v-deep .el-table__body td .cell {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

/* 表格内容样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.table-container .el-table {
  width: 100% !important;
}

.table-container .el-table__body-wrapper {
  overflow-x: auto;
}

/* 确保表格列能够自适应宽度 */
.el-table ::v-deep .el-table__body,
.el-table ::v-deep .el-table__header {
  width: 100% !important;
}

.el-table ::v-deep .el-table__fixed-right {
  right: 0 !important;
}

.el-table ::v-deep .el-table__fixed-left {
  left: 0 !important;
}

.id-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 60px;
}

.id-badge {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.card-info {
  text-align: left;
  padding: 8px 0;
}

.card-name {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.card-name i {
  color: #409eff;
  margin-right: 6px;
}

.card-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.card-no, .card-code {
  font-size: 12px;
  color: #909399;
}

.phone-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 100%;
  min-height: 60px;
}

.phone-cell i {
  color: #67c23a;
}

.type-cell, .status-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 60px;
}

.type-icon {
  margin-right: 4px;
  display: inline-flex;
  align-items: center;
}

.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 6px;
  vertical-align: middle;
}

.status-dot.status--1 {
  background-color: #f56c6c;
}

.status-dot.status-0 {
  background-color: #67c23a;
}

.status-dot.status-1 {
  background-color: #909399;
}

.status-dot.status-2 {
  background-color: #e6a23c;
}

.date-range {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 100%;
  min-height: 60px;
}

.date-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.date-item i {
  color: #409eff;
}

.date-separator {
  color: #909399;
  font-size: 12px;
}

.create-time {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  height: 100%;
  min-height: 60px;
}

.create-time i {
  color: #409eff;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
}

.pagination-container ::v-deep .el-pagination {
  font-weight: 500;
}

.pagination-container ::v-deep .el-pagination .el-pager li {
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-container ::v-deep .el-pagination .el-pager li.active {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  margin-top: 24px;
}

.dialog-footer .el-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 10px 24px;
}

.button-group {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.button-group .el-button {
  margin: 0;
  border-radius: 4px;
  font-size: 12px;
  padding: 6px 12px;
  font-weight: 500;
}

.button-group .el-button--text {
  color: #409eff;
  border: 1px solid #c6e2ff;
  background: #ecf5ff;
}

.button-group .el-button--text:hover {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.button-group .el-button--text:disabled {
  color: #c0c4cc;
  background: #f5f7fa;
  border-color: #e4e7ed;
}

.button-group .delete-btn {
  color: #f56c6c;
  border-color: #fbc4c4;
  background: #fef0f0;
}

.button-group .delete-btn:hover:not(:disabled) {
  background: #f56c6c;
  color: white;
  border-color: #f56c6c;
}

.button-group .view-btn {
  color: #67c23a;
  border-color: #c2e7b0;
  background: #f0f9ff;
}

.button-group .view-btn:hover {
  background: #67c23a;
  color: white;
  border-color: #67c23a;
}

.button-group .online-btn {
  color: #409eff;
  border-color: #c6e2ff;
  background: #ecf5ff;
}

.button-group .online-btn:hover {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

/* 标签样式优化 */
.el-tag {
  border-radius: 12px;
  font-weight: 500;
  font-size: 12px;
  padding: 6px 12px;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  vertical-align: middle;
}

.el-tag--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
}

.el-tag--warning {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%);
  color: white;
}

.el-tag--primary {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
}

.el-tag--info {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
  color: white;
}

.el-tag--danger {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
  color: white;
}

/* 对话框样式优化 */
.el-dialog {
  border-radius: 8px;
}

.el-dialog ::v-deep .el-dialog__header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px 8px 0 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
}

.el-dialog ::v-deep .el-dialog__title {
  font-weight: 600;
  color: #303133;
}

.el-dialog ::v-deep .el-dialog__body {
  padding: 24px;
}

/* 表单样式优化 */
.el-form-item ::v-deep .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-input ::v-deep .el-input__inner,
.el-select ::v-deep .el-input__inner {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.el-input ::v-deep .el-input__inner:focus,
.el-select ::v-deep .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-container {
    padding: 16px;
  }

  .filter-container .demo-form-inline {
    flex-direction: column;
  }

  .filter-container .el-input,
  .filter-container .el-select,
  .filter-container .el-date-editor {
    width: 100%;
  }

  .button-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .button-group-left {
    flex-direction: column;
    width: 100%;
  }

  .button-group-left .el-button {
    width: 100%;
  }

  .table-info {
    align-self: flex-start;
  }

  .pagination-container {
    justify-content: center;
  }
}

/* 关联商品相关样式 */
.goods-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.goods-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.goods-tag {
  margin: 2px;
}

.no-goods {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 12px;
}

.no-goods i {
  margin-right: 4px;
}

.link-btn {
  color: #e6a23c !important;
}

.link-btn:hover {
  color: #ebb563 !important;
}

/* 商品选择对话框样式 */
.goods-dialog ::v-deep .el-dialog {
  border-radius: 8px;
}

.goods-dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.goods-search {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.selected-goods {
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 6px;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #303133;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag {
  margin: 0;
}

.goods-image {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  overflow: hidden;
  background: #f5f7fa;
}

.goods-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.price {
  color: #f56c6c;
  font-weight: 500;
}

.goods-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
