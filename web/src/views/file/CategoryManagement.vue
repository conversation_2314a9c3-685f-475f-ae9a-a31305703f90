<template>
  <div class="category-management">
    <div class="page-header">
      <h2>分类管理</h2>
      <p>管理文件分类，支持拖拽排序、批量操作等功能</p>
    </div>

    <div class="content-container">
      <!-- 左侧分类树 -->
      <div class="category-tree-panel">
        <div class="panel-header">
          <h3>分类树</h3>
          <div class="header-actions">
            <el-button type="primary" size="small" icon="el-icon-plus" @click="showCreateDialog">
              新增分类
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              icon="el-icon-delete" 
              :disabled="selectedCategories.length === 0"
              @click="batchDelete"
            >
              批量删除
            </el-button>
          </div>
        </div>

        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索分类"
            size="small"
            @input="handleSearch"
            clearable
          >
            <i slot="suffix" class="el-icon-search"></i>
          </el-input>
        </div>

        <div class="tree-container">
          <el-tree
            ref="categoryTree"
            :data="categoryTreeData"
            :props="treeProps"
            node-key="id"
            :default-expand-all="true"
            :expand-on-click-node="false"
            show-checkbox
            draggable
            :allow-drop="allowDrop"
            :allow-drag="allowDrag"
            @node-drop="handleNodeDrop"
            @check-change="handleCheckChange"
            @node-click="handleNodeClick"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span class="node-content">
                <i :class="getNodeIcon(data)" class="node-icon"></i>
                <span class="node-title">{{ data.title }}</span>
                <span class="node-count" v-if="data.fileCount > 0">({{ data.fileCount }})</span>
              </span>
              <span class="node-actions">
                <el-dropdown @command="(command) => handleNodeAction(data, command)" trigger="click">
                  <el-button type="text" size="mini" icon="el-icon-more"></el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="add">新增子分类</el-dropdown-item>
                    <el-dropdown-item command="edit">编辑分类</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除分类</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </span>
          </el-tree>
        </div>
      </div>

      <!-- 右侧详情面板 -->
      <div class="detail-panel">
        <div class="panel-header">
          <h3>分类详情</h3>
        </div>

        <div class="detail-content" v-if="selectedCategory">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="分类名称">
              {{ selectedCategory.title }}
            </el-descriptions-item>
            <el-descriptions-item label="父级分类">
              {{ selectedCategory.parentName || '根分类' }}
            </el-descriptions-item>
            <el-descriptions-item label="分类层级">
              第 {{ selectedCategory.level + 1 }} 级
            </el-descriptions-item>
            <el-descriptions-item label="排序值">
              {{ selectedCategory.sortOrder }}
            </el-descriptions-item>
            <el-descriptions-item label="文件数量">
              {{ selectedCategory.fileCount || 0 }} 个
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ selectedCategory.createTime }}
            </el-descriptions-item>
            <el-descriptions-item label="分类描述" :span="2">
              {{ selectedCategory.description || '暂无描述' }}
            </el-descriptions-item>
          </el-descriptions>

          <div class="detail-actions">
            <el-button type="primary" @click="editCategory(selectedCategory)">
              编辑分类
            </el-button>
            <el-button @click="addSubCategory(selectedCategory)">
              新增子分类
            </el-button>
            <el-button type="danger" @click="deleteCategory(selectedCategory)">
              删除分类
            </el-button>
          </div>
        </div>

        <div class="empty-state" v-else>
          <i class="el-icon-folder-opened"></i>
          <p>请选择一个分类查看详情</p>
        </div>
      </div>
    </div>

    <!-- 分类表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="categoryForm"
        :model="categoryForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类名称" prop="title">
              <el-input
                v-model="categoryForm.title"
                placeholder="请输入分类名称"
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序值" prop="sortOrder">
              <el-input-number
                v-model="categoryForm.sortOrder"
                :min="0"
                :max="9999"
                placeholder="排序值"
                style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="父级分类" prop="parentId">
          <el-cascader
            v-model="categoryForm.parentId"
            :options="categoryOptions"
            :props="cascaderProps"
            placeholder="请选择父级分类"
            clearable
            style="width: 100%"
          ></el-cascader>
        </el-form-item>

        <el-form-item label="分类图标" prop="icon">
          <el-input
            v-model="categoryForm.icon"
            placeholder="请输入图标类名，如：el-icon-folder"
          >
            <template slot="prepend">
              <i :class="categoryForm.icon || 'el-icon-folder'"></i>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入分类描述"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCategoryTreeApi,
  createCategoryApi,
  updateCategoryApi,
  categoryDelApi,
  batchDeleteCategoriesApi,
  moveCategoryApi,
  searchCategoriesApi,
} from '@/api/uploadPictures';

export default {
  name: 'CategoryManagement',
  data() {
    return {
      categoryTreeData: [],
      categoryOptions: [],
      selectedCategories: [],
      selectedCategory: null,
      searchKeyword: '',
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      submitting: false,
      categoryForm: {
        id: null,
        title: '',
        parentId: 0,
        sortOrder: 0,
        description: '',
        icon: 'el-icon-folder'
      },
      treeProps: {
        children: 'children',
        label: 'title'
      },
      cascaderProps: {
        value: 'id',
        label: 'title',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      formRules: {
        title: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 1, max: 50, message: '分类名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        sortOrder: [
          { type: 'number', message: '排序必须为数字值', trigger: 'blur' }
        ],
        description: [
          { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    allowDrop() {
      return true;
    },
    allowDrag() {
      return true;
    }
  },
  mounted() {
    this.loadCategoryTree();
  },
  methods: {
    // 加载分类树
    async loadCategoryTree() {
      try {
        const response = await getCategoryTreeApi();
        this.categoryTreeData = response.data || [];
        this.categoryOptions = this.buildCascaderOptions(response.data || []);
      } catch (error) {
        this.$message.error('加载分类失败：' + error.message);
      }
    },

    // 构建级联选择器选项
    buildCascaderOptions(categories) {
      const options = [{ id: 0, title: '根分类', children: [] }];
      if (categories.length > 0) {
        options[0].children = categories;
      }
      return options;
    },

    // 获取节点图标
    getNodeIcon(data) {
      if (data.icon) {
        return data.icon;
      }
      return data.children && data.children.length > 0 ? 'el-icon-folder' : 'el-icon-folder-opened';
    },

    // 处理节点操作
    handleNodeAction(data, command) {
      switch (command) {
        case 'add':
          this.addSubCategory(data);
          break;
        case 'edit':
          this.editCategory(data);
          break;
        case 'delete':
          this.deleteCategory(data);
          break;
      }
    },

    // 显示创建对话框
    showCreateDialog() {
      this.isEdit = false;
      this.dialogTitle = '新增分类';
      this.categoryForm = {
        id: null,
        title: '',
        parentId: 0,
        sortOrder: 0,
        description: '',
        icon: 'el-icon-folder'
      };
      this.dialogVisible = true;
    },

    // 新增子分类
    addSubCategory(parentCategory) {
      this.isEdit = false;
      this.dialogTitle = '新增子分类';
      this.categoryForm = {
        id: null,
        title: '',
        parentId: parentCategory.id,
        sortOrder: 0,
        description: '',
        icon: 'el-icon-folder'
      };
      this.dialogVisible = true;
    },

    // 编辑分类
    editCategory(category) {
      this.isEdit = true;
      this.dialogTitle = '编辑分类';
      this.categoryForm = { ...category };
      this.dialogVisible = true;
    },

    // 提交表单
    async submitForm() {
      try {
        await this.$refs.categoryForm.validate();
        this.submitting = true;

        if (this.isEdit) {
          await updateCategoryApi(this.categoryForm);
          this.$message.success('更新成功');
        } else {
          await createCategoryApi(this.categoryForm);
          this.$message.success('创建成功');
        }

        this.dialogVisible = false;
        this.loadCategoryTree();
      } catch (error) {
        if (error.message) {
          this.$message.error(error.message);
        }
      } finally {
        this.submitting = false;
      }
    },

    // 删除分类
    async deleteCategory(category) {
      try {
        await this.$confirm(`确定要删除分类"${category.title}"吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        await categoryDelApi(category.id);
        this.$message.success('删除成功');
        this.loadCategoryTree();
        
        // 如果删除的是当前选中的分类，清空选中状态
        if (this.selectedCategory && this.selectedCategory.id === category.id) {
          this.selectedCategory = null;
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败：' + error.message);
        }
      }
    },

    // 批量删除
    async batchDelete() {
      if (this.selectedCategories.length === 0) {
        this.$message.warning('请选择要删除的分类');
        return;
      }

      try {
        await this.$confirm(`确定要删除选中的 ${this.selectedCategories.length} 个分类吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        const ids = this.selectedCategories.map(item => item.id);
        await batchDeleteCategoriesApi(ids);

        this.$message.success('批量删除成功');
        this.selectedCategories = [];
        this.selectedCategory = null;
        this.loadCategoryTree();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量删除失败：' + error.message);
        }
      }
    },

    // 搜索处理
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.loadCategoryTree();
        return;
      }

      try {
        const response = await searchCategoriesApi(this.searchKeyword);
        this.categoryTreeData = response.data || [];
      } catch (error) {
        this.$message.error('搜索失败：' + error.message);
      }
    },

    // 处理节点拖拽
    async handleNodeDrop(draggingNode, dropNode, dropType) {
      if (dropType === 'inner') {
        try {
          await moveCategoryApi(draggingNode.data.id, dropNode.data.id);
          this.$message.success('移动成功');
          this.loadCategoryTree();
        } catch (error) {
          this.$message.error('移动失败：' + error.message);
          this.loadCategoryTree();
        }
      }
    },

    // 处理复选框变化
    handleCheckChange() {
      this.selectedCategories = this.$refs.categoryTree.getCheckedNodes();
    },

    // 处理节点点击
    handleNodeClick(data) {
      this.selectedCategory = data;
    },

    // 重置表单
    resetForm() {
      if (this.$refs.categoryForm) {
        this.$refs.categoryForm.resetFields();
      }
    }
  }
};
</script>

<style scoped lang="scss">
.category-management {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 20px;
  
  h2 {
    margin: 0 0 5px 0;
    color: #303133;
  }
  
  p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.content-container {
  flex: 1;
  display: flex;
  gap: 20px;
  min-height: 0;
}

.category-tree-panel {
  width: 400px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.detail-panel {
  flex: 1;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    color: #303133;
  }
}

.search-box {
  padding: 0 20px 20px;
}

.tree-container {
  flex: 1;
  padding: 0 20px 20px;
  overflow-y: auto;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-icon {
  margin-right: 5px;
  color: #409eff;
}

.node-title {
  margin-right: 5px;
}

.node-count {
  color: #909399;
  font-size: 12px;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.3s;
}

.custom-tree-node:hover .node-actions {
  opacity: 1;
}

.detail-content {
  padding: 20px;
  flex: 1;
}

.detail-actions {
  margin-top: 20px;
  text-align: right;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  
  i {
    font-size: 64px;
    margin-bottom: 16px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
