// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------

import axios from 'axios';
import { Message } from 'element-ui';
import { getCookies, removeCookies } from '@/libs/util';
import Setting from '@/setting';
import router from '@/router';
const service = axios.create({
  baseURL: Setting.apiBaseURL,
  timeout: 30000, // 请求超时时间 - 增加到30秒
});

axios.defaults.withCredentials = true; // 携带cookie

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    if (config.kefu) {
      let baseUrl = Setting.apiBaseURL.replace(/adminapi/, 'kefuapi');
      config.baseURL = baseUrl;
    } else {
      config.baseURL = Setting.apiBaseURL;
    }
    if (config.file) {
      config.headers['Content-Type'] = 'multipart/form-data';
    }
    const token = getCookies('token') || localStorage.getItem('token');
    const kefuToken = getCookies('kefu_token');
    if (token || kefuToken) {
      config.headers['Authori-zation'] = config.kefu ? 'Bearer ' + kefuToken : 'Bearer ' + token;
    }
    return config;
  },
  (error) => {
    // do something with request error
    console.error('Request error:', error);
    return Promise.reject(error);
  },
);

// response interceptor
service.interceptors.response.use(
  (response) => {
    let obj = {};
    if (!!response.data) {
      if (typeof response.data == 'string') {
        try {
          obj = JSON.parse(response.data);
        } catch (e) {
          console.error('JSON parse error:', e);
          obj = { code: 400, message: '数据格式错误' };
        }
      } else {
        obj = response.data;
      }
    }
    let status = response.data ? obj.code : 0;
    // let status = response.data ? response.data.status : 0;
    const code = parseInt(status);
    switch (code) {
      case 200:
        return obj;
      case 110002:
      case 110003:
      case 110004:
        localStorage.clear();
        removeCookies('token');
        removeCookies('expires_time');
        removeCookies('uuid');
        router.replace({ name: 'login' });
        break;
      case 110005:
      case 110006:
      case 110007:
        removeCookies('kefuInfo');
        removeCookies('kefu_token');
        removeCookies('kefu_expires_time');
        removeCookies('kefu_uuid');
        router.replace({ path: '/kefu' });
        break;
      case 110008:
        router.replace({ name: 'system_opendir_login' });
        break;
      default:
        return Promise.reject(obj || { message: '未知错误' });
    }
  },
  (error) => {
    console.error('Response error:', error);
    let message = '网络错误，请稍后重试';
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const status = error.response.status;
      switch (status) {
        case 401:
          message = '未授权，请重新登录';
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        default:
          message = `请求错误 (${status})`;
      }
    } else if (error.request) {
      // The request was made but no response was received
      message = '服务器无响应，请检查网络';
    } else {
      // Something happened in setting up the request that triggered an Error
      message = error.message;
    }
    Message.error(message);
    return Promise.reject({ message });
  },
);

export default service;
