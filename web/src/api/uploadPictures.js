// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from '@/libs/request';

/**
 * @description 获取分类列表
 * @param {Object} param params {Object} 传值参数
 */
export function getCategoryListApi(params) {
  return request({
    url: 'file/category',
    method: 'get',
    params,
  });
}

/**
 * @description 添加分类
 */
export function createApi(id) {
  return request({
    url: 'file/category/create',
    method: 'get',
    params: id,
  });
}

/**
 * @description 编辑分类
 * @param {Number} param id {Number} 分类id
 */
export function categoryEditApi(id) {
  return request({
    url: `file/category/${id}/edit`,
    method: 'get',
  });
}

/**
 * @description 删除分类
 * @param {Number} param id {Number} 分类id
 */
export function categoryDelApi(id) {
  return request({
    url: `file/category/${id}`,
    method: 'delete',
  });
}

/**
 * @description 附件列表
 * @param {Object} param data {Object} 传值
 */
export function fileListApi(data) {
  return request({
    url: 'file/file',
    method: 'get',
    params: data,
  });
}

/**
 * @description 移动分类，修改附件分类表单
 * @param {Object} param data {Object} 传值
 */
export function moveApi(data) {
  return request({
    url: 'file/file/do_move',
    method: 'put',
    data,
  });
}

/**
 * @description 修改附件名称
 * @param {String} param ids {String} 图片id拼接成的字符串
 */
export function fileUpdateApi(ids, data) {
  return request({
    url: 'file/file/update/' + ids,
    method: 'put',
    data,
  });
}

/**
 * @description 删除附件
 * @param {String} param ids {String} 图片id拼接成的字符串
 */
export function fileDelApi(ids) {
  return request({
    url: 'file/file/delete',
    method: 'post',
    data: ids,
  });
}
/**
 * @description 网络图片上传
 */
export function onlineUpload(data) {
  return request({
    url: 'file/online_upload',
    method: 'post',
    data,
  });
}

/**
 * @description 清除扫码上传 code
 */
export function scanUploadCode() {
  return request({
    url: 'file/scan_upload/qrcode',
    method: 'delete',
  });
}

/**
 * @description 扫码上传链接获取
 */
export function scanUploadQrcode(pid) {
  return request({
    url: `file/scan_upload/qrcode?pid=${pid}`,
    method: 'get',
  });
}

/**
 * @description 扫码上传图片获取
 */
export function scanUploadGet(scan_token) {
  return request({
    url: `file/scan_upload/image/${scan_token}`,
    method: 'get',
  });
}

/**
 * @description 素材管理-视频上传
 */
export function videoCloudUpload(data) {
  return request({
    url: 'file/video_data_save',
    method: 'post',
    data,
  });
}

/**
 * @description 创建分类
 * @param {Object} data 分类数据
 */
export function createCategoryApi(data) {
  return request({
    url: 'file/category',
    method: 'post',
    data,
  });
}

/**
 * @description 更新分类
 * @param {Object} data 分类数据
 */
export function updateCategoryApi(data) {
  return request({
    url: 'file/category',
    method: 'put',
    data,
  });
}

/**
 * @description 批量删除分类
 * @param {Array} ids 分类ID数组
 */
export function batchDeleteCategoriesApi(ids) {
  return request({
    url: 'file/category/batch',
    method: 'delete',
    data: ids,
  });
}

/**
 * @description 移动分类
 * @param {Number} id 分类ID
 * @param {Number} newParentId 新父分类ID
 */
export function moveCategoryApi(id, newParentId) {
  return request({
    url: `file/category/${id}/move`,
    method: 'put',
    params: { newParentId },
  });
}

/**
 * @description 更新分类排序
 * @param {Number} id 分类ID
 * @param {Number} sortOrder 排序值
 */
export function updateCategorySortApi(id, sortOrder) {
  return request({
    url: `file/category/${id}/sort`,
    method: 'put',
    params: { sortOrder },
  });
}

/**
 * @description 获取分类路径
 * @param {Number} id 分类ID
 */
export function getCategoryPathApi(id) {
  return request({
    url: `file/category/${id}/path`,
    method: 'get',
  });
}

/**
 * @description 搜索分类
 * @param {String} title 搜索关键词
 */
export function searchCategoriesApi(title) {
  return request({
    url: 'file/category/search',
    method: 'get',
    params: { title },
  });
}

/**
 * @description 获取分类树
 */
export function getCategoryTreeApi() {
  return request({
    url: 'file/category/tree',
    method: 'get',
  });
}

/**
 * @description 获取分类树（用于级联选择器）
 */
export function categoryTreeApi() {
  return request({
    url: 'file/category',
    method: 'get',
  });
}
