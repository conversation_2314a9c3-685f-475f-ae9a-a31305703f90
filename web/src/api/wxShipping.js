import request from '@/libs/request';

/**
 * 微信发货信息管理API
 */

// 获取微信发货信息列表
export function getWxShippingList(params) {
  return request({
    url: '/admin/wx/shipping/list',
    method: 'get',
    params
  });
}

// 上传单个订单发货信息
export function uploadShippingInfo(orderId) {
  return request({
    url: `/admin/wx/shipping/upload/order/${orderId}`,
    method: 'post'
  });
}

// 批量上传发货信息
export function batchUploadShipping(data) {
  return request({
    url: '/admin/wx/shipping/batch-upload',
    method: 'post',
    data
  });
}

// 查询订单发货状态
export function getOrderShippingStatus(params) {
  return request({
    url: '/admin/wx/shipping/order/status',
    method: 'get',
    params
  });
}

// 确认收货提醒
export function notifyConfirmReceive(data) {
  return request({
    url: '/admin/wx/shipping/notify/confirm-receive',
    method: 'post',
    params: data
  });
}

// 设置消息跳转路径
export function setMsgJumpPath(path) {
  return request({
    url: '/admin/wx/shipping/set-jump-path',
    method: 'post',
    params: { path }
  });
}

// 检查服务开通状态
export function checkServiceStatus() {
  return request({
    url: '/admin/wx/shipping/service/status',
    method: 'get'
  });
}

// 手动录入发货信息
export function manualUploadShipping(data) {
  return request({
    url: '/admin/wx/shipping/upload',
    method: 'post',
    data
  });
}
