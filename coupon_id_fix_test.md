# 优惠券ID获取问题修复测试

## 问题描述
选择优惠券后，优惠券ID未正确获取，导致：
1. 优惠券选择后不显示或显示错误
2. 价格计算时优惠券不生效
3. 页面刷新后优惠券选择丢失

## 修复内容

### 1. 数据持久化
- 在优惠券选择确认时，将couponId保存到storage
- 在价格重新计算时，同步更新storage中的couponId
- 确保数据在页面跳转和刷新后不丢失

### 2. 数据加载优先级
- 页面初始化时，优先使用storage中的couponId
- 只有在storage中没有数据时，才使用URL参数中的couponId
- 避免URL参数覆盖用户已选择的优惠券

### 3. 调试功能增强
- 添加"调试优惠券ID获取"按钮
- 显示storage和页面数据的对比
- 提供重新计算价格的快捷操作

## 修改的文件

### 1. app/wjhx/pages/shopping/coupon-select/coupon-select.js
```javascript
// 在confirmSelection方法中添加storage保存
const couponId = this.data.selectedCouponId ? parseInt(this.data.selectedCouponId) : 0;
if (couponId > 0) {
  wx.setStorageSync('couponId', couponId);
  console.log('保存优惠券ID到storage:', couponId);
} else {
  wx.removeStorageSync('couponId');
  console.log('清除storage中的优惠券ID');
}
```

### 2. app/wjhx/pages/shopping/checkout/checkout.js
```javascript
// 修复onLoad方法中的数据加载逻辑
var storageCouponId = wx.getStorageSync('couponId');
if (storageCouponId && storageCouponId !== 'null' && storageCouponId > 0) {
  this.setData({
    'couponId': parseInt(storageCouponId)
  });
  console.log('Loaded couponId from storage:', storageCouponId);
}

// 优先使用storage中的数据
const finalCouponId = this.data.couponId > 0 ? this.data.couponId : parseInt(couponId);

// 在价格计算时同步更新storage
if (res.data.checkedCoupon && res.data.checkedCoupon.id) {
  wx.setStorageSync('couponId', parseInt(res.data.checkedCoupon.id));
  console.log('保存后端返回的优惠券ID到storage:', res.data.checkedCoupon.id);
}
```

### 3. app/wjhx/pages/shopping/checkout/checkout.wxml
```xml
<!-- 添加调试按钮 -->
<view class="debug-button" bindtap="debugCouponId">
    <text>调试优惠券ID获取</text>
</view>
```

## 测试步骤

### 测试用例1：正常选择优惠券
1. 进入checkout页面
2. 点击"调试优惠券ID获取"按钮，查看初始状态
3. 点击优惠券卡片，进入选择页面
4. 选择一张优惠券，点击"确认选择"
5. 返回checkout页面后，再次点击"调试优惠券ID获取"
6. 验证：
   - Storage中应该有正确的couponId
   - 页面中应该显示选中的优惠券
   - 价格计算应该包含优惠券抵扣

### 测试用例2：页面刷新后数据保持
1. 在已选择优惠券的状态下
2. 点击"调试优惠券ID获取"，记录当前数据
3. 刷新页面或重新进入checkout页面
4. 再次点击"调试优惠券ID获取"
5. 验证：
   - Storage中的couponId应该保持不变
   - 页面应该正确显示之前选择的优惠券
   - 价格计算应该正确

### 测试用例3：取消优惠券选择
1. 在已选择优惠券的状态下
2. 进入优惠券选择页面
3. 选择"不使用优惠券"
4. 点击"确认选择"
5. 返回checkout页面后点击"调试优惠券ID获取"
6. 验证：
   - Storage中的couponId应该被清除
   - 页面应该显示"请选择优惠券"
   - 价格计算不应该包含优惠券抵扣

### 测试用例4：多次切换优惠券
1. 选择优惠券A，确认
2. 点击"调试优惠券ID获取"，验证数据
3. 重新进入选择页面，选择优惠券B，确认
4. 再次点击"调试优惠券ID获取"
5. 验证：
   - Storage中应该是优惠券B的ID
   - 页面应该显示优惠券B
   - 价格计算应该使用优惠券B的抵扣

## 预期结果

### 成功标准
1. ✅ 优惠券ID能够正确保存到storage
2. ✅ 页面刷新后优惠券选择不丢失
3. ✅ 价格计算正确包含优惠券抵扣
4. ✅ 调试信息显示正确的数据状态
5. ✅ 多次选择和取消操作稳定

### 调试信息检查
点击"调试优惠券ID获取"按钮应该看到：
```
Storage中的ID: 1
页面中的ID: 1
选中的优惠券: 10元消费券
```

控制台应该显示：
```
=== 优惠券ID获取调试 ===
Storage数据: {storageCouponId: 1, orderOptions: {...}}
页面数据: {this.data.couponId: 1, this.data.checkedCoupon: {...}}
```

## 故障排除

### 问题1：Storage中有ID但页面不显示
- 检查onLoad方法中的数据加载逻辑
- 确认setData是否正确执行
- 检查WXML中的数据绑定

### 问题2：选择后Storage中没有保存
- 检查confirmSelection方法中的保存逻辑
- 确认wx.setStorageSync是否正确调用
- 检查couponId的数据类型转换

### 问题3：价格计算不包含优惠券
- 检查recalculatePrice方法中的请求参数
- 确认后端API是否正确处理couponId
- 检查优惠券数据结构是否完整

### 问题4：页面跳转后数据丢失
- 检查页面生命周期方法
- 确认storage操作的时机
- 检查是否有其他地方清除了storage

## 回归测试

修复完成后，还需要测试以下功能确保没有破坏：
1. 地址选择功能
2. 积分抵扣功能
3. 余额抵扣功能
4. 订单提交功能
5. 单商品订单流程
6. 礼券兑换流程

## 总结

通过添加storage持久化和优化数据加载优先级，解决了优惠券ID获取不正确的问题。关键改进包括：

1. **数据持久化**：确保优惠券选择能够跨页面保持
2. **加载优先级**：优先使用用户已选择的数据
3. **调试工具**：提供便捷的问题排查方式
4. **数据同步**：确保storage和页面数据的一致性

这些修复确保了优惠券功能的稳定性和用户体验的连续性。