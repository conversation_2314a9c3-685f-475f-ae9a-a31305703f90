# 余额和积分输入联动功能测试

## 功能概述

已实现余额抵扣和积分抵扣的手动输入功能，用户可以自由调整使用的余额和积分数量，系统会实时联动更新相关计算。

## 新增功能

### ✅ 积分手动输入
- 用户可以手动输入要使用的积分数量
- 实时显示积分抵扣金额
- 自动验证输入范围和最小使用限制
- 支持"使用最大"快捷按钮

### ✅ 余额手动输入优化
- 优化了余额输入的实时响应
- 增强了输入验证和错误提示
- 自动开关联动（输入金额时自动开启抵扣）

### ✅ 实时联动计算
- 输入过程中实时更新抵扣金额
- 实时更新订单总价和实付金额
- 智能组合抵扣计算

## 主要改进

### 1. 积分输入界面 (WXML)

```xml
<!-- 新增积分输入框 -->
<view class="points-input-section">
    <view class="points-input-wrapper">
        <text class="input-label">使用积分：</text>
        <input class="points-input" type="number" value="{{usePoints}}" 
               placeholder="最多可用{{maxUsablePoints}}" 
               bindinput="onPointsInput" bindblur="onPointsBlur" />
        <text class="input-unit">分</text>
    </view>
    <view class="points-tips">
        <text class="tip-text">积分抵扣{{format.formatPrice(pointsPrice)}}元（{{pointsConfig.useRate}}积分=1元）</text>
        <text class="max-use-btn" bindtap="useMaxPoints">使用最大</text>
    </view>
</view>
```

### 2. 输入处理逻辑 (JavaScript)

#### A. 积分输入处理
```javascript
// 实时输入处理
onPointsInput(e) {
    const value = parseInt(e.detail.value) || 0;
    const finalValue = Math.min(value, this.data.maxUsablePoints);
    this.setData({ usePoints: finalValue });
    this.updateDeductionsRealtime(); // 实时更新
}

// 失焦验证处理
onPointsBlur(e) {
    // 验证最小使用限制、用户积分余额等
    // 自动开关联动
    // 显示错误提示
}
```

#### B. 余额输入优化
```javascript
// 实时输入处理
onBalanceInput(e) {
    const value = parseFloat(e.detail.value) || 0;
    const finalValue = Math.min(value, this.data.maxUsableBalance);
    this.setData({ useBalance: finalValue });
    this.updateDeductionsRealtime(); // 实时更新
}
```

#### C. 实时更新机制
```javascript
// 新增实时更新方法，避免频繁API调用
updateDeductionsRealtime: function() {
    const deductionResult = this.calculateAllDeductions();
    this.setData({
        couponPrice: deductionResult.couponPrice,
        pointsPrice: deductionResult.pointsPrice,
        balancePrice: deductionResult.balancePrice,
        orderTotalPrice: deductionResult.orderTotalPrice,
        actualPrice: deductionResult.actualPrice
    });
}
```

## 测试场景

### 1. 积分输入测试
- [ ] **基础输入**：输入有效积分数量，验证抵扣金额正确计算
- [ ] **超限输入**：输入超过最大可用积分，验证自动限制
- [ ] **最小限制**：输入少于最小使用积分，验证提示和处理
- [ ] **开关联动**：输入积分时自动开启积分抵扣开关
- [ ] **清零处理**：输入0时自动关闭积分抵扣开关

### 2. 余额输入测试
- [ ] **基础输入**：输入有效余额金额，验证抵扣金额正确计算
- [ ] **超限输入**：输入超过最大可用余额，验证自动限制
- [ ] **余额不足**：输入超过用户余额，验证提示和处理
- [ ] **开关联动**：输入余额时自动开启余额抵扣开关
- [ ] **清零处理**：输入0时自动关闭余额抵扣开关

### 3. 实时联动测试
- [ ] **实时计算**：输入过程中实时更新抵扣金额
- [ ] **组合计算**：同时使用积分和余额时的正确计算
- [ ] **优先级处理**：验证抵扣优先级（优惠券>积分>余额）
- [ ] **总额限制**：验证总抵扣不超过订单金额

### 4. 用户体验测试
- [ ] **响应速度**：输入响应是否流畅
- [ ] **错误提示**：错误情况下的提示是否清晰
- [ ] **操作便利性**：是否方便用户操作
- [ ] **数据一致性**：页面显示是否与实际计算一致

## 验证步骤

### 步骤1：积分输入验证
1. 开启积分抵扣开关
2. 在积分输入框中输入不同数值
3. 观察积分抵扣金额是否实时更新
4. 验证输入验证逻辑是否正确

### 步骤2：余额输入验证
1. 开启余额抵扣开关
2. 在余额输入框中输入不同数值
3. 观察余额抵扣金额是否实时更新
4. 验证输入验证逻辑是否正确

### 步骤3：组合使用验证
1. 同时开启积分和余额抵扣
2. 分别调整积分和余额使用数量
3. 验证总抵扣金额和实付金额计算正确
4. 验证抵扣优先级是否正确

### 步骤4：边界情况验证
1. 测试输入超限情况的处理
2. 测试输入无效值的处理
3. 测试开关联动是否正常
4. 测试错误提示是否准确

## 预期效果

用户现在可以：
1. **精确控制**：手动输入想要使用的积分和余额数量
2. **实时反馈**：输入过程中立即看到抵扣效果
3. **智能验证**：系统自动验证输入有效性并给出提示
4. **便捷操作**：通过"使用最大"按钮快速使用最大可用金额
5. **自动联动**：输入金额时自动开启相应的抵扣开关

## 技术特点

1. **实时计算**：使用本地计算避免频繁API调用，提高响应速度
2. **智能验证**：多层验证确保输入数据的有效性
3. **自动联动**：输入和开关状态自动同步
4. **用户友好**：清晰的错误提示和操作引导
5. **性能优化**：区分实时更新和完整重算，平衡性能和准确性

这些改进大大提升了用户在使用抵扣功能时的灵活性和便利性。
