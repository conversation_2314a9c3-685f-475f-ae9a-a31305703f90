# 余额抵扣功能完善说明

## 问题分析

通过代码审查发现余额抵扣功能存在以下问题：

### 1. 前端逻辑不完善 ❌
- **单商品订单缺少余额计算**：`calculateSingleGoodsPrice()`方法中没有包含余额抵扣
- **余额开关切换不及时**：切换后没有立即重新计算最大可用余额和实付金额
- **最大可用余额计算简单**：没有考虑优惠券和积分抵扣的影响
- **余额输入处理不完善**：缺少实时的余额价格更新

### 2. 后端逻辑缺陷 ❌
- **缺少余额使用记录**：没有余额使用历史追踪
- **事务管理不完善**：余额扣减缺少完整的事务保护
- **审计日志缺失**：无法追踪余额的使用情况
- **数据一致性风险**：没有余额计算验证机制

### 3. 数据结构不完整 ❌
- **缺少余额记录表**：无法追踪余额的充值、使用、退款记录
- **缺少余额服务**：没有专门的余额管理服务类

## 完善方案

### 1. 前端逻辑完善

#### 修复文件：`app/wjhx/pages/shopping/checkout/checkout.js`

**主要改进：**

1. **余额开关切换优化**
   ```javascript
   onBalanceSwitchChange(e) {
     const enabled = e.detail.value;
     const maxUsableBalance = this.calculateMaxUsableBalance();
     const useBalance = enabled ? maxUsableBalance : 0;
     const balancePrice = enabled ? useBalance : 0;
     
     // 立即更新数据并重新计算价格
     this.setData({
       useBalanceEnabled: enabled,
       useBalance: useBalance,
       balancePrice: balancePrice,
       maxUsableBalance: maxUsableBalance
     });
     
     // 区分单商品和购物车订单
     if (this.data.goodsId > 0 && this.data.type == 1) {
       this.calculateSingleGoodsPrice();
     } else {
       this.recalculatePrice();
     }
   }
   ```

2. **余额输入处理完善**
   - 实时更新`balancePrice`
   - 添加详细的调试日志
   - 优化失焦处理逻辑

3. **最大可用余额计算优化**
   ```javascript
   calculateMaxUsableBalance() {
     const goodsTotalPrice = this.data.goodsTotalPrice || 0;
     const freightPrice = this.data.freightPrice || 0;
     const couponPrice = this.data.couponPrice || 0;
     const pointsPrice = this.data.pointsPrice || 0;
     const userBalance = this.data.userBalance || 0;
     
     // 订单总价 = 商品总价 + 运费 - 优惠券抵扣 - 积分抵扣
     const orderTotal = goodsTotalPrice + freightPrice - couponPrice - pointsPrice;
     
     // 最大可用余额 = min(用户余额, max(0, 订单总价))
     return Math.min(userBalance, Math.max(0, orderTotal));
   }
   ```

4. **单商品订单余额计算**
   - 在`calculateSingleGoodsPrice()`中添加余额抵扣计算
   - 确保余额抵扣正确影响实付金额
   - 更新费用明细显示

### 2. 后端架构完善

#### 新增文件：

1. **余额记录实体类**：`server/src/main/java/com/logic/code/entity/BalanceRecord.java`
2. **余额记录Mapper**：`server/src/main/java/com/logic/code/mapper/BalanceRecordMapper.java`
3. **余额服务类**：`server/src/main/java/com/logic/code/service/BalanceService.java`

#### 主要功能：

1. **余额使用记录**
   ```java
   @Transactional(rollbackFor = Exception.class)
   public boolean useBalanceForOrder(Long userId, BigDecimal amount, Long orderId, String description) {
     // 检查余额是否足够
     // 更新用户余额
     // 创建余额使用记录
     // 事务保护
   }
   ```

2. **余额充值功能**
   ```java
   public boolean rechargeBalance(Long userId, BigDecimal amount, String source, Long sourceId, String description)
   ```

3. **余额退款功能**
   ```java
   public boolean refundToBalance(Long userId, BigDecimal amount, Long orderId, String description)
   ```

4. **余额一致性验证**
   ```java
   public boolean validateUserBalance(Long userId)
   ```

### 3. 数据库结构完善

#### 新增表：`weshop_balance_record`

```sql
CREATE TABLE `weshop_balance_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '操作类型：recharge-充值, use-使用, refund-退款, adjust-调整',
  `amount` decimal(10,2) NOT NULL COMMENT '金额（正数为增加，负数为减少）',
  `balance_before` decimal(10,2) NOT NULL COMMENT '操作前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '操作后余额',
  `source` varchar(50) NOT NULL COMMENT '来源：order-订单, recharge-充值, refund-退款, manual-手动调整',
  `source_id` bigint(20) DEFAULT NULL COMMENT '来源ID（如订单ID、充值记录ID等）',
  `description` varchar(255) DEFAULT NULL COMMENT '操作描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_source` (`source`, `source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额使用记录表';
```

### 4. 订单服务集成

#### 修复文件：`server/src/main/java/com/logic/code/service/OrderService.java`

**主要改进：**

1. **集成新的余额服务**
   ```java
   @Resource
   private BalanceService balanceService;
   ```

2. **使用新的余额扣减方法**
   ```java
   // 使用余额支付
   if (balancePrice.compareTo(BigDecimal.ZERO) > 0) {
       boolean balanceUsed = balanceService.useBalanceForOrder(
           userInfo.getId(), 
           balancePrice, 
           order.getId(), 
           "订单使用余额支付"
       );
       if (!balanceUsed) {
           throw new WeshopWechatException(WeshopWechatResultStatus.BALANCE_USE_FAILED);
       }
   }
   ```

## 完善后的功能特性

### ✅ 前端功能
- **实时余额计算**：余额开关切换立即更新实付金额
- **智能最大余额**：考虑优惠券和积分抵扣后的可用余额
- **单商品支持**：单商品订单完整支持余额抵扣
- **输入验证**：余额输入自动验证和限制
- **费用明细**：清晰显示余额抵扣金额

### ✅ 后端功能
- **完整事务管理**：余额扣减在事务中执行
- **详细使用记录**：每次余额操作都有完整记录
- **数据一致性**：余额计算验证机制
- **审计追踪**：完整的余额使用历史
- **错误处理**：完善的异常处理和回滚

### ✅ 数据管理
- **余额记录表**：追踪所有余额变动
- **操作类型**：充值、使用、退款、调整
- **来源追踪**：记录每次操作的来源和关联ID
- **时间戳**：完整的时间记录

## 部署步骤

### 1. 数据库初始化
```sql
-- 创建余额记录表
source database/create_balance_record_table.sql;
```

### 2. 后端代码部署
- 部署新增的实体类、Mapper、Service
- 更新OrderService的余额处理逻辑
- 重启后端服务

### 3. 前端代码部署
- 更新checkout.js中的余额处理逻辑
- 重新编译和部署小程序

### 4. 功能测试
```sql
-- 执行余额抵扣测试
source database/test_balance_deduction.sql;
```

## 测试验证

### 功能测试清单
- [ ] 余额开关切换立即更新实付金额
- [ ] 余额输入自动验证和限制
- [ ] 最大可用余额计算正确
- [ ] 单商品订单余额抵扣正常
- [ ] 购物车订单余额抵扣正常
- [ ] 余额与优惠券、积分组合使用
- [ ] 余额不足时正确提示
- [ ] 订单提交后余额正确扣减
- [ ] 余额使用记录完整创建
- [ ] 数据一致性验证通过

### 安全测试清单
- [ ] 余额不能超额使用
- [ ] 并发订单不会导致余额异常
- [ ] 事务回滚机制正常工作
- [ ] 余额记录不能被篡改
- [ ] 数据一致性检查通过

## 监控指标

部署后需要监控：
- 余额使用成功率
- 余额计算一致性
- 订单提交成功率
- 余额记录完整性
- 异常余额操作

## 长期优化建议

1. **性能优化**：余额记录表分表策略
2. **实时监控**：余额异常变动告警
3. **数据分析**：余额使用行为分析
4. **安全加固**：余额操作权限控制
5. **用户体验**：余额使用引导和提示优化
