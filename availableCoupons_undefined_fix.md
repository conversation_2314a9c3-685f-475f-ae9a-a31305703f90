# availableCoupons未定义错误修复

## 错误描述
```
checkout.js? [sm]:327 Failed to get address: ReferenceError: availableCoupons is not defined at checkout.js? [sm]:241
```

## 问题原因
在修改单商品订单的优惠券处理逻辑时，我在API调用之前就尝试使用`availableCoupons`变量，但这个变量是在API调用的回调函数中才定义的，导致了作用域错误。

### 错误的代码结构：
```javascript
// 构建优惠券对象
let checkedCoupon = null;
if (that.data.couponId > 0) {
  // ❌ 错误：availableCoupons在这里还未定义
  const selectedCoupon = availableCoupons.find(coupon => coupon.id === that.data.couponId);
  // ...
}

// API调用
util.request(api.CouponList, {
  // ...
}).then(function (couponRes) {
  let availableCoupons = []; // ✅ availableCoupons在这里才定义
  // ...
});
```

## 修复方案

### 1. 重新组织代码结构
将优惠券对象的构建分为两个阶段：
1. **初始构建**：在API调用前创建基本的优惠券对象
2. **完善更新**：在API调用成功后用完整数据更新优惠券对象

### 2. 修复后的代码结构：
```javascript
// 先构建基本的优惠券对象
let checkedCoupon = null;
if (that.data.couponId > 0) {
  // ✅ 创建基本的优惠券对象，稍后会从API数据中更新
  checkedCoupon = {
    id: that.data.couponId,
    name: that.data.couponName || '优惠券',
    amount: 0, // 默认金额，后续会从API数据中更新
    minAmount: 0
  };
}

// API调用
util.request(api.CouponList, {
  // ...
}).then(function (couponRes) {
  let availableCoupons = [];
  // ... 处理API数据

  // ✅ 如果选择了优惠券，尝试从API数据中找到完整信息
  if (that.data.couponId > 0 && couponRes.success && couponRes.data) {
    const selectedCoupon = couponRes.data.find(coupon => coupon.id === that.data.couponId);
    if (selectedCoupon) {
      checkedCoupon = selectedCoupon; // 用完整数据更新
    }
  }
});
```

## 修改的文件

### app/wjhx/pages/shopping/checkout/checkout.js

**主要修改**：
1. 将优惠券对象的初始构建移到API调用之前
2. 在API调用成功后，用完整的优惠券数据更新对象
3. 确保在catch块中也能访问到checkedCoupon变量

## 修复验证

### 1. 语法检查
```bash
node -c app/wjhx/pages/shopping/checkout/checkout.js
```
✅ 通过，无语法错误

### 2. 变量作用域检查
- ✅ `checkedCoupon`变量在API调用前定义，在catch块中可访问
- ✅ `availableCoupons`变量在正确的作用域中使用
- ✅ 所有变量引用都在其定义之后

## 测试建议

### 1. 基本功能测试
- 进入单商品订单页面
- 选择优惠券
- 验证不再出现"availableCoupons is not defined"错误

### 2. 边界情况测试
- 网络异常情况下的优惠券处理
- 优惠券API返回空数据的情况
- 选择的优惠券在API数据中不存在的情况

### 3. 控制台日志检查
应该看到以下日志序列：
```
创建基本优惠券对象: {id: 1, name: "优惠券", amount: 0, minAmount: 0}
单商品订单优惠券数据: {全部优惠券: [...], 可用优惠券: [...], 商品总价: 100}
从API数据中找到选中的优惠券: {id: 1, name: "10元消费券", amount: 10, minAmount: 50}
```

## 预防措施

### 1. 变量作用域管理
- 在使用变量前确保其已在当前作用域中定义
- 对于异步操作中的变量，注意回调函数的作用域

### 2. 代码结构优化
- 将变量定义尽可能提前到合适的作用域
- 避免在异步回调中定义外部需要访问的变量

### 3. 错误处理完善
- 确保在catch块中能够访问到必要的变量
- 为异步操作失败的情况提供合理的默认值

## 总结

这个错误是由于在重构代码时没有注意到JavaScript的变量作用域规则导致的。通过重新组织代码结构，将变量定义移到正确的位置，确保了代码的正确执行。

修复后的代码结构更加清晰，既保证了功能的完整性，也避免了作用域相关的错误。