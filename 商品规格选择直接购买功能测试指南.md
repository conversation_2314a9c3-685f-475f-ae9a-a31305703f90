# 商品规格选择直接购买功能测试指南

## 测试环境准备

1. 确保小程序开发环境正常运行
2. 准备测试商品数据：
   - 无规格商品（单一规格）
   - 有规格商品（多规格选择）
3. 确保用户已登录

## 测试用例

### 测试用例1：无规格商品购买流程

**测试步骤：**
1. 进入无规格商品详情页
2. 点击"立即购买"按钮
3. 验证是否直接跳转到结账页面

**预期结果：**
- 不显示规格选择弹窗
- 直接跳转到结账页面
- 结账页面显示正确的商品信息

### 测试用例2：无规格商品加入购物车流程

**测试步骤：**
1. 进入无规格商品详情页
2. 点击"加入购物车"按钮
3. 验证购物车图标数量是否更新

**预期结果：**
- 不显示规格选择弹窗
- 显示"添加成功"提示
- 购物车图标数量增加

### 测试用例3：有规格商品立即购买流程

**测试步骤：**
1. 进入有规格商品详情页
2. 不选择任何规格，直接点击"立即购买"按钮
3. 验证规格选择弹窗是否自动打开
4. 选择完整的商品规格
5. 点击确认按钮
6. 验证是否自动跳转到结账页面

**预期结果：**
- 自动打开规格选择弹窗
- 显示"请选择商品规格"提示
- 确认按钮显示"立即购买 (X件)"
- 选择规格后自动跳转到结账页面

### 测试用例4：有规格商品加入购物车流程

**测试步骤：**
1. 进入有规格商品详情页
2. 不选择任何规格，直接点击"加入购物车"按钮
3. 验证规格选择弹窗是否自动打开
4. 选择完整的商品规格
5. 点击确认按钮
6. 验证是否自动加入购物车

**预期结果：**
- 自动打开规格选择弹窗
- 显示"请选择商品规格"提示
- 确认按钮显示"加入购物车 (X件)"
- 选择规格后自动加入购物车
- 购物车图标数量增加

### 测试用例5：规格选择取消操作

**测试步骤：**
1. 进入有规格商品详情页
2. 点击"立即购买"或"加入购物车"按钮
3. 在规格选择弹窗中点击关闭按钮（不选择规格）
4. 验证是否正常关闭弹窗

**预期结果：**
- 弹窗正常关闭
- 不执行购买操作
- 页面状态恢复正常

### 测试用例6：规格选择不完整的情况

**测试步骤：**
1. 进入有多个规格选项的商品详情页
2. 点击"立即购买"按钮
3. 只选择部分规格（不完整）
4. 点击确认按钮
5. 验证系统反应

**预期结果：**
- 确认按钮显示"请选择完整规格"
- 按钮处于不可用状态
- 不执行购买操作

### 测试用例7：数量选择功能

**测试步骤：**
1. 进入商品详情页
2. 点击购买按钮打开规格选择弹窗
3. 修改商品数量
4. 验证按钮文本是否更新
5. 确认购买后验证数量是否正确

**预期结果：**
- 按钮文本实时更新数量显示
- 购买时使用正确的数量
- 结账页面显示正确数量

### 测试用例8：库存限制测试

**测试步骤：**
1. 进入库存有限的商品详情页
2. 在规格选择中增加数量到库存上限
3. 尝试继续增加数量
4. 验证系统限制

**预期结果：**
- 数量不能超过库存限制
- 显示"库存不足"提示
- 按钮状态正确显示

## 测试检查点

### 界面检查
- [ ] 规格选择弹窗正常显示
- [ ] 按钮文本根据操作类型正确显示
- [ ] 数量选择器正常工作
- [ ] 规格选项正确显示和选择

### 功能检查
- [ ] 无规格商品直接购买正常
- [ ] 有规格商品自动弹窗选择
- [ ] 规格选择后自动执行购买操作
- [ ] 购物车数量正确更新
- [ ] 结账页面参数传递正确

### 异常处理检查
- [ ] 未登录用户正确跳转登录页
- [ ] 库存不足时正确提示
- [ ] 网络异常时正确处理
- [ ] 规格选择不完整时正确提示

## 测试数据准备

### 商品数据
```sql
-- 无规格商品
INSERT INTO goods (name, retail_price, goods_number) VALUES ('测试商品1', 99.00, 100);

-- 有规格商品
INSERT INTO goods (name, retail_price, goods_number) VALUES ('测试商品2', 199.00, 50);
INSERT INTO goods_specification (goods_id, name) VALUES (2, '颜色');
INSERT INTO goods_specification_value (specification_id, value) VALUES (1, '红色');
INSERT INTO goods_specification_value (specification_id, value) VALUES (1, '蓝色');
```

## 测试报告模板

### 测试结果记录
- 测试用例编号：
- 测试结果：通过/失败
- 实际结果：
- 问题描述：
- 截图：

### 问题分类
- 功能问题
- 界面问题
- 性能问题
- 兼容性问题

## 回归测试

在修复问题后，需要重新执行以下核心测试用例：
1. 测试用例3：有规格商品立即购买流程
2. 测试用例4：有规格商品加入购物车流程
3. 测试用例6：规格选择不完整的情况