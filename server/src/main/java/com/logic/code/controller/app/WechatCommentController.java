package com.logic.code.controller.app;

import com.logic.code.common.response.Result;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.model.query.CommentQuery;
import com.logic.code.model.vo.CommentCountVO;
import com.logic.code.model.vo.CommentPostVO;
import com.logic.code.model.vo.CommentResultVO;
import com.logic.code.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/wechat/comment")
public class WechatCommentController {

    @Autowired
    private CommentService commentService;


    @GetMapping("/list")
    public Result<List<CommentResultVO>> queryList(@Validated CommentQuery commentQuery) {
        return Result.success(commentService.queryList(commentQuery));
    }

    @GetMapping("/count")
    public Result<CommentCountVO> countList(@Validated CommentQuery commentQuery) {
        return Result.success(commentService.countList(commentQuery));
    }

    @PostMapping("post")
    public Result postComment(@RequestBody @Validated CommentPostVO commentPostDTO) {
        User userInfo = JwtHelper.getUserInfo();
        commentPostDTO.setUserId(userInfo.getId());
        return Result.success(commentService.create(commentPostDTO.toPO()));
    }

}
