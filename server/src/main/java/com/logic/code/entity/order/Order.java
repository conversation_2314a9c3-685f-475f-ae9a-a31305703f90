package com.logic.code.entity.order;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.logic.code.common.enmus.OrderStatusEnum;
import com.logic.code.common.enmus.PayStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@TableName( "weshop_order")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Order {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String orderSn;

    private Integer userId;

    private OrderStatusEnum orderStatus;

    private Short shippingStatus;

    private PayStatusEnum payStatus;

    private String consignee;

    private Short country;

    private Short province;

    private Short city;

    private Short district;

    private String address;

    private String mobile;

    private String postscript;

    private BigDecimal shippingFee;

    private String payName;

    private Byte payId;

    /**
     * 实际需要支付的金额
     */
    private BigDecimal actualPrice;

    private Integer integral;

    private BigDecimal integralMoney;

    /**
     * 订单总价
     */
    private BigDecimal orderPrice;

    /**
     * 商品总价
     */
    private BigDecimal goodsPrice;

    private Date createTime;

    private Date confirmTime;

    private Date payTime;

    /**
     * 配送费用
     */
    private BigDecimal freightPrice;

    /**
     * 使用的优惠券id
     */
    private Integer couponId;

    private Integer parentId;

    private BigDecimal couponPrice;

    private String callbackStatus;

    /**
     * 微信支付交易单号
     */
    private String transactionId;

    /**
     * 商户号
     */
    private String mchid;

    /**
     * 发货状态：0-未发货，1-已发货，2-已收货
     */
    private Integer shippingInfoStatus;

    /**
     * 发货时间
     */
    private Date shippingTime;

    /**
     * 确认收货时间
     */
    private Date confirmReceiveTime;

    /**
     * 推广者用户ID
     */
    private Integer promoterId;

    /**
     * 推广佣金金额
     */
    private BigDecimal promotionCommission;

    /**
     * 佣金状态：none-无佣金，pending-待确认，confirmed-已确认
     */
    private String commissionStatus;

    /**
     * 使用的余额金额
     */
    private BigDecimal balancePrice;

    public Integer getId() {
        return id;
    }

    public Order setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public Order setOrderSn(String orderSn) {
        this.orderSn = orderSn;
        return this;
    }

    public Integer getUserId() {
        return userId;
    }

    public Order setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    public OrderStatusEnum getOrderStatus() {
        return orderStatus;
    }

    public Order setOrderStatus(OrderStatusEnum orderStatus) {
        this.orderStatus = orderStatus;
        return this;
    }

    public Short getShippingStatus() {
        return shippingStatus;
    }

    public Order setShippingStatus(Short shippingStatus) {
        this.shippingStatus = shippingStatus;
        return this;
    }

    public PayStatusEnum getPayStatus() {
        return payStatus;
    }

    public Order setPayStatus(PayStatusEnum payStatus) {
        this.payStatus = payStatus;
        return this;
    }

    public String getConsignee() {
        return consignee;
    }

    public Order setConsignee(String consignee) {
        this.consignee = consignee;
        return this;
    }

    public Short getCountry() {
        return country;
    }

    public Order setCountry(Short country) {
        this.country = country;
        return this;
    }

    public Short getProvince() {
        return province;
    }

    public Order setProvince(Short province) {
        this.province = province;
        return this;
    }

    public Short getCity() {
        return city;
    }

    public Order setCity(Short city) {
        this.city = city;
        return this;
    }

    public Short getDistrict() {
        return district;
    }

    public Order setDistrict(Short district) {
        this.district = district;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public Order setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getMobile() {
        return mobile;
    }

    public Order setMobile(String mobile) {
        this.mobile = mobile;
        return this;
    }

    public String getPostscript() {
        return postscript;
    }

    public Order setPostscript(String postscript) {
        this.postscript = postscript;
        return this;
    }

    public BigDecimal getShippingFee() {
        return shippingFee;
    }

    public Order setShippingFee(BigDecimal shippingFee) {
        this.shippingFee = shippingFee;
        return this;
    }

    public String getPayName() {
        return payName;
    }

    public Order setPayName(String payName) {
        this.payName = payName;
        return this;
    }

    public Byte getPayId() {
        return payId;
    }

    public Order setPayId(Byte payId) {
        this.payId = payId;
        return this;
    }

    public BigDecimal getActualPrice() {
        return actualPrice;
    }

    public Order setActualPrice(BigDecimal actualPrice) {
        this.actualPrice = actualPrice;
        return this;
    }

    public Integer getIntegral() {
        return integral;
    }

    public Order setIntegral(Integer integral) {
        this.integral = integral;
        return this;
    }

    public BigDecimal getIntegralMoney() {
        return integralMoney;
    }

    public Order setIntegralMoney(BigDecimal integralMoney) {
        this.integralMoney = integralMoney;
        return this;
    }

    public BigDecimal getOrderPrice() {
        return orderPrice;
    }

    public Order setOrderPrice(BigDecimal orderPrice) {
        this.orderPrice = orderPrice;
        return this;
    }

    public BigDecimal getGoodsPrice() {
        return goodsPrice;
    }

    public Order setGoodsPrice(BigDecimal goodsPrice) {
        this.goodsPrice = goodsPrice;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public Order setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public Order setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
        return this;
    }

    public Date getPayTime() {
        return payTime;
    }

    public Order setPayTime(Date payTime) {
        this.payTime = payTime;
        return this;
    }

    public BigDecimal getFreightPrice() {
        return freightPrice;
    }

    public Order setFreightPrice(BigDecimal freightPrice) {
        this.freightPrice = freightPrice;
        return this;
    }

    public Integer getCouponId() {
        return couponId;
    }

    public Order setCouponId(Integer couponId) {
        this.couponId = couponId;
        return this;
    }

    public Integer getParentId() {
        return parentId;
    }

    public Order setParentId(Integer parentId) {
        this.parentId = parentId;
        return this;
    }

    public BigDecimal getCouponPrice() {
        return couponPrice;
    }

    public Order setCouponPrice(BigDecimal couponPrice) {
        this.couponPrice = couponPrice;
        return this;
    }

    public String getCallbackStatus() {
        return callbackStatus;
    }

    public Order setCallbackStatus(String callbackStatus) {
        this.callbackStatus = callbackStatus;
        return this;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public Order setTransactionId(String transactionId) {
        this.transactionId = transactionId;
        return this;
    }

    public String getMchid() {
        return mchid;
    }

    public Order setMchid(String mchid) {
        this.mchid = mchid;
        return this;
    }

    public Integer getShippingInfoStatus() {
        return shippingInfoStatus;
    }

    public Order setShippingInfoStatus(Integer shippingInfoStatus) {
        this.shippingInfoStatus = shippingInfoStatus;
        return this;
    }

    public Date getShippingTime() {
        return shippingTime;
    }

    public Order setShippingTime(Date shippingTime) {
        this.shippingTime = shippingTime;
        return this;
    }

    public Date getConfirmReceiveTime() {
        return confirmReceiveTime;
    }

    public Order setConfirmReceiveTime(Date confirmReceiveTime) {
        this.confirmReceiveTime = confirmReceiveTime;
        return this;
    }

    public Integer getPromoterId() {
        return promoterId;
    }

    public Order setPromoterId(Integer promoterId) {
        this.promoterId = promoterId;
        return this;
    }

    public BigDecimal getPromotionCommission() {
        return promotionCommission;
    }

    public Order setPromotionCommission(BigDecimal promotionCommission) {
        this.promotionCommission = promotionCommission;
        return this;
    }

    public String getCommissionStatus() {
        return commissionStatus;
    }

    public Order setCommissionStatus(String commissionStatus) {
        this.commissionStatus = commissionStatus;
        return this;
    }

    public BigDecimal getBalancePrice() {
        return balancePrice;
    }

    public Order setBalancePrice(BigDecimal balancePrice) {
        this.balancePrice = balancePrice;
        return this;
    }
}
