# 单商品订单优惠券价格计算修复

## 问题描述
在单商品订单（付费商品，type=1）中，当选择优惠券后，价格计算逻辑未实现，导致：
1. 优惠券选择了但没有实际抵扣
2. 页面显示选择了优惠券，但价格没有变化
3. 用户体验差，以为优惠券生效了但实际没有

## 问题根源
在`getCheckoutInfo`方法的单商品订单处理逻辑中，有以下代码：
```javascript
if (that.data.couponId > 0) {
  // 这里应该调用API计算优惠券抵扣，暂时先不抵扣
  console.log('付费商品订单选择了优惠券，需要重新计算价格');
}
```

这个TODO逻辑没有实现，导致优惠券选择后价格不会重新计算。

## 修复方案

### 1. 分离单商品订单和购物车订单的价格计算逻辑

**问题分析**：
- 单商品订单使用`api.OrderCardSubmit`提交
- 购物车订单使用`api.OrderSubmit`提交  
- 但价格计算都使用`api.CartAdvancedCheckout`（基于购物车数据）

**解决方案**：
- 购物车订单继续使用`api.CartAdvancedCheckout`
- 单商品订单使用本地计算逻辑`calculateSingleGoodsPrice`

### 2. 实现单商品订单的优惠券价格计算

```javascript
// 计算单商品订单价格（包含优惠券抵扣）
calculateSingleGoodsPrice() {
  const goodsTotalPrice = this.data.goodsTotalPrice;
  const freightPrice = this.data.freightPrice || 0;
  let couponPrice = 0;
  
  // 如果选择了优惠券，计算优惠券抵扣
  if (this.data.couponId > 0 && this.data.checkedCoupon) {
    const coupon = this.data.checkedCoupon;
    
    // 检查优惠券是否满足使用条件
    if (coupon.minAmount && goodsTotalPrice >= coupon.minAmount) {
      couponPrice = Math.min(coupon.amount || 0, goodsTotalPrice);
    }
  }
  
  // 重新计算订单总价和实付金额
  const orderTotalPrice = goodsTotalPrice + freightPrice - couponPrice;
  const actualPrice = Math.max(0, orderTotalPrice);
  
  // 更新页面数据
  this.setData({
    couponPrice: couponPrice,
    orderTotalPrice: orderTotalPrice,
    actualPrice: actualPrice
  });
}
```

### 3. 修复优惠券对象构建逻辑

**原问题**：
```javascript
let checkedCoupon = [];
if (that.data.couponId > 0) {
  checkedCoupon = [{
    id: that.data.couponId,
    name: that.data.couponName
  }];
}
```

**修复后**：
```javascript
let checkedCoupon = null;
if (that.data.couponId > 0) {
  // 从可用优惠券列表中查找选中的优惠券
  const selectedCoupon = availableCoupons.find(coupon => coupon.id === that.data.couponId);
  if (selectedCoupon) {
    checkedCoupon = selectedCoupon;
  } else {
    // 创建基本的优惠券对象
    checkedCoupon = {
      id: that.data.couponId,
      name: that.data.couponName || '优惠券',
      amount: 0,
      minAmount: 0
    };
  }
}
```

### 4. 优化recalculatePrice方法

```javascript
recalculatePrice() {
  // 礼券兑换订单跳过计算
  if (this.data.isVoucherOrder && this.data.type == 0) {
    return;
  }

  // 对于单商品订单，使用本地计算逻辑
  if (this.data.goodsId > 0 && this.data.type == 1) {
    this.calculateSingleGoodsPrice();
    return;
  }

  // 购物车订单使用API计算
  // ... 原有的API调用逻辑
}
```

## 修改的文件

### 1. app/wjhx/pages/shopping/checkout/checkout.js

**主要修改**：
1. 添加`calculateSingleGoodsPrice`方法
2. 修改`recalculatePrice`方法，区分单商品和购物车订单
3. 修复单商品订单的优惠券对象构建逻辑
4. 在数据设置完成后自动触发价格重新计算

## 测试步骤

### 测试用例1：单商品付费订单选择优惠券
1. 进入商品详情页，选择付费商品（非礼券商品）
2. 点击"立即购买"，进入checkout页面
3. 点击优惠券卡片，选择一张满足条件的优惠券
4. 确认选择后返回checkout页面
5. 验证：
   - 优惠券卡片显示选中的优惠券名称
   - 费用明细中显示优惠券抵扣金额
   - 实付金额 = 商品价格 + 运费 - 优惠券抵扣

### 测试用例2：优惠券不满足使用条件
1. 选择价格较低的商品（如30元）
2. 尝试选择高门槛优惠券（如满100减20）
3. 验证：
   - 优惠券应该在"不可用"列表中
   - 如果强制选择，应该不产生抵扣效果

### 测试用例3：优惠券抵扣计算正确性
1. 选择价格100元的商品
2. 选择"满50减10"的优惠券
3. 验证：
   - 优惠券抵扣：10元
   - 实付金额：90元（假设无运费）

### 测试用例4：与礼券兑换订单的区别
1. 测试礼券兑换订单（type=0）：应该跳过价格计算
2. 测试付费商品订单（type=1）：应该正确计算优惠券抵扣
3. 测试购物车订单：应该使用API计算

### 测试用例5：页面刷新后数据保持
1. 在单商品订单中选择优惠券
2. 刷新页面或重新进入
3. 验证：
   - 优惠券选择状态保持
   - 价格计算正确
   - 抵扣金额显示正确

## 预期结果

### 成功标准
1. ✅ 单商品付费订单能够正确计算优惠券抵扣
2. ✅ 优惠券选择后价格立即更新
3. ✅ 费用明细正确显示抵扣金额
4. ✅ 不满足条件的优惠券不产生抵扣
5. ✅ 与购物车订单和礼券订单逻辑分离

### 调试信息检查
控制台应该显示：
```
单商品付费订单选择了优惠券，开始重新计算价格
开始计算单商品订单价格: {goodsTotalPrice: 100, couponId: 1, checkedCoupon: {...}}
优惠券抵扣计算: {couponAmount: 10, minAmount: 50, goodsTotalPrice: 100, couponPrice: 10}
单商品订单价格计算完成: {goodsTotalPrice: 100, freightPrice: 0, couponPrice: 10, orderTotalPrice: 90, actualPrice: 90}
```

## 故障排除

### 问题1：优惠券选择后价格没有变化
- 检查`calculateSingleGoodsPrice`方法是否被调用
- 确认优惠券对象是否正确构建
- 检查优惠券是否满足使用条件

### 问题2：优惠券抵扣金额错误
- 检查优惠券的amount和minAmount字段
- 确认商品总价是否正确
- 验证抵扣计算逻辑

### 问题3：页面显示异常
- 检查setData是否正确执行
- 确认WXML中的数据绑定
- 验证数据格式是否正确

### 问题4：与其他订单类型冲突
- 确认订单类型判断逻辑
- 检查是否正确区分单商品和购物车订单
- 验证礼券订单是否正确跳过计算

## 回归测试

修复完成后，还需要测试以下功能确保没有破坏：
1. 购物车订单的优惠券功能
2. 礼券兑换订单的免费逻辑
3. 积分抵扣功能
4. 余额抵扣功能
5. 地址选择功能
6. 订单提交功能

## 总结

通过实现专门的单商品订单价格计算逻辑，解决了优惠券选择后价格不更新的问题。关键改进包括：

1. **逻辑分离**：区分单商品订单和购物车订单的处理逻辑
2. **本地计算**：单商品订单使用本地计算，避免API依赖问题
3. **实时更新**：优惠券选择后立即重新计算价格
4. **数据完整性**：确保优惠券对象包含完整的计算所需信息

这些修复确保了单商品订单的优惠券功能能够正常工作，提升了用户体验。