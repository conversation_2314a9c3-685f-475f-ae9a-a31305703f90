# 优惠券价格计算问题调试指南

## 问题描述
选择优惠券后，页面底部实付金额价格未发生变化。

## 调试步骤

### 1. 检查前端数据流
在微信开发者工具控制台中查看以下日志：

#### 优惠券选择页面
```
加载的优惠券数据: {availableCoupons: [...], unavailableCoupons: [...]}
选择优惠券: {id: 1, name: "10元消费券", amount: 10, ...}
确认选择优惠券: {selectedCouponId: 1, selectedCoupon: {...}}
设置选中的优惠券: {id: 1, name: "10元消费券", ...}
更新checkout页面数据: {couponId: 1, checkedCoupon: {...}}
```

#### checkout页面
```
checkout页面onShow，当前数据: {couponId: 1, checkedCoupon: {...}}
跳过重新加载checkout数据，但会重新计算价格
检测到优惠券选择，重新计算价格
调用重新计算价格方法，当前couponId: 1
重新计算价格请求参数: {addressId: "1", couponId: 1, usePoints: 0, useBalance: 0}
重新计算价格后更新数据: {actualPrice: 90, couponPrice: 10, ...}
```

### 2. 检查后端日志
在后端日志中查看：
```
查找优惠券ID: 1, 可用优惠券数量: 3
找到优惠券: 10元消费券, 抵扣金额: 10.00
```

### 3. 验证API响应
在网络面板中检查`/weshop-wjhx/wechat/cart/advanced-checkout`请求：

#### 请求参数
```json
{
  "addressId": "1",
  "couponId": 1,
  "usePoints": 0,
  "useBalance": 0
}
```

#### 响应数据
```json
{
  "success": true,
  "data": {
    "actualPrice": 90.00,
    "couponPrice": 10.00,
    "goodsTotalPrice": 100.00,
    "checkedCoupon": {
      "id": 1,
      "name": "10元消费券",
      "amount": 10.00
    }
  }
}
```

### 4. 检查页面数据绑定
验证以下数据是否正确更新：
- `this.data.actualPrice` - 实付金额
- `this.data.couponPrice` - 优惠券抵扣金额
- `this.data.checkedCoupon` - 选中的优惠券信息

### 5. 检查页面显示
验证WXML中的数据绑定：
```xml
<!-- 费用明细中的优惠券抵扣 -->
<view class="price-item discount-item" wx:if="{{couponPrice > 0}}">
    <text class="price-label">优惠券</text>
    <text class="price-value discount-value">-¥{{format.formatPrice(couponPrice)}}</text>
</view>

<!-- 底部实付金额 -->
<text class="amount-value">{{actualPrice}}</text>
```

## 可能的问题和解决方案

### 问题1：优惠券ID传递错误
**症状**：后端日志显示"未找到优惠券ID"
**解决方案**：
- 检查优惠券选择页面的ID类型转换
- 确保`parseInt(this.data.selectedCouponId)`正确执行

### 问题2：页面数据未更新
**症状**：API返回正确，但页面显示未变化
**解决方案**：
- 检查`setData`调用是否成功
- 验证数据绑定路径是否正确

### 问题3：时序问题
**症状**：数据更新和价格计算时序不对
**解决方案**：
- 使用`setTimeout`确保数据更新完成后再计算价格
- 在`recalculatePrice`中添加loading状态

### 问题4：后端优惠券查找失败
**症状**：后端找不到对应的优惠券
**解决方案**：
- 检查优惠券是否在可用列表中
- 验证优惠券是否满足使用条件

## 测试用例

### 测试用例1：基本优惠券使用
1. 商品总价：100元
2. 选择10元满50减券
3. 预期结果：实付金额90元

### 测试用例2：不满足条件的优惠券
1. 商品总价：30元
2. 尝试选择50元满100减券
3. 预期结果：优惠券在不可用列表中

### 测试用例3：取消优惠券
1. 先选择优惠券
2. 再选择"不使用优惠券"
3. 预期结果：实付金额恢复原价

## 手动测试方法

### 方法1：控制台手动触发
在checkout页面的控制台中执行：
```javascript
// 获取当前页面实例
const pages = getCurrentPages();
const currentPage = pages[pages.length - 1];

// 手动设置优惠券ID并重新计算
currentPage.setData({couponId: 1});
currentPage.recalculatePrice();
```

### 方法2：添加测试按钮
在checkout页面临时添加测试按钮：
```xml
<button bindtap="manualRecalculate">重新计算价格</button>
```

## 修复验证

修复完成后，验证以下功能：
1. ✅ 选择优惠券后实付金额正确减少
2. ✅ 费用明细显示优惠券抵扣项目
3. ✅ 取消优惠券后价格恢复
4. ✅ 与积分、余额抵扣组合使用正常
5. ✅ 页面跳转流畅，无数据丢失