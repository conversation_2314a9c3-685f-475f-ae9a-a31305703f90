# availableCoupons显示为0的问题排查指南

## 问题描述
checkout页面的优惠券卡片显示"0张可用"，但实际上用户应该有可用的优惠券。

## 可能的原因

### 1. 数据库中没有优惠券数据
- **症状**: 后端API返回空数组
- **排查**: 检查数据库中是否有测试优惠券数据

### 2. 优惠券状态或有效期问题
- **症状**: 有优惠券数据但状态不正确
- **排查**: 检查优惠券的status、start_time、end_time字段

### 3. 优惠券最低消费金额限制
- **症状**: 优惠券存在但不满足使用条件
- **排查**: 检查coupon.minAmount与订单金额的关系

### 4. 前端数据处理问题
- **症状**: 后端返回正确但前端显示错误
- **排查**: 检查前端数据绑定和处理逻辑

### 5. 单商品订单流程缺少优惠券获取
- **症状**: 单商品订单没有获取优惠券列表
- **排查**: 检查是否进入了单商品流程

## 排查步骤

### 步骤1: 检查数据库数据
```sql
-- 查看用户优惠券数据
SELECT id, user_id, title, amount, min_amount, status, 
       DATE_FORMAT(start_time, '%Y-%m-%d') as start_date,
       DATE_FORMAT(end_time, '%Y-%m-%d') as end_date
FROM weshop_user_coupon 
WHERE user_id = 1 AND status = 0
ORDER BY create_time DESC;
```

### 步骤2: 测试优惠券API
点击"调试优惠券数据"按钮，查看：
- API是否正常返回数据
- 返回的优惠券数量
- 筛选后的可用优惠券数量

### 步骤3: 检查订单流程
点击"查看当前数据状态"按钮，确认：
- 是否进入了正确的订单流程
- 商品总价是否正确
- availableCoupons数组长度

### 步骤4: 检查网络请求
在开发者工具的网络面板中查看：
- `/weshop-wjhx/wechat/cart/advanced-checkout` 的响应
- `/weshop-wjhx/wechat/coupon/list` 的响应

## 修复方案

### 方案1: 确保数据库有测试数据
```sql
-- 执行测试数据脚本
source database/test_coupon_data.sql;
```

### 方案2: 修复单商品订单流程
已添加优惠券获取逻辑：
```javascript
// 单商品订单也需要获取优惠券列表
util.request(api.CouponList, {
  status: 'available',
  page: 1,
  size: 100
}).then(function (couponRes) {
  let availableCoupons = [];
  if (couponRes.success && couponRes.data) {
    availableCoupons = couponRes.data.filter(coupon => {
      return coupon.minAmount <= goodsTotalPrice;
    });
  }
  // 更新页面数据...
});
```

### 方案3: 检查优惠券筛选条件
确保优惠券的最低消费金额不超过订单金额：
```javascript
const availableCoupons = allCoupons.filter(coupon => {
  return coupon.minAmount <= orderAmount;
});
```

### 方案4: 修复前端数据绑定
确保WXML中正确绑定数据：
```xml
<text class="coupon-count">
  {{availableCoupons ? availableCoupons.length : 0}}张可用
</text>
```

## 调试工具

### 1. 调试按钮
- "查看当前数据状态": 显示基本订单信息和优惠券数据
- "调试优惠券数据": 直接测试优惠券API

### 2. 控制台日志
查看以下关键日志：
```
优惠券数据详情: {
  res.data.couponList: [...],
  res.data.availableCoupons: [...],
  最终availableCoupons: [...],
  数组长度: 3
}
```

### 3. 网络面板
检查API响应格式：
```json
{
  "success": true,
  "data": {
    "availableCoupons": [
      {
        "id": 1,
        "title": "10元消费券",
        "amount": 10.00,
        "minAmount": 50.00
      }
    ]
  }
}
```

## 预期结果

修复后应该看到：
- 优惠券卡片显示正确的可用数量
- 点击优惠券卡片能进入选择页面
- 优惠券选择页面显示可用优惠券列表

## 常见问题

### Q: 为什么单商品订单没有优惠券？
A: 单商品订单流程之前没有获取优惠券列表，现已修复。

### Q: 为什么优惠券API返回数据但页面显示0？
A: 可能是筛选条件问题，检查minAmount与订单金额的关系。

### Q: 如何确认优惠券数据是否正确？
A: 使用调试按钮直接测试API，查看返回的原始数据。