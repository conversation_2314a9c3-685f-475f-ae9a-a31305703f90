# 推广用户详情页面组件路径修复

## 问题描述
在创建推广用户详情页面时，`custom-navbar` 组件的路径配置错误，导致页面无法正常加载。

## 错误信息
```
pages/ucenter/promotion-user-detail/promotion-user-detail.json: ["usingComponents"]["custom-navbar"]: "/components/custom-navbar/custom-navbar", component not found in the path
```

## 问题原因
在 `app/wjhx/pages/ucenter/promotion-user-detail/promotion-user-detail.json` 文件中，`custom-navbar` 组件的路径配置错误：

**错误配置：**
```json
{
  "usingComponents": {
    "custom-navbar": "/components/custom-navbar/custom-navbar"
  }
}
```

**正确配置：**
```json
{
  "usingComponents": {
    "custom-navbar": "/components/custom-navbar/index"
  }
}
```

## 修复内容

### 1. 修复组件路径
- **文件**: `app/wjhx/pages/ucenter/promotion-user-detail/promotion-user-detail.json`
- **修改**: 将组件路径从 `/components/custom-navbar/custom-navbar` 改为 `/components/custom-navbar/index`

### 2. 添加导航样式配置
- **添加**: `"navigationStyle": "custom"` - 使用自定义导航栏
- **添加**: `"backgroundColor": "#f8fafc"` - 设置页面背景色

### 3. 最终配置
```json
{
  "navigationStyle": "custom",
  "enablePullDownRefresh": true,
  "usingComponents": {
    "custom-navbar": "/components/custom-navbar/index"
  },
  "backgroundColor": "#f8fafc"
}
```

## 验证修复
1. **组件存在性验证**: 确认 `/components/custom-navbar/index.js` 等文件存在
2. **路径一致性验证**: 与其他页面的配置保持一致
3. **功能验证**: 页面能正常加载和显示自定义导航栏

## 参考配置
其他使用 `custom-navbar` 组件的页面配置：
- `pages/ucenter/promotion-detail/promotion-detail.json`
- `pages/ucenter/earnings/earnings.json`
- `pages/ucenter/points/points.json`
- `pages/ucenter/recharge/recharge.json`

所有这些页面都使用相同的组件路径：`/components/custom-navbar/index`

## 修复结果
✅ 组件路径修复完成
✅ 页面配置完善
✅ 推广用户详情页面现在可以正常加载和使用自定义导航栏

这个修复确保了推广用户详情页面能够正常使用自定义导航栏组件，与应用的整体设计风格保持一致。