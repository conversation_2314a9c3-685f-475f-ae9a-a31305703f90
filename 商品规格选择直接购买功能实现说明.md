# 商品规格选择直接购买功能实现说明

## 功能概述

实现了点击"立即购买"和"加入购物车"按钮时，如果商品有规格需要选择，会自动弹出规格选择弹窗，用户确认规格后直接执行相应的购买操作。

## 实现细节

### 1. 数据结构修改

在 `app/wjhx/pages/goods/goods.js` 中添加了购买类型标记：

```javascript
data: {
  // ... 其他数据
  purchaseType: ''  // 购买类型：'addToCart' 或 'instantlyBuy'
}
```

### 2. 购买流程优化

#### 立即购买流程：
1. 用户点击"立即购买"按钮
2. 检查是否需要选择规格
3. 如果需要选择规格且未完整选择：
   - 设置 `purchaseType` 为 `'instantlyBuy'`
   - 自动打开规格选择弹窗
   - 提示用户选择规格
4. 用户选择规格后点击确认按钮
5. 自动执行立即购买操作，跳转到结账页面

#### 加入购物车流程：
1. 用户点击"加入购物车"按钮
2. 检查是否需要选择规格
3. 如果需要选择规格且未完整选择：
   - 设置 `purchaseType` 为 `'addToCart'`
   - 自动打开规格选择弹窗
   - 提示用户选择规格
4. 用户选择规格后点击确认按钮
5. 自动执行加入购物车操作

### 3. 用户界面优化

规格选择弹窗的确认按钮文本会根据购买类型动态显示：
- 立即购买：显示"立即购买 (X件)"
- 加入购物车：显示"加入购物车 (X件)"
- 普通选择：显示"确认选择 (X件)"

### 4. 核心函数说明

#### `addToCart(e)`
- 处理加入购物车按钮点击
- 检查规格选择状态
- 设置购买类型并打开规格弹窗（如需要）

#### `instantlyBuy(e)`
- 处理立即购买按钮点击
- 检查规格选择状态
- 设置购买类型并打开规格弹窗（如需要）

#### `executeAddToCart(goodsId)`
- 执行实际的加入购物车操作
- 获取选中的产品ID和数量
- 调用购物车服务

#### `executeInstantlyBuy(goodsId)`
- 执行实际的立即购买操作
- 获取选中的产品ID和数量
- 跳转到结账页面

#### `closeAttr()`
- 关闭规格选择弹窗
- 根据购买类型执行相应操作
- 清除购买类型标记

## 用户体验改进

1. **流程简化**：用户不需要先选择规格再点击购买，可以直接点击购买按钮
2. **智能提示**：系统会自动检测是否需要选择规格并引导用户
3. **一键操作**：选择规格后自动执行购买操作，无需再次点击
4. **清晰反馈**：按钮文本明确显示将要执行的操作

## 兼容性说明

- 对于无规格商品，保持原有的直接购买流程
- 对于已选择完整规格的商品，直接执行购买操作
- 向后兼容原有的规格选择功能

## 测试建议

1. 测试无规格商品的购买流程
2. 测试有规格商品的购买流程
3. 测试规格选择后的自动购买功能
4. 测试按钮文本的动态显示
5. 测试取消规格选择的情况