# 购物车页面频繁跳转登录问题解决方案

## 问题分析

### 1. 主要问题
- 购物车页面有时会触发频繁跳转到登录页面
- 登录页面显示登录成功，但跳转到购物车页面依然提示需要登录
- 用户体验差，造成登录循环

### 2. 根本原因

#### 2.1 Token过期处理不当
- JWT Token有效期为7天，过期后服务器返回错误码616
- 前端只检查`userInfo`存在性，未验证Token有效性
- Token过期时，前端仍认为用户已登录

#### 2.2 登录状态检查不一致
- 前端：只检查`wx.getStorageSync('userInfo')`
- 服务器：需要验证Token的有效性和完整性
- 存在userInfo存在但Token无效的情况

#### 2.3 错误处理逻辑问题
- 购物车页面每次`onShow`都调用API
- Token过期时会弹出登录提示，造成频繁弹窗
- 登录成功后的跳转时机不当

#### 2.4 数据保存和验证问题
- 登录成功后数据保存可能不完整
- 缺少保存后的验证机制

## 解决方案

### 1. 优化购物车页面登录检查

#### 1.1 增强登录状态检查
```javascript
// 检查userInfo和token都存在且有效
const userInfo = wx.getStorageSync('userInfo');
const token = wx.getStorageSync('token');

if (!userInfo || !token) {
  console.log('用户未登录，跳过购物车加载');
  return;
}
```

#### 1.2 改进错误处理
```javascript
// 检查是否是登录错误
if (res.code === 616) {
  console.log('Token已过期，需要重新登录');
  // 清除过期的登录信息
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('token');
  return;
}
```

### 2. 优化util.js请求处理

#### 2.1 避免购物车页面频繁弹窗
```javascript
// 避免在购物车页面频繁弹窗
if (url === 'pages/cart/cart') {
  console.log('购物车页面登录过期，静默处理');
  // 返回登录错误，但不弹窗
  resolve({
    code: 616,
    msg: '请先登录',
    success: false
  });
  return;
}
```

#### 2.2 清理过期登录信息
```javascript
// 清除过期的登录信息
wx.removeStorageSync('userInfo');
wx.removeStorageSync('token');

// 更新全局数据
const app = getApp();
app.globalData.userInfo = {
  nickname: '点击登录',
  avatar: 'http://yanxuan.nosdn.127.net/8945ae63d940cc42406c3f67019c5cb6.png'
};
app.globalData.token = '';
```

### 3. 增强登录状态检查函数

#### 3.1 更严格的验证
```javascript
function checkLoginStatus(autoNavigate = true, showModal = true) {
  const userInfo = wx.getStorageSync('userInfo');
  const token = wx.getStorageSync('token');
  
  // 更严格的登录状态检查
  let isLoggedIn = false;
  
  if (userInfo && token) {
    // 检查userInfo是否是有效对象
    let validUserInfo = false;
    try {
      if (typeof userInfo === 'string') {
        const parsedUserInfo = JSON.parse(userInfo);
        validUserInfo = !!(parsedUserInfo && (parsedUserInfo.id || parsedUserInfo.userId));
      } else if (typeof userInfo === 'object') {
        validUserInfo = !!(userInfo.id || userInfo.userId);
      }
    } catch (e) {
      validUserInfo = false;
    }
    
    // 检查token是否是有效字符串
    const validToken = typeof token === 'string' && token.length > 0 && token !== 'undefined';
    
    isLoggedIn = validUserInfo && validToken;
  }
  
  return isLoggedIn;
}
```

### 4. 优化登录成功处理

#### 4.1 增加保存验证
```javascript
completeLoginSuccess: function(userInfo, token) {
  // 保存用户信息和token
  try {
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('token', token);
    console.log('用户信息和Token保存成功');
  } catch (e) {
    console.error('保存用户信息失败:', e);
    return;
  }
  
  // 验证登录状态是否正确保存
  const savedUserInfo = wx.getStorageSync('userInfo');
  const savedToken = wx.getStorageSync('token');
  
  if (!savedUserInfo || !savedToken) {
    console.error('登录信息保存验证失败');
    return;
  }
}
```

#### 4.2 缩短跳转延迟
```javascript
// 缩短延迟时间，确保快速跳转
setTimeout(() => {
  util.toPage(that.data.backUrl, that.data.backParams);
}, 1000); // 从1500ms改为1000ms
```

### 5. 调试工具

创建了登录状态调试页面 `/pages/test/login-debug`，可以：
- 实时查看登录状态
- 测试购物车API调用
- 清除登录信息
- 复制调试信息

## 使用建议

### 1. 测试步骤
1. 访问调试页面：`/pages/test/login-debug`
2. 查看当前登录状态
3. 测试购物车API调用
4. 模拟登录过期场景

### 2. 监控要点
- 关注控制台日志中的登录状态检查信息
- 观察Token过期时的处理流程
- 验证登录成功后的跳转是否正常

### 3. 预防措施
- 定期检查Token有效期
- 在关键操作前验证登录状态
- 提供用户友好的登录提示

## 技术细节

### 1. JWT Token管理
- 有效期：7天
- 存储位置：`wx.getStorageSync('token')`
- 验证方式：服务器端解析JWT

### 2. 登录状态存储
- userInfo：用户基本信息
- token：JWT认证令牌
- 全局数据：app.globalData

### 3. 错误码说明
- 616：需要登录
- 其他错误码按原有逻辑处理

## 预期效果

1. **消除频繁跳转**：购物车页面不再频繁跳转到登录页面
2. **改善用户体验**：登录成功后能正确返回原页面
3. **增强稳定性**：登录状态检查更加可靠
4. **便于调试**：提供专门的调试工具

通过以上改进，应该能够有效解决购物车页面的登录跳转问题。
