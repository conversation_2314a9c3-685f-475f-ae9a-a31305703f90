# 优惠券选择后价格变成0的问题排查指南

## 问题描述
选择了10元优惠券后，计算后价格变成了0，而不是正确的减去10元。

## 可能的原因

### 1. 订单类型误判
- **症状**: 普通购物车结算被识别为礼券兑换订单
- **原因**: `type == 0` 时系统认为是免费的礼券兑换订单
- **结果**: `finalActualPrice` 被设置为 0

### 2. 单商品流程被错误触发
- **症状**: 有 `goodsId` 参数导致进入单商品处理流程
- **原因**: 普通购物车结算不应该有 `goodsId` 参数
- **结果**: 使用了错误的价格计算逻辑

### 3. 优惠券抵扣金额计算错误
- **症状**: `couponPrice` 等于商品总价而不是优惠券面额
- **原因**: 后端API返回了错误的抵扣金额
- **结果**: 优惠券抵扣了全部金额

## 排查步骤

### 步骤1: 检查订单参数
点击"查看当前数据状态"按钮，查看以下信息：
```
基本信息: {
  goodsId: 0,        // 应该为0（购物车结算）
  type: undefined,   // 应该为undefined或0
  isVoucherOrder: false,  // 应该为false
  couponId: 1        // 你选择的优惠券ID
}
```

### 步骤2: 检查价格计算流程
查看控制台日志：
```
getCheckoutInfo订单类型检查: {
  是否使用单商品流程: false  // 应该为false
}
```

### 步骤3: 检查API响应
在网络面板中查看 `/weshop-wjhx/wechat/cart/advanced-checkout` 的响应：
```json
{
  "actualPrice": 90.00,     // 应该是原价减去优惠券金额
  "couponPrice": 10.00,     // 应该是优惠券面额
  "goodsTotalPrice": 100.00 // 商品总价
}
```

### 步骤4: 检查前端数据更新
查看"重新计算价格后更新数据"日志：
```
{
  actualPrice: 90,    // 实付金额
  couponPrice: 10,    // 优惠券抵扣
  orderTotalPrice: 90 // 订单总价
}
```

## 修复方案

### 方案1: 确保使用购物车流程
如果你是从购物车进入的，确保：
- `goodsId` 为 0 或 undefined
- `type` 为 undefined 或不是 0/1
- 使用购物车结算流程而不是单商品流程

### 方案2: 修复订单类型判断
```javascript
// 只有明确的单商品订单才使用单商品流程
if (that.data.goodsId > 0 && (that.data.type == 0 || that.data.type == 1)) {
  // 单商品流程
} else {
  // 购物车流程
}
```

### 方案3: 检查后端API
确保后端正确计算优惠券抵扣：
- 优惠券抵扣金额 = 优惠券面额（不是商品总价）
- 实付金额 = 商品总价 - 优惠券抵扣金额

## 临时解决方案

如果问题仍然存在，可以：

1. **使用强制重新计算**：
   - 点击"强制重新计算(忽略订单类型)"按钮
   - 这会直接调用购物车API重新计算价格

2. **清除页面参数**：
   ```javascript
   // 在控制台执行
   const pages = getCurrentPages();
   const currentPage = pages[pages.length - 1];
   currentPage.setData({
     goodsId: 0,
     type: undefined,
     isVoucherOrder: false
   });
   currentPage.recalculatePrice();
   ```

## 预期结果

修复后应该看到：
- 商品总价：100元
- 优惠券抵扣：-10元
- 实付金额：90元

## 测试验证

1. 选择10元优惠券
2. 查看费用明细显示"-¥10.00"
3. 实付金额显示"90.00"
4. 提交订单时金额正确