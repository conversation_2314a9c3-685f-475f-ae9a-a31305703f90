# 礼券订单判断逻辑修复

## 问题描述
礼券订单判断逻辑有误，导致普通的购物车结算被错误识别为礼券订单，从而跳过了价格计算。

## 原始问题
```javascript
// 错误的判断逻辑
if (goodsId > 0 && couponId > 0) {
  isVoucherOrder = true;
}

// 导致任何有商品ID和优惠券ID的情况都被判断为礼券订单
```

## 修复方案

### 1. 重新定义订单类型
- **普通购物车结算**: 没有`goodsId`参数，从购物车页面进入
- **单商品订单**: 有`goodsId`参数，包括两种子类型：
  - `type == 0`: 礼券兑换订单（免费）
  - `type == 1`: 付费商品订单（需要支付）

### 2. 修复判断逻辑
```javascript
let isVoucherOrder = false;

if (goodsId <= 0) {
  // 没有goodsId，肯定是普通购物车结算
  isVoucherOrder = false;
} else {
  // 有goodsId的情况下，根据type判断
  if (type == 0) {
    isVoucherOrder = true; // 礼券兑换订单
  } else {
    isVoucherOrder = false; // 付费商品订单，需要价格计算
  }
}
```

### 3. 修复价格计算逻辑
```javascript
// 只有礼券兑换订单（type == 0）才跳过价格计算
if (this.data.isVoucherOrder && this.data.type == 0) {
  console.log('礼券兑换订单（免费），跳过价格重新计算');
  return;
}
// 付费商品订单和普通购物车结算都需要价格计算
```

### 4. 统一单商品处理流程
```javascript
// 如果有goodsId，说明是单商品订单
if (that.data.goodsId > 0) {
  // 使用单商品处理流程，支持礼券兑换和付费商品
}
```

## 订单类型对照表

| 场景 | goodsId | type | isVoucherOrder | 价格计算 | 提交API |
|------|---------|------|----------------|----------|---------|
| 购物车结算 | 0 | - | false | ✅ 需要 | OrderSubmit |
| 礼券兑换 | >0 | 0 | true | ❌ 跳过 | OrderCardSubmit |
| 付费商品 | >0 | 1 | false | ✅ 需要 | OrderCardSubmit |

## 测试验证

### 1. 普通购物车结算
- 从购物车页面进入checkout
- 没有goodsId参数
- 应该正常计算优惠券抵扣

### 2. 礼券兑换订单
- 有goodsId和type=0
- isVoucherOrder=true
- 跳过价格计算，实付金额为0

### 3. 付费商品订单
- 有goodsId和type=1
- isVoucherOrder=false
- 需要价格计算，支持优惠券抵扣

## 调试日志检查

修复后应该看到以下日志：
```
页面参数: {couponId: 0, goodsId: 0, type: 0, ...}
订单类型判断: {isVoucherOrder: false, ...}
getCheckoutInfo订单类型检查: {是否单商品订单: false}
订单类型检查: {是否跳过计算: false}
```

对于普通购物车结算，所有判断都应该是false，确保价格计算正常进行。

## 预期结果

修复后：
1. ✅ 普通购物车结算不会被误判为礼券订单
2. ✅ 优惠券抵扣功能正常工作
3. ✅ 价格计算逻辑正确执行
4. ✅ 不同订单类型使用正确的处理流程