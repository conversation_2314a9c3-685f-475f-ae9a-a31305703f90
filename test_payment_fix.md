# 支付金额修复验证测试

## 问题描述
在使用余额抵扣、优惠券、积分抵扣后，调用微信支付时商品价格仍然是优惠前的商品原价，需要优化，核查后端接口逻辑是否未实现。

## 🚨 新发现的问题
**优惠券抵扣金额页面显示正确，但是付款支付时显示错误，优惠券抵扣金额没有计算进去**

## 问题根因分析
通过代码审查发现问题出现在 `OrderService.submitCardOrder()` 方法中：

### 修复前的问题代码
```java
// 订单价格计算  实际价格 = 商品价格 + 运费价格 - 优惠券价格
BigDecimal orderTotalPrice = goodsTotalPrice.add(freightPrice).subtract(couponPrice);
// 减去其它支付的金额后，要实际支付的金额
BigDecimal actualPrice = orderTotalPrice.subtract(new BigDecimal(0.00));
```

**问题：** 只减去了 `new BigDecimal(0.00)`，没有减去积分抵扣和余额抵扣的金额。

### 优惠券问题代码
```java
// 获取订单使用的优惠券
BigDecimal couponPrice = BigDecimal.ZERO;
if (param.getCouponId() != null) {
    // 计算优惠券的价格 未实现  ← 这里是空实现！
}
```

**问题：** 优惠券价格计算逻辑完全没有实现，导致`couponPrice`始终为0。

### 修复后的积分和余额抵扣代码
```java
// 计算积分抵扣
Integer usePoints = param.getUsePoints();
BigDecimal pointsPrice = BigDecimal.ZERO;
if (usePoints != null && usePoints > 0) {
    // 验证用户积分是否足够
    User currentUser = userService.queryById(userInfo.getId());
    Integer userPoints = currentUser.getPoints() != null ? currentUser.getPoints() : 0;
    if (userPoints < usePoints) {
        throw new WeshopWechatException(WeshopWechatResultStatus.INSUFFICIENT_POINTS);
    }
    
    // 计算积分可抵扣的金额
    pointsPrice = pointsService.calculatePointsValue(usePoints);
}

// 计算余额抵扣
BigDecimal useBalance = param.getUseBalance();
BigDecimal balancePrice = BigDecimal.ZERO;
if (useBalance != null && useBalance.compareTo(BigDecimal.ZERO) > 0) {
    // 验证用户余额是否足够
    User currentUser = userService.queryById(userInfo.getId());
    BigDecimal userBalance = currentUser.getBalance() != null ? currentUser.getBalance() : BigDecimal.ZERO;
    if (userBalance.compareTo(useBalance) < 0) {
        throw new WeshopWechatException(WeshopWechatResultStatus.INSUFFICIENT_BALANCE);
    }
    balancePrice = useBalance;
}

// 订单价格计算  实际价格 = 商品价格 + 运费价格 - 优惠券价格 - 积分抵扣 - 余额抵扣
BigDecimal orderTotalPrice = goodsTotalPrice.add(freightPrice).subtract(couponPrice);
BigDecimal actualPrice = orderTotalPrice.subtract(pointsPrice).subtract(balancePrice);

// 确保实际支付金额不为负数
if (actualPrice.compareTo(BigDecimal.ZERO) < 0) {
    actualPrice = BigDecimal.ZERO;
}
```

### 修复后的优惠券抵扣代码
```java
// 获取订单使用的优惠券
BigDecimal couponPrice = BigDecimal.ZERO;
if (param.getCouponId() != null && param.getCouponId() > 0) {
    // 获取用户优惠券信息
    List<UserCoupon> userCoupons = userCouponService.getUserCoupons(userInfo.getId(), "available", 1, 100);
    UserCoupon selectedCoupon = userCoupons.stream()
        .filter(coupon -> coupon.getId().equals(param.getCouponId()))
        .findFirst()
        .orElse(null);

    if (selectedCoupon != null) {
        // 检查优惠券是否满足使用条件
        if (selectedCoupon.getMinAmount().compareTo(goodsTotalPrice) <= 0) {
            couponPrice = selectedCoupon.getAmount();
            log.info("单商品订单使用优惠券：{}, 抵扣金额：{}", selectedCoupon.getTitle(), couponPrice);
        } else {
            log.warn("优惠券{}不满足使用条件，最低消费：{}，订单金额：{}",
                selectedCoupon.getTitle(), selectedCoupon.getMinAmount(), goodsTotalPrice);
            throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
        }
    } else {
        log.warn("未找到可用的优惠券，ID：{}", param.getCouponId());
        throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
    }
}

// 优惠券使用逻辑
if (param.getCouponId() != null && param.getCouponId() > 0) {
    boolean couponUsed = userCouponService.useCoupon(
        userInfo.getId(),
        param.getCouponId(),
        order.getId()
    );
    if (!couponUsed) {
        throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
    }
    log.info("单商品订单{}成功使用优惠券{}", order.getId(), param.getCouponId());
}
```

## 修复内容总结

### 1. 价格计算逻辑完善
- ✅ 添加了积分抵扣计算逻辑
- ✅ 添加了余额抵扣计算逻辑
- ✅ **🆕 实现了优惠券抵扣计算逻辑**
- ✅ 更新了actualPrice计算公式
- ✅ 添加了负数保护机制

### 2. 订单信息记录完善
- ✅ 正确保存积分使用信息（integral、integralMoney）
- ✅ 正确保存余额使用信息（balancePrice）
- ✅ **🆕 正确保存优惠券使用信息（couponId、couponPrice）**
- ✅ 确保订单记录包含完整的抵扣信息

### 3. 抵扣执行逻辑
- ✅ 添加了积分实际扣减逻辑
- ✅ 添加了余额实际扣减逻辑
- ✅ **🆕 添加了优惠券实际使用逻辑**
- ✅ 使用了正确的BalanceService方法
- ✅ 添加了完整的异常处理

### 4. 数据类型修正
- ✅ 修正了balanceService.useBalanceForOrder的参数类型（Integer）
- ✅ 统一了submitOrder和submitCardOrder的余额使用逻辑
- ✅ 解决了编译错误，确保类型匹配

## 测试验证点

### 1. 单商品订单测试
- [ ] 创建单商品订单，使用积分抵扣
- [ ] 创建单商品订单，使用余额抵扣
- [ ] 创建单商品订单，同时使用积分+余额抵扣
- [ ] 验证微信支付金额是否为正确的实付金额

### 2. 购物车订单测试
- [ ] 创建购物车订单，使用积分抵扣
- [ ] 创建购物车订单，使用余额抵扣
- [ ] 创建购物车订单，同时使用优惠券+积分+余额抵扣
- [ ] 验证微信支付金额是否为正确的实付金额

### 3. 边界情况测试
- [ ] 抵扣金额大于订单金额（应该为0元）
- [ ] 积分不足的情况
- [ ] 余额不足的情况
- [ ] 抵扣后实付金额为0的情况

### 4. 数据一致性验证
- [ ] 验证订单表中的actualPrice字段
- [ ] 验证积分扣减记录
- [ ] 验证余额扣减记录
- [ ] 验证微信支付金额与actualPrice一致

## 关键修复文件

1. **server/src/main/java/com/logic/code/service/OrderService.java**
   - submitCardOrder方法：添加积分和余额抵扣计算
   - submitOrder方法：修正余额服务调用参数类型

2. **server/src/main/java/com/logic/code/service/PayService.java**
   - prepay方法：已正确使用order.getActualPrice()（无需修改）

## 编译验证

✅ **编译成功**：所有代码修改已通过Maven编译验证
- 解决了类型不匹配问题
- 确保了方法调用的正确性
- 没有语法错误或依赖问题

## 预期效果

修复后，当用户在结算页面使用积分抵扣、余额抵扣、优惠券后：

1. **前端显示**：实付金额 = 商品总价 + 运费 - 优惠券 - 积分抵扣 - 余额抵扣
2. **订单记录**：actualPrice字段保存正确的实付金额
3. **微信支付**：调用微信支付API时传递的金额为actualPrice
4. **用户体验**：支付金额与页面显示的实付金额一致

## 安全性保障

- ✅ 添加了积分余额不足的验证
- ✅ 添加了负数金额的保护
- ✅ 使用了事务性的余额扣减服务
- ✅ 完整的异常处理和回滚机制

## 修复完成状态

🎉 **修复已完成并通过编译验证**

### 核心问题解决
- ✅ `submitCardOrder`方法中的价格计算逻辑已完善
- ✅ 积分抵扣和余额抵扣逻辑已添加
- ✅ 微信支付金额现在使用正确的`actualPrice`
- ✅ 所有类型匹配问题已解决

### 下一步建议
1. **部署测试**：将修复后的代码部署到测试环境
2. **功能验证**：按照测试验证点进行完整测试
3. **生产部署**：测试通过后部署到生产环境
