# 管理员中心功能说明

## 功能概述

在用户中心的服务中心下面新增了管理员中心功能，允许管理员查看所有订单和所有用户信息。

## 权限控制

- 只有 `userLevelId == 1` 的用户才能看到管理员中心入口
- 所有管理员页面都会检查用户权限，非管理员用户会被重定向

## 新增页面

### 1. 管理员中心主页 (`/pages/ucenter/admin/admin`)
- 显示系统统计数据（总订单数、总用户数、总交易额、今日订单等）
- 提供快速导航到订单管理和用户管理
- 包含数据统计详情弹窗

### 2. 订单管理页面 (`/pages/ucenter/admin/orders/orders`)
- 显示所有订单列表
- 支持按状态筛选（全部、待支付、待发货、已发货、已完成）
- 支持搜索订单号和用户昵称
- 订单详情查看功能
- 管理员操作（发货、取消订单等）

### 3. 用户管理页面 (`/pages/ucenter/admin/users/users`)
- 显示所有用户列表
- 支持按类型筛选（全部用户、新用户、活跃用户、VIP用户）
- 支持搜索用户昵称和手机号
- 用户详情查看功能
- 用户数据统计（订单数、消费金额、积分、余额等）
- 推广关系显示

## 新增API接口

### 1. 管理员统计数据接口
- 路径：`/wechat/admin/stats`
- 方法：GET
- 返回：系统统计数据

### 2. 复用现有接口
- 订单列表：`/wechat/order/list`
- 订单详情：`/wechat/order/detail`
- 用户列表：复用现有用户相关接口

## 文件结构

```
app/wjhx/pages/ucenter/admin/
├── admin.wxml              # 管理员中心主页
├── admin.wxss              # 主页样式
├── admin.js                # 主页逻辑
├── admin.json              # 主页配置
├── orders/
│   ├── orders.wxml         # 订单管理页面
│   ├── orders.wxss         # 订单管理样式
│   ├── orders.js           # 订单管理逻辑
│   └── orders.json         # 订单管理配置
└── users/
    ├── users.wxml          # 用户管理页面
    ├── users.wxss          # 用户管理样式
    ├── users.js            # 用户管理逻辑
    └── users.json          # 用户管理配置
```

## 功能特点

### 1. 权限安全
- 多层权限检查
- 非管理员用户无法访问
- 自动重定向到登录页面

### 2. 数据展示
- 实时统计数据
- 分页加载
- 搜索和筛选功能
- 详情弹窗展示

### 3. 用户体验
- 现代化UI设计
- 渐变色彩搭配
- 流畅的动画效果
- 响应式布局

### 4. 数据处理
- 支持真实API接口
- 提供模拟数据兜底
- 错误处理机制
- 加载状态提示

## 使用方法

1. 确保用户已登录且 `userLevelId == 1`
2. 在个人中心页面的服务中心部分可以看到"管理员中心"入口
3. 点击进入管理员中心主页
4. 通过主页导航到订单管理或用户管理页面
5. 使用搜索和筛选功能查找特定数据

## 注意事项

1. 管理员中心功能仅对管理员用户可见
2. 部分功能使用模拟数据，实际使用时需要对接真实API
3. 图标文件使用了现有图标的副本，建议替换为专用的管理员图标
4. 后端接口需要添加相应的权限验证逻辑

## 扩展建议

1. 添加更多统计图表
2. 增加数据导出功能
3. 添加用户编辑功能
4. 增加订单批量操作
5. 添加系统设置管理
6. 增加日志查看功能
