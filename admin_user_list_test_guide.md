# 管理员用户列表功能测试指南

## 测试目的
验证管理员用户列表页面的接口数据展示问题已修复，确保页面能正常显示用户数据。

## 测试前提条件
1. 使用管理员账号登录（userLevelId = 1）
2. 确保有访问管理员功能的权限

## 测试步骤

### 1. 基础功能测试
1. **进入用户管理页面**
   - 从"我的"页面点击管理员入口
   - 选择"用户管理"
   - 验证页面能正常加载

2. **数据展示测试**
   - 检查用户列表是否正常显示
   - 验证用户基本信息（头像、昵称、手机号）
   - 检查用户等级标识
   - 验证在线状态显示
   - 检查订单数量、消费金额等统计数据

### 2. 筛选功能测试
1. **全部用户**
   - 点击"全部用户"标签
   - 验证显示所有用户数据

2. **新用户筛选**
   - 点击"新用户"标签
   - 验证只显示最近注册的用户

3. **活跃用户筛选**
   - 点击"活跃用户"标签
   - 验证显示在线或最近登录的用户

4. **VIP用户筛选**
   - 点击"VIP用户"标签
   - 验证只显示等级大于0的用户

### 3. 搜索功能测试
1. **昵称搜索**
   - 在搜索框输入用户昵称
   - 点击搜索按钮
   - 验证搜索结果正确

2. **手机号搜索**
   - 在搜索框输入手机号
   - 点击搜索按钮
   - 验证搜索结果正确

### 4. 分页功能测试
1. **上拉加载更多**
   - 滚动到页面底部
   - 验证能自动加载更多用户

2. **下拉刷新**
   - 在页面顶部下拉
   - 验证能刷新用户列表

### 5. 用户详情测试
1. **查看用户详情**
   - 点击用户项的"查看详情"按钮
   - 验证弹窗显示用户详细信息

2. **查看用户订单**
   - 点击"查看订单"按钮
   - 验证能跳转到用户订单页面

### 6. 权限控制测试
1. **非管理员访问**
   - 使用普通用户账号尝试访问
   - 验证显示权限不足提示

2. **未登录访问**
   - 未登录状态下尝试访问
   - 验证能正确处理权限验证

## 预期结果

### 正常情况
- 页面能正常加载用户列表
- 所有筛选和搜索功能正常工作
- 用户数据完整显示（包括统计信息）
- 分页加载功能正常
- 用户详情弹窗正常显示

### 异常情况处理
- 接口调用失败时自动使用模拟数据
- 权限不足时显示相应提示
- 网络错误时有友好的错误提示

## 模拟数据说明
当后端接口不可用时，系统会自动使用以下模拟数据：
- 张三（VIP用户，在线）
- 李四（L1用户，离线）
- 王五（L0用户，离线）
- 赵六（SVIP用户，在线）
- 孙七（L1用户，离线）

## 故障排除

### 如果页面显示空白
1. 检查管理员权限是否正确
2. 查看浏览器控制台是否有错误信息
3. 确认API配置是否正确

### 如果数据不显示
1. 检查网络连接
2. 确认后端服务是否正常运行
3. 验证模拟数据是否正确加载

### 如果筛选不工作
1. 检查筛选逻辑是否正确
2. 确认数据格式是否符合预期
3. 验证时间计算是否准确

## 测试完成标准
- [ ] 页面能正常加载
- [ ] 用户列表正常显示
- [ ] 所有筛选功能正常
- [ ] 搜索功能正常
- [ ] 分页功能正常
- [ ] 用户详情功能正常
- [ ] 权限控制正常
- [ ] 异常情况处理正常