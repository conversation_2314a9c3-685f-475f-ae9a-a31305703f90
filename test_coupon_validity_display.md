# 优惠券有效期显示测试指南

## 测试目的
验证优惠券有效期数据修复后是否能正确显示在各个页面中。

## 测试环境准备

### 1. 后端服务启动
确保后端服务正常运行，包括：
- Spring Boot 应用启动
- 数据库连接正常
- 优惠券相关接口可访问

### 2. 前端小程序
确保小程序开发工具中：
- 项目编译无错误
- 网络请求配置正确
- 用户登录状态正常

## 测试用例

### 测试用例1：优惠券列表页面
**页面路径**: `app/wjhx/pages/ucenter/coupon/coupon`

**测试步骤**:
1. 登录小程序
2. 进入个人中心
3. 点击"我的优惠券"
4. 查看优惠券列表

**预期结果**:
- 有效期显示格式：`2025-07-27 - 2025-10-25`（有开始和结束时间）
- 有效期显示格式：`至 2025-10-25`（只有结束时间）
- 有效期显示格式：`永久有效`（无时间限制）

**验证点**:
- [ ] 有效期文字正确显示
- [ ] 时间格式为 YYYY-MM-DD
- [ ] 不同状态的优惠券都能正确显示时间
- [ ] 没有显示 undefined 或空白

### 测试用例2：优惠券选择页面
**页面路径**: `app/wjhx/pages/shopping/coupon-select/coupon-select`

**测试步骤**:
1. 添加商品到购物车
2. 进入结算页面
3. 点击"选择优惠券"
4. 查看优惠券选择列表

**预期结果**:
- 可用优惠券显示正确的有效期
- 不可用优惠券也显示正确的有效期
- 有效期格式与优惠券列表页面一致

**验证点**:
- [ ] 可用优惠券有效期正确显示
- [ ] 不可用优惠券有效期正确显示
- [ ] 时间格式一致性
- [ ] 选择优惠券后返回结算页面正常

### 测试用例3：购物车结算页面
**页面路径**: `app/wjhx/pages/shopping/checkout/checkout`

**测试步骤**:
1. 在结算页面选择优惠券
2. 查看选中优惠券的显示信息
3. 验证优惠券信息的完整性

**预期结果**:
- 选中的优惠券名称正确显示
- 优惠券金额正确计算
- 如果有时间显示，格式应该正确

**验证点**:
- [ ] 优惠券名称显示正确
- [ ] 优惠券金额计算正确
- [ ] 页面无JavaScript错误

## 数据验证

### 后端API测试
使用API测试工具（如Postman）测试以下接口：

#### 1. 获取优惠券统计
```
GET /wechat/coupon/stats
Authorization: Bearer {token}
```

#### 2. 获取优惠券列表
```
GET /wechat/coupon/list?status=available&page=1&size=10
Authorization: Bearer {token}
```

**验证点**:
- [ ] 返回数据中 `remark` 字段格式为 `startTime,endTime`
- [ ] 时间格式为 `yyyy-MM-dd`
- [ ] 状态字段正确（0-可用，1-已使用，2-已过期）

### 前端数据处理验证
在浏览器开发者工具中检查：

#### 1. 网络请求
- [ ] API请求成功返回数据
- [ ] 返回的优惠券数据包含正确的时间信息

#### 2. 控制台日志
- [ ] 没有JavaScript错误
- [ ] 时间解析日志正确
- [ ] 数据格式化日志正确

## 边界情况测试

### 1. 异常数据处理
- 测试 `remark` 字段为空的情况
- 测试 `remark` 字段格式异常的情况
- 测试只有结束时间的情况

### 2. 网络异常处理
- 测试网络请求失败的情况
- 测试API返回错误的情况

### 3. 用户体验测试
- 测试页面加载性能
- 测试数据刷新功能
- 测试下拉刷新功能

## 回归测试

### 1. 相关功能验证
- [ ] 优惠券使用功能正常
- [ ] 优惠券过期状态更新正常
- [ ] 订单结算中优惠券计算正常

### 2. 其他页面验证
- [ ] 订单详情页面优惠券信息显示正常
- [ ] 个人中心页面优惠券统计正常

## 问题排查

### 常见问题及解决方案

#### 1. 有效期仍显示为空
**可能原因**:
- 后端时间格式化未生效
- 前端解析逻辑有误
- 缓存问题

**排查步骤**:
1. 检查API返回数据中的 `remark` 字段
2. 检查前端控制台是否有错误日志
3. 清除小程序缓存重新测试

#### 2. 时间格式不正确
**可能原因**:
- 后端日期格式化配置错误
- 前端时间解析逻辑错误

**排查步骤**:
1. 检查后端 SimpleDateFormat 配置
2. 检查前端 formatDate 方法实现
3. 验证时区设置

#### 3. 部分优惠券显示异常
**可能原因**:
- 数据库中优惠券数据不完整
- 不同类型优惠券处理逻辑不同

**排查步骤**:
1. 检查数据库中优惠券的 start_time 和 end_time 字段
2. 检查不同状态优惠券的处理逻辑
3. 验证优惠券创建时的时间设置

## 测试报告模板

### 测试结果记录
- 测试时间：
- 测试环境：
- 测试人员：

### 功能测试结果
| 测试用例 | 测试结果 | 问题描述 | 修复状态 |
|---------|---------|---------|---------|
| 优惠券列表页面 | ✅/❌ | | |
| 优惠券选择页面 | ✅/❌ | | |
| 购物车结算页面 | ✅/❌ | | |

### 性能测试结果
- 页面加载时间：
- API响应时间：
- 内存使用情况：

### 兼容性测试结果
- 不同设备测试结果：
- 不同网络环境测试结果：

## 总结
通过以上测试用例的执行，可以全面验证优惠券有效期显示功能的修复效果，确保用户能够正确看到优惠券的有效期信息。
