# 积分和优惠券使用状态修复说明

## 问题发现

通过代码审查发现了一个严重的安全漏洞：

### ❌ 优惠券重复使用问题
在`OrderService.submitOrder`方法中，虽然订单记录了使用的优惠券ID和抵扣金额，但是**没有调用`userCouponService.useCoupon()`方法**来更新优惠券状态。

这导致：
1. 优惠券状态始终保持为"可用"(status=0)
2. 同一张优惠券可以被重复使用
3. 用户可以无限次使用同一张优惠券

### ✅ 积分扣减正常
积分扣减逻辑是正常的：
- 正确调用了`pointsService.usePointsForOrder()`方法
- 创建积分使用记录
- 更新用户积分总数

## 修复方案

### 1. 后端代码修复

#### 修复文件：`server/src/main/java/com/logic/code/service/OrderService.java`

**主要修复内容：**

1. **添加优惠券使用逻辑**
   ```java
   // 使用优惠券
   if (orderSubmitParamDTO.getCouponId() != null && orderSubmitParamDTO.getCouponId() > 0) {
       boolean couponUsed = userCouponService.useCoupon(
           userInfo.getId(), 
           orderSubmitParamDTO.getCouponId(), 
           order.getId()
       );
       if (!couponUsed) {
           throw new WeshopWechatException(WeshopWechatResultStatus.COUPON_USE_FAILED);
       }
   }
   ```

2. **完善优惠券价格计算**
   - 实现了优惠券价格计算逻辑
   - 验证优惠券是否满足使用条件
   - 添加详细的错误处理和日志

3. **添加依赖注入**
   ```java
   @Resource
   private UserCouponService userCouponService;
   ```

4. **添加错误状态**
   ```java
   COUPON_USE_FAILED("625", "优惠券使用失败"),
   ```

### 2. 优惠券使用流程

修复后的完整流程：

1. **订单提交时**
   - 验证优惠券是否存在且可用
   - 检查是否满足最低消费条件
   - 计算优惠券抵扣金额
   - 创建订单记录

2. **订单创建成功后**
   - 调用`userCouponService.useCoupon()`
   - 更新优惠券状态为已使用(status=1)
   - 记录使用时间和关联订单ID
   - 扣减用户积分（如果使用）
   - 扣减用户余额（如果使用）

3. **状态验证**
   - 优惠券状态：0(可用) → 1(已使用)
   - 积分记录：创建负数记录表示使用
   - 用户积分：总数减少相应数量

## 测试验证

### 1. 数据库测试
```sql
-- 执行测试脚本
source database/test_coupon_points_usage.sql;
```

### 2. 功能测试步骤

#### 测试场景1：优惠券使用
1. 用户有可用优惠券
2. 下单时选择优惠券
3. 提交订单成功
4. **验证**：
   - 优惠券状态变为已使用
   - 优惠券记录了使用时间和订单ID
   - 不能再次使用该优惠券

#### 测试场景2：积分使用
1. 用户有足够积分
2. 下单时开启积分抵扣
3. 提交订单成功
4. **验证**：
   - 用户积分减少
   - 创建积分使用记录
   - 积分余额正确

#### 测试场景3：组合使用
1. 同时使用优惠券、积分、余额
2. 提交订单成功
3. **验证**：
   - 所有抵扣方式都正确处理
   - 实付金额计算正确
   - 状态更新完整

### 3. 安全测试

#### 重复使用测试
1. 使用优惠券下单
2. 尝试再次使用同一张优惠券
3. **预期结果**：第二次使用失败

#### 并发测试
1. 多个用户同时使用同一类型优惠券
2. **预期结果**：每张优惠券只能被使用一次

## 风险评估

### 修复前的风险
- 🔴 **高风险**：优惠券可重复使用，造成经济损失
- 🔴 **高风险**：用户可能恶意利用漏洞
- 🟡 **中风险**：数据不一致，影响统计分析

### 修复后的保障
- ✅ **优惠券状态正确更新**
- ✅ **防止重复使用**
- ✅ **数据一致性保证**
- ✅ **完整的审计日志**

## 部署注意事项

### 1. 数据修复
对于已经存在的问题数据，需要执行修复脚本：

```sql
-- 修复已使用但状态未更新的优惠券
UPDATE `weshop_user_coupon` uc
JOIN `weshop_order` o ON o.coupon_id = uc.id
SET uc.status = 1,
    uc.use_time = o.create_time,
    uc.order_id = o.id
WHERE uc.status = 0 AND o.coupon_price > 0;
```

### 2. 监控指标
部署后需要监控：
- 优惠券使用成功率
- 重复使用尝试次数
- 订单提交失败率
- 数据一致性检查

### 3. 回滚方案
如果出现问题，可以：
1. 回滚代码到修复前版本
2. 暂时禁用优惠券功能
3. 手动处理异常订单

## 验证清单

部署后验证：

- [ ] 优惠券使用后状态正确更新
- [ ] 不能重复使用已用优惠券
- [ ] 积分扣减正常工作
- [ ] 余额扣减正常工作
- [ ] 组合支付计算正确
- [ ] 订单提交成功率正常
- [ ] 错误处理和日志完整
- [ ] 数据一致性检查通过

## 长期改进建议

1. **事务管理**：确保优惠券使用、积分扣减、余额扣减在同一事务中
2. **幂等性**：添加订单提交的幂等性控制
3. **限流控制**：防止恶意频繁提交订单
4. **实时监控**：建立优惠券使用的实时监控告警
5. **定期审计**：定期检查数据一致性
