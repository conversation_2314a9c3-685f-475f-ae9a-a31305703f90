# getPromotionUserDetail 接口请求方法修复

## 问题描述
在调用 `getPromotionUserDetail` 接口时出现错误：

```
Required request body is missing: public com.logic.code.common.response.Result<java.util.Map<java.lang.String, java.lang.Object>> com.logic.code.controller.app.UserController.getPromotionUserDetail(java.util.Map<java.lang.String, java.lang.Object>)
```

## 问题原因
**前后端请求方法不匹配**：
- **后端接口**: 使用 `@RequestBody` 注解，期望接收 POST 请求的请求体数据
- **前端调用**: 使用默认的 GET 方法，参数作为 URL 查询参数发送

## 错误分析

### 1. 后端接口定义
```java
@RequestMapping("/getPromotionUserDetail")
public Result<Map<String, Object>> getPromotionUserDetail(@RequestBody Map<String, Object> params) {
    // ...
}
```

**特点**:
- 使用 `@RequestBody` 注解
- 期望接收 JSON 格式的请求体
- 需要 POST 方法调用

### 2. 前端调用方式（修复前）
```javascript
util.request(api.GetPromotionUserDetail, {
  userId: userId
}).then(res => {
  // ...
});
```

**问题**:
- `util.request` 默认使用 GET 方法
- 参数作为 URL 查询参数发送
- 没有请求体数据

### 3. util.request 方法签名
```javascript
function request(url, data = {}, method = "GET") {
  // ...
}
```

## 修复方案

### 修复后的前端调用
```javascript
util.request(api.GetPromotionUserDetail, {
  userId: userId
}, 'POST').then(res => {
  // ...
});
```

**修复说明**:
- 明确指定使用 POST 方法
- 数据将作为请求体发送
- 符合后端 `@RequestBody` 的要求

## 技术细节

### 1. HTTP 请求方法对比

| 方法 | 参数位置 | Content-Type | 后端注解 |
|------|----------|--------------|----------|
| GET | URL 查询参数 | - | `@RequestParam` |
| POST | 请求体 | application/json | `@RequestBody` |

### 2. 请求数据格式

**GET 请求**:
```
GET /wechat/user/getPromotionUserDetail?userId=123
```

**POST 请求**:
```
POST /wechat/user/getPromotionUserDetail
Content-Type: application/json

{
  "userId": "123"
}
```

### 3. util.request 实现
```javascript
wx.request({
  url: url,
  data: data,
  method: method,  // 现在会是 'POST'
  header: {
    'Content-Type': 'application/json',
    'X-Weshop-Token': wx.getStorageSync('token')
  },
  // ...
});
```

## 影响范围

### 1. 受影响的功能
- 推广用户详情页面数据加载
- 管理员查看用户推广详情功能

### 2. 修复后的效果
- ✅ 接口调用成功
- ✅ 数据正常返回
- ✅ 页面正常显示

## 其他类似接口检查

为了避免类似问题，需要检查其他使用 `@RequestBody` 的接口：

### 1. 需要检查的接口
```java
// 其他可能使用 @RequestBody 的接口
@RequestMapping("/establishPromotionRelation")
public Result<String> establishPromotionRelation(@RequestBody Map<String, Object> params)

@RequestMapping("/submitWithdraw")
public Result<String> submitWithdraw(@RequestBody Map<String, Object> params)
```

### 2. 前端调用检查
确保所有使用 `@RequestBody` 的接口在前端都使用 POST 方法调用。

## 最佳实践建议

### 1. 接口设计规范
- **查询操作**: 使用 GET 方法 + `@RequestParam`
- **数据提交**: 使用 POST 方法 + `@RequestBody`
- **数据更新**: 使用 PUT 方法 + `@RequestBody`
- **数据删除**: 使用 DELETE 方法

### 2. 前端调用规范
```javascript
// 查询数据 - GET
util.request(api.GetUserInfo)

// 提交数据 - POST
util.request(api.SubmitOrder, orderData, 'POST')

// 更新数据 - POST
util.request(api.UpdateProfile, profileData, 'POST')
```

### 3. 接口文档规范
在接口文档中明确标注：
- HTTP 方法（GET/POST/PUT/DELETE）
- 参数位置（URL参数/请求体）
- 数据格式（JSON/表单）

## 测试验证

### 1. 功能测试
- ✅ 推广用户详情页面正常加载
- ✅ 用户信息正确显示
- ✅ 统计数据准确
- ✅ 订单列表正常

### 2. 接口测试
- ✅ POST 请求成功
- ✅ 请求体数据正确传递
- ✅ 响应数据格式正确
- ✅ 错误处理正常

## 总结

### 修复结果
✅ **请求方法已修复** - 前端使用 POST 方法调用接口
✅ **数据传递正确** - 参数通过请求体发送
✅ **接口调用成功** - 后端能正确接收和处理数据
✅ **功能正常工作** - 推广用户详情页面正常显示

### 经验教训
1. **前后端协调**: 接口设计时要确保前后端对请求方法的理解一致
2. **参数传递**: `@RequestBody` 需要 POST 请求，`@RequestParam` 适用于 GET 请求
3. **测试覆盖**: 新接口开发后要进行完整的前后端联调测试
4. **文档规范**: 接口文档要明确标注 HTTP 方法和参数格式

这个修复确保了 `getPromotionUserDetail` 接口能够正常工作，为推广用户详情功能提供了可靠的数据支持。