# 推广用户筛选功能实现说明

## 功能概述
在管理员用户管理页面中增加推广用户筛选功能，允许管理员筛选出有下线的用户（即推广邀请了其他用户的用户）。

## 实现方案

### 1. 前端实现

#### 1.1 页面结构修改
**文件：** `app/wjhx/pages/ucenter/admin/users/users.wxml`

**修改内容：**
- 在筛选标签中添加"推广用户"选项
- 在统计栏中添加推广用户统计显示

```xml
<!-- 筛选标签新增 -->
<view class="tab-item {{currentType === 'promoter' ? 'active' : ''}}" bindtap="filterByType" data-type="promoter">
  推广用户
</view>

<!-- 统计栏新增 -->
<view class="stat-item">
  <text class="stat-number">{{userStats.promoterUsers || 0}}</text>
  <text class="stat-label">推广用户</text>
</view>
```

#### 1.2 JavaScript逻辑修改
**文件：** `app/wjhx/pages/ucenter/admin/users/users.js`

**修改内容：**
- 在数据结构中添加推广用户统计字段
- 更新统计数据处理逻辑

```javascript
// 数据结构更新
userStats: {
  totalUsers: 0,
  newUsers: 0,
  activeUsers: 0,
  vipUsers: 0,
  promoterUsers: 0  // 新增
}
```

### 2. 后端实现

#### 2.1 查询参数扩展
**文件：** `server/src/main/java/com/logic/code/model/query/AdminUserQuery.java`

**修改内容：**
- 更新type字段注释，添加promoter类型说明

```java
private String type; // 用户类型筛选：new-新用户, active-活跃用户, vip-VIP用户, promoter-推广用户
```

#### 2.2 用户服务实现
**文件：** `server/src/main/java/com/logic/code/service/UserService.java`

**新增方法：**

##### 2.2.1 getAdminUserList方法
实现管理员用户列表查询，支持推广用户筛选：

```java
public AdminUserListVO getAdminUserList(AdminUserQuery query) {
    // 构建查询条件
    QueryWrapper<User> wrapper = new QueryWrapper<>();
    
    // 关键词搜索
    if (query.getKeyword() != null && !query.getKeyword().trim().isEmpty()) {
        String keyword = query.getKeyword().trim();
        wrapper.and(w -> w.like("nickname", keyword).or().like("mobile", keyword));
    }
    
    // 类型筛选
    if ("promoter".equals(query.getType())) {
        // 推广用户筛选：查询有下线的用户
        wrapper.exists("SELECT 1 FROM user u2 WHERE u2.promoter_id = user.id");
    }
    // ... 其他筛选逻辑
}
```

##### 2.2.2 convertToAdminUserVO方法
将User实体转换为AdminUserVO，包含推广信息：

```java
private AdminUserVO convertToAdminUserVO(User user) {
    AdminUserVO userVO = new AdminUserVO();
    
    // 基本信息设置
    // ...
    
    // 推广信息统计
    QueryWrapper<User> promotionWrapper = new QueryWrapper<>();
    promotionWrapper.eq("promoter_id", user.getId());
    Long promotionCount = userMapper.selectCount(promotionWrapper);
    userVO.setPromotionCount(promotionCount.intValue());
    
    // 推广者信息
    if (user.getPromoterId() != null) {
        User promoter = userMapper.selectById(user.getPromoterId());
        if (promoter != null) {
            userVO.setPromoterId(promoter.getId());
            userVO.setPromoterName(promoter.getNickname());
        }
    }
}
```

##### 2.2.3 getAdminStats方法更新
添加推广用户统计：

```java
public Map<String, Object> getAdminStats() {
    // ... 其他统计
    
    // 获取推广用户数（有下线的用户）
    QueryWrapper<User> promoterWrapper = new QueryWrapper<>();
    promoterWrapper.apply("id IN (SELECT DISTINCT promoter_id FROM user WHERE promoter_id IS NOT NULL)");
    Long promoterUsers = userMapper.selectCount(promoterWrapper);
    stats.put("promoterUsers", promoterUsers);
    
    return stats;
}
```

#### 2.3 API配置更新
**文件：** `app/wjhx/config/api.js`

**修改内容：**
- 修正AdminUserList接口路径，与控制器路径保持一致

```javascript
AdminUserList: BaseUrl + 'admin/user/list2', // 管理员用户列表
```

## 核心逻辑说明

### 1. 推广用户识别逻辑
推广用户的识别基于以下条件：
- 用户表中存在其他用户的promoter_id字段指向该用户的id
- 即：该用户是其他用户的推广者

**SQL查询条件：**
```sql
EXISTS (SELECT 1 FROM user u2 WHERE u2.promoter_id = user.id)
```

### 2. 统计数据计算
推广用户统计的计算逻辑：
- 统计所有作为推广者的用户数量
- 使用DISTINCT确保不重复计算

**SQL查询：**
```sql
SELECT COUNT(*) FROM user WHERE id IN (
    SELECT DISTINCT promoter_id FROM user WHERE promoter_id IS NOT NULL
)
```

### 3. 数据展示逻辑
在用户列表中展示推广信息：
- 推广用户数量：该用户推广的下线用户数量
- 推广者信息：如果该用户本身也是被推广的，显示推广者信息

## 数据库依赖

### 必需字段
- `user.promoter_id`: 推广者ID，建立推广关系
- `user.id`: 用户主键，用于关联查询

### 可选字段
- `user.promotion_count`: 推广用户数量缓存（可用于性能优化）
- `user.first_promotion_time`: 首次推广时间

## 性能考虑

### 1. 查询优化
- 推广用户筛选使用EXISTS子查询，性能较好
- 建议在promoter_id字段上建立索引

### 2. 统计缓存
- 可考虑将推广统计数据缓存，定期更新
- 对于大量数据的场景，可使用Redis缓存统计结果

### 3. 分页处理
- 使用MyBatis-Plus的分页插件
- 避免一次性加载大量数据

## 扩展功能

### 1. 推广层级显示
可扩展显示推广层级关系：
- 一级推广用户
- 二级推广用户
- 多级推广关系

### 2. 推广收益统计
可扩展显示推广收益信息：
- 推广佣金总额
- 月度推广收益
- 推广转化率

### 3. 推广排行榜
可扩展推广用户排行功能：
- 按推广用户数量排序
- 按推广收益排序
- 按推广活跃度排序

## 测试要点

### 1. 功能测试
- 筛选功能正确性
- 统计数据准确性
- 搜索功能兼容性

### 2. 性能测试
- 大数据量下的查询性能
- 分页加载性能
- 统计计算性能

### 3. 边界测试
- 无推广关系数据的处理
- 推广关系异常数据的处理
- 权限控制测试

## 部署注意事项

### 1. 数据库准备
- 确保user表结构完整
- 检查推广关系数据的完整性
- 建议添加相关索引

### 2. 权限控制
- 确保只有管理员能访问此功能
- 检查API接口的权限验证

### 3. 数据一致性
- 确保推广关系数据的一致性
- 定期检查和修复异常数据

## 维护建议

### 1. 定期数据检查
- 检查推广关系数据的完整性
- 清理异常的推广关系数据

### 2. 性能监控
- 监控推广用户查询的性能
- 优化慢查询

### 3. 功能扩展
- 根据业务需求扩展推广相关功能
- 优化用户体验