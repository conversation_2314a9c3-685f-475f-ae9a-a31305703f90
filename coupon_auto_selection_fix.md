# 优惠券自动选择问题修复

## 问题描述
当用户在上一个订单中选择了优惠券，然后换成另一个商品进入下单页面时，优惠券会被默认选中。这个逻辑有误，每个新的下单页面进入时应该未选择优惠券，只有用户主动选择了才使用。

## 问题根源

### 原有逻辑问题
```javascript
// 原有代码会自动从storage加载之前的优惠券选择
var storageCouponId = wx.getStorageSync('couponId');
if (storageCouponId && storageCouponId !== 'null' && storageCouponId > 0) {
  this.setData({
    'couponId': parseInt(storageCouponId)
  });
}
```

这导致了：
1. **跨订单污染**：上一个订单的优惠券选择影响新订单
2. **用户体验差**：用户可能不知道已经自动选择了优惠券
3. **逻辑混乱**：不同商品可能不适用相同的优惠券

## 修复方案

### 1. 区分不同的使用场景

**场景1：新订单**
- 用户从商品详情页点击"立即购买"
- 用户从购物车进入结算页面
- **期望行为**：不选择任何优惠券，让用户主动选择

**场景2：优惠券选择返回**
- 用户在当前订单中选择优惠券后返回
- **期望行为**：保持用户的优惠券选择

**场景3：页面刷新**
- 用户在当前订单中刷新页面
- **期望行为**：保持当前订单的状态

### 2. 实现智能的优惠券加载逻辑

```javascript
// 检查是否是从优惠券选择页面返回（有skipCheckoutReload标志）
const skipReload = wx.getStorageSync('skipCheckoutReload');
let storageCouponId = 0;

if (skipReload) {
  // 如果是从优惠券选择页面返回，可以使用storage中的优惠券ID
  storageCouponId = wx.getStorageSync('couponId') || 0;
  console.log('从优惠券选择页面返回，加载storage中的优惠券ID:', storageCouponId);
} else {
  // 新订单默认不选择优惠券，让用户主动选择
  console.log('新订单初始化，优惠券ID设置为0，用户需要主动选择');
}

this.setData({ 'couponId': parseInt(storageCouponId) || 0 });
```

### 3. 订单完成后清理数据

```javascript
// 在订单提交成功后清除storage中的优惠券ID
if (res.success) {
  wx.removeStorageSync('couponId');
  console.log('订单提交成功，清除storage中的优惠券ID');
  // ... 其他成功处理逻辑
}
```

## 修改的文件

### 1. app/wjhx/pages/shopping/checkout/checkout.js

**主要修改**：

1. **onLoad方法**：
   - 区分新订单和优惠券选择返回的场景
   - 只在从优惠券选择页面返回时加载storage中的优惠券ID
   - 新订单默认不选择优惠券

2. **finalCouponId逻辑**：
   - 优先使用已经从storage加载的couponId
   - 其次使用URL参数中的couponId
   - 最后默认为0

3. **submitOrder方法**：
   - 在订单提交成功后清除storage中的优惠券ID
   - 避免影响下一个订单

## 测试步骤

### 测试用例1：新订单不自动选择优惠券
1. 在订单A中选择优惠券并完成订单
2. 进入商品B的详情页，点击"立即购买"
3. 验证：
   - 优惠券卡片显示"请选择优惠券"
   - 没有自动选择之前的优惠券
   - 价格计算不包含优惠券抵扣

### 测试用例2：当前订单内优惠券选择保持
1. 进入新订单页面
2. 选择优惠券A，确认
3. 刷新页面或重新进入
4. 验证：
   - 优惠券A仍然被选中
   - 价格计算正确包含优惠券抵扣

### 测试用例3：优惠券选择页面返回
1. 在订单页面点击优惠券卡片
2. 选择优惠券B，点击确认
3. 返回订单页面
4. 验证：
   - 优惠券B被正确选中
   - 价格立即更新
   - 显示正确的抵扣金额

### 测试用例4：订单完成后清理
1. 选择优惠券完成订单
2. 进入新的商品订单页面
3. 验证：
   - 新订单不自动选择优惠券
   - storage中的优惠券ID已被清除

### 测试用例5：购物车订单
1. 在购物车中选择商品
2. 进入结算页面
3. 验证：
   - 不自动选择优惠券
   - 用户需要主动选择

## 预期结果

### 成功标准
1. ✅ 新订单默认不选择优惠券
2. ✅ 当前订单内的优惠券选择能够保持
3. ✅ 从优惠券选择页面返回时正确显示选择
4. ✅ 订单完成后清理storage数据
5. ✅ 不同订单之间不相互影响

### 用户体验改进
- **清晰的状态**：用户明确知道是否选择了优惠券
- **主动选择**：用户需要主动选择优惠券，避免意外使用
- **数据隔离**：不同订单之间的优惠券选择不相互影响
- **状态保持**：当前订单内的选择能够正确保持

## 调试信息检查

### 新订单场景
```
新订单初始化，优惠券ID设置为0，用户需要主动选择
优惠券ID选择逻辑: {URL参数中的couponId: 0, 最终使用的couponId: 0, 说明: "新订单，未选择优惠券"}
```

### 优惠券选择返回场景
```
从优惠券选择页面返回，加载storage中的优惠券ID: 1
优惠券ID选择逻辑: {URL参数中的couponId: 0, 最终使用的couponId: 1, 说明: "使用URL参数中的优惠券ID"}
```

### 订单完成场景
```
订单提交成功，清除storage中的优惠券ID
```

## 故障排除

### 问题1：新订单仍然自动选择优惠券
- 检查skipCheckoutReload标志是否正确清除
- 确认onLoad方法中的逻辑判断
- 验证storage中的数据状态

### 问题2：优惠券选择后丢失
- 检查skipCheckoutReload标志是否正确设置
- 确认storage中的couponId是否正确保存
- 验证页面跳转的参数传递

### 问题3：订单完成后数据未清理
- 检查订单提交成功的回调逻辑
- 确认wx.removeStorageSync是否正确执行
- 验证不同订单类型的处理

## 总结

通过区分不同的使用场景和智能的数据加载逻辑，解决了优惠券自动选择的问题。关键改进包括：

1. **场景识别**：区分新订单和优惠券选择返回的场景
2. **数据隔离**：不同订单之间的优惠券选择不相互影响
3. **状态管理**：正确处理当前订单内的状态保持
4. **数据清理**：订单完成后及时清理storage数据

这些修复确保了优惠券功能的正确性和良好的用户体验。