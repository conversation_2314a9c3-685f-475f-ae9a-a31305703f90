# 推广者筛选功能测试指南

## 测试准备

### 1. 测试数据要求
为了充分测试推广者筛选功能，需要准备以下类型的测试数据：

- **有推广者的用户**: 至少3-5个通过推广邀请注册的用户
- **无推广者的用户**: 至少3-5个直接注册的用户
- **不同时间的用户**: 包含今日、本周、本月的不同时间注册的用户
- **推广者信息**: 确保有推广者的用户包含推广者昵称等信息

### 2. 数据字段要求
确保用户数据包含以下字段：
```javascript
{
  id: "用户ID",
  nickname: "用户昵称",
  promoterId: "推广者ID（有推广者时不为空）",
  promoterNickname: "推广者昵称",
  promotionTime: "推广时间",
  // ... 其他字段
}
```

## 功能测试

### 1. 基础显示测试

#### 1.1 筛选器显示测试
1. **进入推广详情页面**
   - 确认显示两个筛选区域：时间筛选和推广者筛选
   - 确认筛选器标签显示正确："时间筛选："、"推广者筛选："

2. **默认状态检查**
   - 确认时间筛选默认选中"全部"
   - 确认推广者筛选默认选中"全部用户"
   - 确认用户列表显示所有用户

#### 1.2 用户信息显示测试
1. **推广者信息显示**
   - 有推广者的用户显示："推广者：[推广者昵称]" + 👑 图标
   - 无推广者的用户显示："推广者：直接注册" + 🆓 图标

2. **视觉区分检查**
   - 有推广者用户的推广者信息显示为蓝色
   - 无推广者用户的推广者信息显示为灰色
   - 图标和徽章颜色正确

### 2. 推广者筛选功能测试

#### 2.1 单独推广者筛选
1. **筛选"有推广者"**
   - 点击"有推广者"筛选项
   - 确认筛选项变为激活状态（蓝色背景）
   - 确认列表只显示有推广者的用户
   - 确认状态提示显示"显示有推广者"

2. **筛选"无推广者"**
   - 点击"无推广者"筛选项
   - 确认筛选项变为激活状态（蓝色背景）
   - 确认列表只显示无推广者的用户
   - 确认状态提示显示"显示无推广者"

3. **返回"全部用户"**
   - 点击"全部用户"筛选项
   - 确认显示所有用户
   - 确认状态提示显示"显示全部用户"

#### 2.2 组合筛选测试
1. **时间 + 推广者筛选**
   - 选择"今日" + "有推广者"
   - 确认只显示今日注册且有推广者的用户
   - 确认状态提示显示"显示今日有推广者"

2. **不同组合测试**
   - 测试"本周" + "无推广者"
   - 测试"本月" + "有推广者"
   - 确认每种组合的筛选结果正确
   - 确认状态提示文本正确

### 3. 交互体验测试

#### 3.1 筛选器交互
1. **点击反馈**
   - 确认筛选项点击有视觉反馈
   - 确认激活状态切换正常
   - 确认筛选结果实时更新

2. **筛选器样式**
   - 确认时间筛选器使用橙色主题
   - 确认推广者筛选器使用蓝色主题
   - 确认激活状态颜色正确

#### 3.2 状态提示测试
1. **状态文本准确性**
   - 测试所有筛选组合的状态文本
   - 确认文本描述准确、易懂
   - 确认用户数量统计正确

2. **排序信息显示**
   - 确认排序信息正确显示
   - 确认排序和筛选状态同时显示
   - 确认信息布局美观

### 4. 数据准确性测试

#### 4.1 筛选结果验证
1. **有推广者筛选验证**
   - 手动检查筛选结果中的每个用户
   - 确认所有用户都有 promoterId 字段
   - 确认推广者信息显示正确

2. **无推广者筛选验证**
   - 手动检查筛选结果中的每个用户
   - 确认所有用户的 promoterId 为空或null
   - 确认显示"直接注册"

#### 4.2 组合筛选验证
1. **时间范围验证**
   - 确认时间筛选的准确性
   - 确认组合筛选时时间范围正确

2. **数据一致性验证**
   - 确认筛选前后数据一致
   - 确认排序不影响筛选结果
   - 确认页面刷新后筛选状态保持

### 5. 边界情况测试

#### 5.1 空数据测试
1. **无匹配数据**
   - 测试筛选条件下无匹配用户的情况
   - 确认显示合适的空状态提示
   - 确认状态提示显示"共0人"

2. **单一类型数据**
   - 测试只有有推广者用户的情况
   - 测试只有无推广者用户的情况
   - 确认筛选功能正常工作

#### 5.2 数据异常测试
1. **推广者信息缺失**
   - 测试 promoterId 存在但 promoterNickname 缺失
   - 确认显示默认文本"有推广者"
   - 确认不影响筛选功能

2. **时间数据异常**
   - 测试推广时间格式异常的情况
   - 确认不影响推广者筛选功能

### 6. 性能测试

#### 6.1 大数据量测试
1. **用户数量测试**
   - 测试100+用户的筛选性能
   - 确认筛选响应速度正常
   - 确认页面滚动流畅

2. **频繁切换测试**
   - 快速切换不同筛选条件
   - 确认没有性能问题
   - 确认状态更新及时

#### 6.2 内存使用测试
1. **长时间使用测试**
   - 长时间使用筛选功能
   - 确认没有内存泄漏
   - 确认页面响应正常

## 测试用例示例

### 用例1：基础推广者筛选
**前置条件**: 用户列表包含有推广者和无推广者的用户
**测试步骤**:
1. 进入推广详情页面
2. 点击"有推广者"筛选项
3. 检查筛选结果

**预期结果**:
- 只显示有推广者的用户
- 状态提示显示"显示有推广者"
- 用户数量统计正确

### 用例2：组合筛选测试
**前置条件**: 用户列表包含不同时间和推广者类型的用户
**测试步骤**:
1. 选择"今日"时间筛选
2. 选择"无推广者"推广者筛选
3. 检查筛选结果

**预期结果**:
- 只显示今日注册且无推广者的用户
- 状态提示显示"显示今日无推广者"
- 筛选结果准确

### 用例3：筛选状态切换
**前置条件**: 已应用某个筛选条件
**测试步骤**:
1. 从"有推广者"切换到"无推广者"
2. 再切换到"全部用户"
3. 检查每次切换的结果

**预期结果**:
- 每次切换结果正确
- 状态提示实时更新
- 用户数量统计准确

## 测试完成标准

### 功能完整性
- ✅ 推广者筛选功能正常工作
- ✅ 组合筛选功能正常工作
- ✅ 状态提示准确显示
- ✅ 用户信息正确显示

### 用户体验
- ✅ 交互反馈及时
- ✅ 视觉设计美观
- ✅ 操作流程顺畅
- ✅ 错误处理得当

### 性能表现
- ✅ 筛选响应速度快
- ✅ 大数据量处理正常
- ✅ 内存使用合理
- ✅ 页面滚动流畅

## 问题排查指南

### 常见问题
1. **筛选结果不正确**
   - 检查数据字段是否正确
   - 检查筛选逻辑是否有误
   - 检查数据类型转换

2. **状态提示不更新**
   - 检查方法绑定是否正确
   - 检查数据更新是否及时
   - 检查页面渲染逻辑

3. **样式显示异常**
   - 检查CSS类名是否正确
   - 检查样式优先级
   - 检查条件渲染逻辑

当所有测试用例通过时，推广者筛选功能测试完成。