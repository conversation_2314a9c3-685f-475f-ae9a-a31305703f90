# 组合抵扣功能说明

## 功能概述

系统支持**余额抵扣、优惠券抵扣、积分抵扣**三种方式同时使用，为用户提供最大化的优惠体验。

## 核心特性

### ✅ 三种抵扣方式同时支持
1. **优惠券抵扣**：满减优惠券，有最低消费限制
2. **积分抵扣**：用户积分按汇率抵扣现金（默认100积分=1元）
3. **余额抵扣**：用户账户余额直接抵扣

### ✅ 智能抵扣优先级
**抵扣顺序**：优惠券 > 积分 > 余额
- **优惠券优先**：通常有使用期限，优先使用避免过期
- **积分次之**：用户积累的奖励，次优先使用
- **余额最后**：用户的真实资金，最后使用

### ✅ 总量控制机制
- 确保总抵扣金额不超过订单金额
- 超额时按优先级自动调整
- 前端实时计算 + 后端双重验证

## 用户界面功能

### 1. 独立抵扣控制
每种抵扣方式都有独立的开关和设置：

```xml
<!-- 积分抵扣卡片 -->
<view class="points-card" wx:if="{{userPoints > 0 && maxUsablePoints > 0}}">
    <switch checked="{{usePointsEnabled}}" bindchange="onPointsSwitchChange" />
    <text>使用 {{usePoints}} 积分抵扣 ¥{{pointsPrice}}</text>
</view>

<!-- 余额抵扣卡片 -->
<view class="balance-card" wx:if="{{userBalance > 0}}">
    <switch checked="{{useBalanceEnabled}}" bindchange="onBalanceSwitchChange" />
    <input value="{{useBalance}}" placeholder="最多可用{{maxUsableBalance}}" />
</view>
```

### 2. 一键优惠功能
智能推荐并一键开启所有可用优惠：

```xml
<!-- 一键优惠按钮 -->
<view class="quick-discount-card">
    <text>💡 智能优惠 - 一键使用所有可用优惠</text>
    <view bindtap="useAllAvailableDiscounts">一键优惠</view>
</view>
```

### 3. 组合抵扣提示
当使用多种抵扣方式时，显示组合优惠提示：

```xml
<!-- 组合抵扣提示 -->
<view class="combo-discount-tip" wx:if="{{discountCount > 1}}">
    <text>🎉 已为您组合使用{{discountCount}}种优惠，共节省¥{{totalSaved}}</text>
</view>
```

### 4. 详细费用明细
清晰显示每种抵扣的金额：

```xml
<!-- 费用明细 -->
<view class="price-details">
    <view wx:if="{{couponPrice > 0}}">优惠券：-¥{{couponPrice}}</view>
    <view wx:if="{{pointsPrice > 0}}">积分抵扣：-¥{{pointsPrice}}</view>
    <view wx:if="{{balancePrice > 0}}">余额抵扣：-¥{{balancePrice}}</view>
    <view class="total">实付金额：¥{{actualPrice}}</view>
</view>
```

## 技术实现

### 1. 前端统一计算逻辑

```javascript
// 统一计算所有抵扣金额
calculateAllDeductions: function() {
    const baseOrderAmount = goodsTotalPrice + freightPrice;
    let remainingAmount = baseOrderAmount;
    
    // 1. 优惠券抵扣（优先级最高）
    let finalCouponPrice = 0;
    if (this.data.couponId > 0 && this.data.checkedCoupon) {
        finalCouponPrice = Math.min(coupon.amount, remainingAmount);
        remainingAmount -= finalCouponPrice;
    }
    
    // 2. 积分抵扣（优先级中等）
    let finalPointsPrice = 0;
    if (this.data.usePointsEnabled && this.data.usePoints > 0) {
        const requestedPointsPrice = this.calculatePointsPrice(this.data.usePoints);
        finalPointsPrice = Math.min(requestedPointsPrice, remainingAmount);
        remainingAmount -= finalPointsPrice;
    }
    
    // 3. 余额抵扣（优先级最低）
    let finalBalancePrice = 0;
    if (this.data.useBalanceEnabled && this.data.useBalance > 0) {
        const requestedBalancePrice = Math.min(this.data.useBalance, this.data.userBalance);
        finalBalancePrice = Math.min(requestedBalancePrice, remainingAmount);
        remainingAmount -= finalBalancePrice;
    }
    
    return {
        couponPrice: finalCouponPrice,
        pointsPrice: finalPointsPrice,
        balancePrice: finalBalancePrice,
        actualPrice: remainingAmount
    };
}
```

### 2. 实时更新机制

```javascript
// 重新计算并更新最大可用金额
updateMaxUsableAmounts: function() {
    // 基于当前已选择的抵扣方式，重新计算其他抵扣方式的最大可用金额
    const baseOrderAmount = goodsTotalPrice + freightPrice;
    let usedAmount = 0;
    
    // 计算已使用的抵扣金额
    // ...
    
    // 剩余可抵扣金额
    const remainingAmount = Math.max(0, baseOrderAmount - usedAmount);
    
    // 重新计算最大可用积分和余额
    this.setData({
        maxUsablePoints: newMaxUsablePoints,
        maxUsableBalance: newMaxUsableBalance
    });
}
```

### 3. 后端验证和调整

```java
// 验证总抵扣金额不能超过订单金额
BigDecimal orderBaseAmount = goodsTotalPrice.add(freightPrice);
BigDecimal totalDeduction = couponPrice.add(pointsPrice).add(balancePrice);

if (totalDeduction.compareTo(orderBaseAmount) > 0) {
    // 按优先级调整抵扣金额：优惠券 > 积分 > 余额
    BigDecimal remainingAmount = orderBaseAmount;
    
    // 优惠券抵扣（优先级最高，不调整）
    remainingAmount = remainingAmount.subtract(couponPrice);
    
    // 积分抵扣调整
    if (pointsPrice.compareTo(remainingAmount) > 0) {
        pointsPrice = remainingAmount.max(BigDecimal.ZERO);
        remainingAmount = BigDecimal.ZERO;
    } else {
        remainingAmount = remainingAmount.subtract(pointsPrice);
    }
    
    // 余额抵扣调整
    if (balancePrice.compareTo(remainingAmount) > 0) {
        balancePrice = remainingAmount.max(BigDecimal.ZERO);
    }
}
```

## 使用场景示例

### 场景1：小额订单组合抵扣
- **订单金额**：50元
- **可用优惠券**：20元满30减
- **用户积分**：2000积分（可抵扣20元）
- **用户余额**：30元
- **抵扣结果**：优惠券20元 + 积分20元 + 余额10元 = 50元，实付0元

### 场景2：中等订单部分抵扣
- **订单金额**：150元
- **可用优惠券**：30元满100减
- **用户积分**：2000积分（可抵扣20元）
- **用户余额**：50元
- **抵扣结果**：优惠券30元 + 积分20元 + 余额50元 = 100元，实付50元

### 场景3：大额订单灵活抵扣
- **订单金额**：300元
- **可用优惠券**：50元满200减
- **用户积分**：5000积分（可抵扣50元）
- **用户余额**：100元
- **抵扣结果**：优惠券50元 + 积分50元 + 余额100元 = 200元，实付100元

## 智能功能

### 1. 智能推荐
系统会自动检测用户可用的抵扣方式并推荐：

```javascript
recommendDiscountOptions: function() {
    const recommendations = [];
    
    // 检查未使用的优惠券
    if (!this.data.checkedCoupon && this.data.availableCoupons.length > 0) {
        recommendations.push({
            type: 'coupon',
            message: `您有可用优惠券，最高可省${bestCoupon.amount}元`
        });
    }
    
    // 检查未使用的积分
    if (!this.data.usePointsEnabled && this.data.maxUsablePoints > 0) {
        recommendations.push({
            type: 'points',
            message: `您有${this.data.userPoints}积分，可抵扣${pointsValue}元`
        });
    }
    
    // 检查未使用的余额
    if (!this.data.useBalanceEnabled && this.data.maxUsableBalance > 0) {
        recommendations.push({
            type: 'balance',
            message: `您有余额${this.data.userBalance}元，可抵扣${this.data.maxUsableBalance}元`
        });
    }
    
    return recommendations;
}
```

### 2. 一键优惠
用户可以一键开启所有可用的抵扣方式：

```javascript
useAllAvailableDiscounts: function() {
    // 1. 自动选择最优优惠券
    // 2. 启用积分抵扣
    // 3. 启用余额抵扣
    
    this.recalculateAllDeductions();
    wx.showToast({
        title: '已为您开启所有优惠',
        icon: 'success'
    });
}
```

## 安全保障

### 1. 前端保障
- ✅ 实时计算最大可用金额
- ✅ 总抵扣金额不超过订单金额
- ✅ 用户输入验证和限制

### 2. 后端保障
- ✅ 双重验证机制
- ✅ 自动调整超额抵扣
- ✅ 详细的操作日志

### 3. 数据保障
- ✅ 完整的抵扣记录
- ✅ 状态一致性检查
- ✅ 事务保护机制

## 测试验证

### 功能测试
```sql
-- 执行组合抵扣测试
source database/test_combined_discounts.sql;
```

### 测试清单
- [ ] 三种抵扣方式独立开关正常
- [ ] 组合使用时计算正确
- [ ] 总抵扣金额不超过订单金额
- [ ] 一键优惠功能正常
- [ ] 费用明细显示正确
- [ ] 订单提交成功
- [ ] 抵扣记录完整

## 用户体验优化

### 1. 视觉提示
- 🎉 组合抵扣成功提示
- 💡 智能推荐图标
- ✨ 动画效果增强体验

### 2. 交互优化
- 实时计算和更新
- 一键操作简化流程
- 清晰的费用明细

### 3. 错误处理
- 友好的错误提示
- 自动修正超额抵扣
- 操作引导和帮助

这个组合抵扣功能为用户提供了最大化的优惠体验，同时确保了系统的安全性和数据准确性。
