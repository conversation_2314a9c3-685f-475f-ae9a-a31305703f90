# 费用明细显示不全问题修复

## 问题描述
选择了优惠券后，页面费用明细模块下面展示不全，部分内容被底部支付栏遮挡。

## 问题分析

### 根本原因
1. **固定定位的支付栏**：底部支付栏使用了`position: fixed`，固定在屏幕底部
2. **动态内容增加**：选择优惠券后，费用明细会增加新的行项（优惠券抵扣、积分抵扣、余额抵扣）
3. **底部间距不足**：页面的底部padding不足以容纳增加的内容高度
4. **响应式样式问题**：不同屏幕尺寸下的底部间距设置不一致

### 具体表现
- 选择优惠券前：费用明细只有"商品合计"、"运费"、"实付金额"
- 选择优惠券后：增加了"优惠券"、"积分抵扣"、"余额抵扣"等行项
- 内容高度增加，但底部间距没有相应调整，导致被支付栏遮挡

## 修复方案

### 1. 增加页面包装器的底部间距
```css
.page-wrapper {
    width: 100%;
    min-height: 100vh;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f8f9fa 30%);
    padding-bottom: 160rpx; /* 从120rpx增加到160rpx */
}
```

### 2. 增加主要内容区域的底部间距
```css
.main-content {
    flex: 1;
    padding: 24rpx 20rpx 60rpx; /* 从40rpx增加到60rpx */
    background: #f8f9fa;
    border-radius: 32rpx 32rpx 0 0;
    margin-top: -32rpx;
    position: relative;
    z-index: 8;
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
}
```

### 3. 修复响应式样式的底部间距

**中等屏幕 (max-width: 750rpx)**：
```css
.main-content {
    padding: 20rpx 16rpx 60rpx; /* 从32rpx增加到60rpx */
}
```

**小屏幕 (max-width: 600rpx)**：
```css
.main-content {
    padding: 16rpx 12rpx 60rpx; /* 从28rpx增加到60rpx */
}
```

## 修改的文件

### app/wjhx/pages/shopping/checkout/checkout.wxss

**主要修改**：
1. `.page-wrapper`的`padding-bottom`从120rpx增加到160rpx
2. `.main-content`的底部padding从40rpx增加到60rpx
3. 响应式样式中的`.main-content`底部padding统一增加到60rpx

## 测试验证

### 测试场景1：基本费用明细
1. 进入checkout页面（未选择优惠券）
2. 验证费用明细完全可见
3. 确认"实付金额"行不被支付栏遮挡

### 测试场景2：选择优惠券后
1. 选择一张优惠券
2. 返回checkout页面
3. 验证：
   - 费用明细增加了"优惠券"行
   - 所有内容完全可见
   - "实付金额"行不被遮挡

### 测试场景3：多种抵扣组合
1. 同时使用优惠券、积分抵扣、余额抵扣
2. 验证：
   - 费用明细显示所有抵扣项
   - 内容完全可见，无遮挡
   - 可以正常滚动查看

### 测试场景4：不同屏幕尺寸
1. 在不同设备/屏幕尺寸下测试
2. 验证响应式样式正常工作
3. 确认各种尺寸下都有足够的底部空间

## 预期结果

### 修复前的问题
- ❌ 选择优惠券后，"实付金额"被支付栏遮挡
- ❌ 费用明细底部内容显示不全
- ❌ 用户需要向上滚动才能看到完整内容

### 修复后的效果
- ✅ 所有费用明细项完全可见
- ✅ "实付金额"行与支付栏有适当间距
- ✅ 不同屏幕尺寸下都有良好的显示效果
- ✅ 用户体验得到改善

## 技术细节

### 布局结构
```
page-wrapper (padding-bottom: 160rpx)
├── header-section
└── main-content (padding-bottom: 60rpx)
    ├── address-card
    ├── coupon-card
    ├── goods-card
    └── price-card (费用明细)
        └── price-details
            ├── price-item (商品合计)
            ├── price-item (运费)
            ├── price-item (优惠券) [动态显示]
            ├── price-item (积分抵扣) [动态显示]
            ├── price-item (余额抵扣) [动态显示]
            ├── price-divider
            └── price-item total-item (实付金额)

payment-bar (position: fixed, bottom: 0)
```

### 间距计算
- **支付栏高度**：约80-100rpx
- **页面底部padding**：160rpx（确保有足够缓冲）
- **内容区底部padding**：60rpx（额外的安全间距）
- **总底部空间**：220rpx（足够容纳动态内容）

## 注意事项

### 1. 性能考虑
- 增加的padding不会影响页面性能
- 固定定位的支付栏保持高效渲染

### 2. 兼容性
- 修改的样式兼容所有微信小程序支持的设备
- 响应式设计确保不同屏幕尺寸的适配

### 3. 维护建议
- 如果未来添加更多费用项，可能需要进一步调整底部间距
- 建议定期测试不同内容长度下的显示效果

## 总结

通过增加页面和内容区域的底部间距，解决了费用明细被支付栏遮挡的问题。修复后的布局能够适应动态内容的变化，确保用户在任何情况下都能看到完整的费用明细信息。

关键改进：
1. **预留足够空间**：考虑到动态内容的最大可能高度
2. **响应式适配**：确保不同屏幕尺寸下的一致体验
3. **用户体验优化**：消除了内容被遮挡的问题