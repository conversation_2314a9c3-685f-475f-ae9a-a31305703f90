# 优惠券退回功能优化 - 简化解决方案

## 问题描述

之前的优惠券退回逻辑存在问题：当订单取消时，优惠券会被退回给用户，但是订单中使用的优惠券记录会被清空（`useTime` 和 `orderId` 字段被设为 null），导致在查看已取消订单的详情时无法显示原本使用的优惠券信息。

## 解决方案

在现有的 `weshop_user_coupon` 表中新增字段来保存历史记录，避免创建额外的历史表，实现更简洁的解决方案。

## 实现内容

### 1. 数据库层面

#### 修改现有表：`weshop_user_coupon`
```sql
ALTER TABLE `weshop_user_coupon` 
ADD COLUMN `last_used_order_id` int(11) DEFAULT NULL COMMENT '最后使用的订单ID（历史记录）' AFTER `order_id`,
ADD COLUMN `last_use_time` datetime DEFAULT NULL COMMENT '最后使用时间（历史记录）' AFTER `use_time`,
ADD COLUMN `is_refunded` tinyint(1) DEFAULT 0 COMMENT '是否已退回：0-未退回，1-已退回' AFTER `last_use_time`,
ADD COLUMN `refund_time` datetime DEFAULT NULL COMMENT '退回时间' AFTER `is_refunded`;
```

#### 字段说明
- `order_id` / `use_time` - 当前使用状态（退回后清空）
- `last_used_order_id` / `last_use_time` - 历史记录（永不清空）
- `is_refunded` - 是否已退回标识
- `refund_time` - 退回时间

### 2. 后端代码

#### 修改实体类
- `UserCoupon.java` - 添加新的历史记录字段

#### 修改现有服务
- `UserCouponServiceImpl.java`
  - 在 `useCoupon()` 方法中同时设置当前状态和历史记录
  - 在 `refundCoupon()` 方法中清空当前状态但保留历史记录，标记为已退回

- `OrderService.java`
  - 在 `queryOrderDetail()` 方法中通过 `last_used_order_id` 查询优惠券历史信息

#### 修改VO类
- `OrderDetailVO.OrderInfoVO` 添加字段：
  - `couponTitle` - 优惠券标题
  - `couponDescription` - 优惠券描述  
  - `couponRefunded` - 是否已退回

### 3. 前端页面

#### 订单详情页面 (`orderDetail.wxml`)
- 在费用明细中显示优惠券标题
- 对已取消订单显示"已退回"标识

#### 样式优化 (`orderDetail.wxss`)
- 添加优惠券标题和退回状态的样式
- 使用绿色背景标识已退回状态

## 核心逻辑流程

### 使用优惠券时
```java
// 设置当前状态
coupon.setStatus(1);
coupon.setUseTime(now);
coupon.setOrderId(orderId);
// 同时保存历史记录
coupon.setLastUsedOrderId(orderId);
coupon.setLastUseTime(now);
coupon.setIsRefunded(false);
```

### 取消订单时
```java
// 清空当前状态
coupon.setStatus(0);
coupon.setUseTime(null);
coupon.setOrderId(null);
// 标记为已退回，但保留历史记录
coupon.setIsRefunded(true);
coupon.setRefundTime(now);
// last_used_order_id 和 last_use_time 保持不变
```

### 查询订单详情时
```java
// 通过历史记录字段查询
QueryWrapper<UserCoupon> wrapper = new QueryWrapper<>();
wrapper.eq("last_used_order_id", orderId);
UserCoupon coupon = userCouponService.getOne(wrapper);
```

## 优势

1. **简洁性**：无需创建额外的历史表，直接在现有表中扩展
2. **数据完整性**：历史记录永久保存，不会因订单取消而丢失
3. **用户体验**：用户可以在已取消订单中看到原本使用的优惠券信息
4. **向后兼容**：不影响现有的优惠券使用逻辑
5. **性能优化**：避免了跨表查询，提高查询效率

## 测试验证

使用 `test_coupon_refund_simple.sql` 脚本可以验证：
1. 优惠券使用时历史记录的保存
2. 订单取消后优惠券的退回
3. 历史记录的保留
4. 订单详情查询的正确性

## 文件清单

### 新增文件
- `database/alter_user_coupon_add_history_fields.sql`
- `database/test_coupon_refund_simple.sql`

### 修改文件
- `server/src/main/java/com/logic/code/entity/UserCoupon.java`
- `server/src/main/java/com/logic/code/service/impl/UserCouponServiceImpl.java`
- `server/src/main/java/com/logic/code/service/OrderService.java`
- `server/src/main/java/com/logic/code/model/vo/OrderDetailVO.java`
- `app/wjhx/pages/ucenter/orderDetail/orderDetail.wxml`
- `app/wjhx/pages/ucenter/orderDetail/orderDetail.wxss`

## 部署说明

1. 执行数据库脚本添加新字段
2. 部署后端代码
3. 更新前端页面
4. 运行测试脚本验证功能

此简化方案避免了复杂的历史表设计，通过在现有表中添加字段的方式实现了同样的功能，更加简洁高效。
