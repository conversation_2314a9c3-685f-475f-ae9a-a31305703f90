const util = require('../utils/util.js');
const api = require('../config/api.js');

/**
 * 订阅消息模板ID
 * 根据实际情况配置
 */
const TMPL_IDS = {
  ORDER_STATUS: 'jAbBQuG3Uc_VxNPTWdhOLeZNeXd7qLhm-hwXytogu1A', // 订单状态变化通知
  EXPRESS_STATUS: 'lkNSGD-0WrxpJWzl0u8MrdkwBfy8-BCVrtMDQJl7zJk', // 物流状态通知
};

/**
 * 请求订阅消息
 */
function requestOrderSubscribeMessage() {
  return new Promise((resolve, reject) => {
    // 先从服务器获取最新的模板ID
    util.request(api.SubscribeMsgTmplIds).then(res => {
      if (res.errno === 0 && res.data && res.data.length > 0) {
        // 使用服务器返回的模板ID
        util.requestSubscribeMessage(res.data)
          .then(subscribeRes => {
            console.log('订阅消息请求成功', subscribeRes);
            resolve(subscribeRes);
          })
          .catch(err => {
            console.log('订阅消息请求失败', err);
            // 即使订阅失败也不影响后续流程
            resolve({});
          });
      } else {
        // 使用本地配置的模板ID
        const tmplIds = [TMPL_IDS.ORDER_STATUS];
        util.requestSubscribeMessage(tmplIds)
          .then(subscribeRes => {
            console.log('使用本地模板ID订阅消息请求成功', subscribeRes);
            resolve(subscribeRes);
          })
          .catch(err => {
            console.log('使用本地模板ID订阅消息请求失败', err);
            // 即使订阅失败也不影响后续流程
            resolve({});
          });
      }
    }).catch(err => {
      console.log('获取订阅消息模板ID失败', err);
      // 使用本地配置的模板ID
      const tmplIds = [TMPL_IDS.ORDER_STATUS];
      util.requestSubscribeMessage(tmplIds)
        .then(subscribeRes => {
          resolve(subscribeRes);
        })
        .catch(err => {
          // 即使订阅失败也不影响后续流程
          resolve({});
        });
    });
  });
}

function noticeAdd(id) {
  util.request(api.MsgNotice, { id: id }).then(function (ress) {
    if (ress.success) {
      console.log("---msg_notice_suc---")
    }
  });
}

function messageNotice() {
  let userInfo = null;
  if (wx.getStorageSync('userInfo')) {
    userInfo = wx.getStorageSync('userInfo')
  }

  if (!(userInfo && userInfo.userLevelId == 1)) {
    return;
  }
  var that = this
  var tmplIds = TMPL_IDS.ORDER_STATUS//模板id
  try {
    wx.getSetting({
      withSubscriptions: true,
      success: function (res) {
        console.log(res)
        if (res.subscriptionsSetting.mainSwitch) {  // 用户打开了订阅消息总开关
          if (res.subscriptionsSetting.itemSettings != null) {   // 用户同意总是保持是否推送消息的选择, 这里表示以后不会再拉起推送消息的授权
            let moIdState = res.subscriptionsSetting.itemSettings[tmplIds];  // 用户同意的消息模板id
            console.log(moIdState)
            if (moIdState && moIdState == 'accept') {
              wx.requestSubscribeMessage({
                tmplIds: [tmplIds],
                success(res) {
                  //成功之后处理业务
                  noticeAdd(tmplIds);
                },
              })
            } else {
              console.log('未匹配到模板')
            }

          }
        } else {
          console.log('订阅消息未开启')
        }
      },
      fail: function (error) {
        console.log(error);
      },
    })
  } catch (error) {

  }

}

function payOrder(orderId) {
  console.log("payOrder ",orderId)
  return new Promise(function (resolve, reject) {
    util.request(api.PayPrepayId, {
      orderId: orderId
    }).then((res) => {
      console.log("PayPrepayId",res)
      if (res.success) {
        console.log("PayPrepayId success ")
        const payParam = res.data;
        
        // 如果返回的支付参数为null，说明是0元订单，直接成功
        if (!payParam) {
          console.log("0元订单，无需微信支付，直接成功");
          resolve({ success: true, message: '0元订单支付成功' });
          return;
        }
        
        wx.requestPayment({
          'timeStamp': payParam.timeStamp,
          'nonceStr': payParam.nonceStr,
          'package': payParam.packageValue,
          'signType': payParam.signType,
          'paySign': payParam.paySign,
          'success': function (res) {
            debugger
            // 支付成功后，请求订阅消息
            // util.request(api.PayNotifySuc, { orderId: orderId }).then(function (ress) {
            //   if (ress.success) {
            //     resolve(res);
            //   }
            // });
            resolve(res);
          },
          'fail': function (res) {
            console.log("pay fail",res)
            reject(res);
          },
          'complete': function (res) {
            console.log("pay complete",res)
            reject(res);
          }
        });
      } else {
        reject(res);
      }
    }).catch(function (error) {
     
      console.error('pay:', error);
    });;
  });
}


module.exports = {
  payOrder,
  requestOrderSubscribeMessage,
  messageNotice
};











