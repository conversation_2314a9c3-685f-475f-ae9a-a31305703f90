### 微信小程序端

# WeShop WeChat UI

## 订阅消息功能实现说明

### 前端实现
1. 在 `app.json` 中添加了订阅消息权限声明
2. 在 `utils/util.js` 中添加了 `requestSubscribeMessage` 通用方法
3. 在 `services/pay.js` 中添加了 `requestOrderSubscribeMessage` 方法，用于请求订阅订单相关的消息
4. 在支付成功后和订单详情页面添加了订阅消息的请求

### 后端实现要求
需要在后端实现以下API接口：

1. `/weshop-wjhx/wechat/subscribe/tmplIds`
   - 方法：GET
   - 功能：获取订阅消息模板ID列表
   - 返回示例：
   ```json
   {
     "errno": 0,
     "data": ["模板ID1", "模板ID2"],
     "success": true
   }
   ```

2. `/weshop-wjhx/wechat/subscribe/send`
   - 方法：POST
   - 功能：发送订阅消息
   - 参数：
     - orderId: 订单ID
     - templateId: 模板ID
     - data: 模板数据
   - 返回示例：
   ```json
   {
     "errno": 0,
     "success": true
   }
   ```

### 订阅消息模板说明
1. 订单状态变化通知：用于通知用户订单状态的变化
2. 物流状态通知：用于通知用户物流状态的变化

### 使用方法
1. 用户支付成功后，自动请求订阅消息授权
2. 用户可以在订单详情页点击"订阅通知"按钮手动订阅消息
