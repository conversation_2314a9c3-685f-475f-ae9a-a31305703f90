// 登录状态测试页面
Page({
  data: {
    userInfo: null,
    token: null,
    hasLogin: false,
    globalUserInfo: null,
    globalToken: null
  },

  onLoad: function () {
    this.checkAllLoginStatus();
  },

  onShow: function () {
    this.checkAllLoginStatus();
  },

  /**
   * 检查所有登录状态
   */
  checkAllLoginStatus: function () {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const app = getApp();

    this.setData({
      userInfo: userInfo,
      token: token,
      hasLogin: !!(userInfo && token),
      globalUserInfo: app.globalData.userInfo,
      globalToken: app.globalData.token
    });

    console.log('=== 登录状态测试 ===');
    console.log('本地存储 userInfo:', userInfo);
    console.log('本地存储 token:', token);
    console.log('全局数据 userInfo:', app.globalData.userInfo);
    console.log('全局数据 token:', app.globalData.token);
    console.log('计算的 hasLogin:', !!(userInfo && token));
  },

  /**
   * 清除登录数据
   */
  clearLoginData: function () {
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('token');
    const app = getApp();
    app.globalData.userInfo = {};
    app.globalData.token = '';

    this.checkAllLoginStatus();

    wx.showToast({
      title: '已清除登录数据',
      icon: 'success'
    });
  },

  /**
   * 模拟登录数据
   */
  mockLoginData: function () {
    const mockUserInfo = {
      id: 1,
      username: 'test_user',
      nickname: '测试用户',
      avatar: 'https://example.com/avatar.jpg',
      mobile: '13800138000'
    };
    const mockToken = 'mock_token_' + Date.now();

    wx.setStorageSync('userInfo', mockUserInfo);
    wx.setStorageSync('token', mockToken);

    const app = getApp();
    app.globalData.userInfo = mockUserInfo;
    app.globalData.token = mockToken;

    this.checkAllLoginStatus();

    wx.showToast({
      title: '已设置模拟登录数据',
      icon: 'success'
    });
  },

  /**
   * 跳转到个人中心
   */
  goToMe: function () {
    wx.switchTab({
      url: '/pages/ucenter/me/me'
    });
  },

  /**
   * 跳转到登录页面
   */
  goToLogin: function () {
    wx.navigateTo({
      url: '/pages/auth/login/login'
    });
  }
});
