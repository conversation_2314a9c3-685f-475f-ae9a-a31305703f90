<view class="container">
  <view class="header">
    <text class="title">登录状态调试</text>
  </view>

  <view class="section">
    <view class="section-title">登录状态</view>
    <view class="status-item">
      <text class="label">登录状态:</text>
      <text class="value {{hasLogin ? 'success' : 'error'}}">{{hasLogin ? '已登录' : '未登录'}}</text>
    </view>
    <view class="status-item">
      <text class="label">本地 userInfo:</text>
      <text class="value">{{userInfo ? '存在' : '不存在'}}</text>
    </view>
    <view class="status-item">
      <text class="label">本地 token:</text>
      <text class="value">{{token ? '存在' : '不存在'}}</text>
    </view>
    <view class="status-item">
      <text class="label">全局 userInfo:</text>
      <text class="value">{{globalUserInfo ? '存在' : '不存在'}}</text>
    </view>
    <view class="status-item">
      <text class="label">全局 token:</text>
      <text class="value">{{globalToken ? '存在' : '不存在'}}</text>
    </view>
  </view>

  <view class="section">
    <view class="section-title">操作按钮</view>
    <view class="button-group">
      <button class="btn primary" bindtap="checkAllLoginStatus">刷新状态</button>
      <button class="btn secondary" bindtap="testCartAPI">测试购物车API</button>
      <button class="btn warning" bindtap="clearLoginInfo">清除登录信息</button>
      <button class="btn success" bindtap="goToLogin">去登录</button>
    </view>
  </view>

  <view class="section" wx:if="{{cartTestResult}}">
    <view class="section-title">购物车API测试结果</view>
    <view class="test-result {{cartTestResult.success ? 'success' : 'error'}}">
      <text class="result-status">{{cartTestResult.success ? '成功' : '失败'}}</text>
      <text class="result-detail">{{cartTestResult.success ? cartTestResult.data.msg || '调用成功' : cartTestResult.error.errMsg || '调用失败'}}</text>
    </view>
  </view>

  <view class="section">
    <view class="section-title">
      <text>调试信息</text>
      <button class="copy-btn" bindtap="copyDebugInfo">复制</button>
    </view>
    <view class="debug-info">
      <view class="debug-line" wx:for="{{debugInfo}}" wx:key="index">{{item}}</view>
    </view>
  </view>
</view>
