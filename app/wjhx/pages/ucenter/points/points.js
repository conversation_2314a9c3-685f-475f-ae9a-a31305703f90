// pages/ucenter/points/points.js
const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userPoints: 0,
    pointsConfig: {},
    pointsRecords: [],
    page: 1,
    size: 20,
    hasMore: true,
    loading: false,
    containerPaddingTop: '200rpx' // 容器顶部间距
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setNavbarPadding();
    this.getUserPointsInfo();
    this.getPointsRecords();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 刷新积分信息
    this.getUserPointsInfo();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({
      page: 1,
      pointsRecords: [],
      hasMore: true
    });
    this.getUserPointsInfo();
    this.getPointsRecords();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.getPointsRecords();
    }
  },

  /**
   * 设置导航栏间距
   */
  setNavbarPadding: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const titleBarHeight = 44; // 导航栏标准高度 44px
    const navbarHeight = statusBarHeight + titleBarHeight;
    const paddingTop = navbarHeight * 2 + 32; // 转换为rpx并添加额外间距

    this.setData({
      containerPaddingTop: paddingTop + 'rpx'
    });
  },

  /**
   * 自定义导航栏返回事件
   */
  onNavBack: function() {
    const pages = getCurrentPages();

    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];

      // 检查是否是 tabBar 页面
      const tabBarPages = [
        'pages/index/index',
        'pages/allGoods/allGoods',
        'pages/cart/cart',
        'pages/ucenter/me/me'
      ];

      if (tabBarPages.includes(prevPage.route)) {
        wx.switchTab({ url: `/${prevPage.route}` });
      } else {
        wx.navigateBack({ delta: 1 });
      }
    } else {
      // 默认返回到个人中心页面
      wx.switchTab({ url: '/pages/ucenter/me/me' });
    }
  },

  /**
   * 获取用户积分信息
   */
  getUserPointsInfo: function () {
    const that = this;
    
    util.request(api.UserPoints).then(function (res) {
      if (res.success) {
        that.setData({
          userPoints: res.data.points || 0,
          pointsConfig: res.data.pointsConfig || {}
        });
      } else {
        wx.showToast({
          title: res.msg || '获取积分信息失败',
          icon: 'none'
        });
      }
    }).catch(function (err) {
      console.error('获取积分信息失败:', err);
      wx.showToast({
        title: '获取积分信息失败',
        icon: 'none'
      });
    });
  },

  /**
   * 获取积分记录
   */
  getPointsRecords: function () {
    const that = this;
    
    if (this.data.loading) {
      return;
    }
    
    this.setData({
      loading: true
    });

    const params = {
      page: this.data.page,
      size: this.data.size
    };

    util.request(api.PointsRecords, params).then(function (res) {
      that.setData({
        loading: false
      });
      
      if (res.success) {
        const records = res.data.records || [];
        const currentRecords = that.data.page === 1 ? [] : that.data.pointsRecords;
        
        that.setData({
          pointsRecords: currentRecords.concat(records),
          page: that.data.page + 1,
          hasMore: records.length >= that.data.size
        });
      } else {
        wx.showToast({
          title: res.msg || '获取积分记录失败',
          icon: 'none'
        });
      }
    }).catch(function (err) {
      that.setData({
        loading: false
      });
      console.error('获取积分记录失败:', err);
      wx.showToast({
        title: '获取积分记录失败',
        icon: 'none'
      });
    });
  },

  /**
   * 格式化积分数量显示
   */
  formatPoints: function (points) {
    if (points > 0) {
      return '+' + points;
    }
    return points.toString();
  },

  /**
   * 格式化时间显示
   */
  formatTime: function (timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    return util.formatTime(date);
  }
});