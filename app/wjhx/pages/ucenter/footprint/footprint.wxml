<view class="container">
  <view class="footprint">
    <view class="day-item" wx:for="{{footprintList}}" wx:key="{{index}}">
      <view class="day-hd">{{item[0].displayTime}}</view>
      <view class="day-list">
        <view class="item" data-footprint="{{iitem}}" bindtouchstart="touchStart" bindtouchend="touchEnd" bindtap="deleteItem" wx:for="{{item}}"  wx:for-item="iitem" wx:key="{{iitem.id}}">
          <image class="img" src="{{iitem.listPicUrl}}"></image>
          <view class="info">
            <view class="name">{{iitem.name}}</view>
            <view class="subtitle">{{iitem.goodsBrief}}</view>
            <view class="price">￥{{iitem.retailPrice}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>