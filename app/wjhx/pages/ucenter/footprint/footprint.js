var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');


var app = getApp();

Page({
    data: {
        footprintList: [],
    },
    getFootprintList() {
        let that = this;
        util.request(api.FootprintList).then(function (res) {
            if (res.success) {
                that.setData({
                    footprintList: res.data
                });
            }
        });
    },
    deleteItem(event) {
        let that = this;
        let footprint = event.currentTarget.dataset.footprint;
        var touchTime = that.data.touch_end - that.data.touch_start;
        console.log(touchTime);
        //如果按下时间大于350为长按
        if (touchTime > 350) {
            wx.showModal({
                title: '',
                content: '要删除所选足迹？',
                success: function (res) {
                    if (res.confirm) {
                        util.request(api.FootprintDelete, {footprintId: footprint.id}, 'POST').then(function (res) {
                            if (res.success) {
                                wx.showToast({
                                    title: '删除成功',
                                    icon: 'success',
                                    duration: 2000
                                });
                                that.getFootprintList();
                            }
                        });
                        console.log('用户点击确定')
                    }
                }
            });
        } else {
            wx.navigateTo({
                url: '/pages/goods/goods?id=' + footprint.goodsId,
            });
        }

    },
    onLoad: function (options) {
        this.getFootprintList();
    },
    onReady: function () {

    },
    onShow: function () {

    },
    onHide: function () {
        // 页面隐藏

    },
    onUnload: function () {
        // 页面关闭
    },
    //按下事件开始
    touchStart: function (e) {
        let that = this;
        that.setData({
            touch_start: e.timeStamp
        })
        console.log(e.timeStamp + '- touch-start')
    },
    //按下事件结束
    touchEnd: function (e) {
        let that = this;
        that.setData({
            touch_end: e.timeStamp
        })
        console.log(e.timeStamp + '- touch-end')
    },
})