page{
    background: #f4f4f4;
    min-height: 100%;
}

.container{
    background: #f4f4f4;
    min-height: 100%;
    padding-top: 30rpx;
}

.fb-type{
  height: 104rpx;
  width: 100%;
  background: #fff;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.fb-type .type-label{
  height: 36rpx;
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

.fb-type .type-icon{
  height: 36rpx;
  width: 36rpx;
}

.fb-body{
  width: 100%;
  background: #fff;
  height: 374rpx;
  padding: 18rpx 30rpx 64rpx 30rpx;
}

.fb-body .content{
  width: 100%;
  height: 100%;
  color: #333;
  line-height: 40rpx;
  font-size: 28rpx;
}

.fb-body .text-count{
  padding-top: 17rpx;
  line-height: 30rpx;
  float: right;
  color: #666;
  font-size: 24rpx;
}

.fb-mobile{
  height: 162rpx;
  width: 100%;
}

.fb-mobile .label{
  height: 58rpx;
  width: 100%;
  padding-top: 14rpx;
  padding-bottom: 11rpx;
  color: #7f7f7f;
  font-size: 24rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  line-height: 33rpx;
}

.fb-mobile .mobile-box{
  height: 104rpx;
  width: 100%;
  color: #333;
  padding-left: 30rpx;
  padding-right: 30rpx;
  font-size: 24rpx;
  background: #fff;
  position: relative;
}

.fb-mobile .mobile{
  position: absolute;
  top: 27rpx;
  left: 30rpx;
  height: 50rpx;
  width: 100%;
  color: #333;
  line-height: 50rpx;
  font-size: 24rpx;
}

.clear-icon{
  position: absolute;
  top: 43rpx;
  right: 30rpx;
  width: 28rpx;
  height: 28rpx;
}

.fb-btn{
  width: 100%;
  height: 98rpx;
  line-height: 98rpx;
  background: #42A5F5;
  position: fixed;
  bottom: 0;
  left: 0;
  border-radius: 0;
  color: #fff;
  font-size: 28rpx;
}