<view class="container">
  <view class='express-header'>
    <view class="left">
      <view class="txt">📦 {{expressInfo.logisticCode || '暂无单号'}}</view>
      <view class="txt">🚚 {{expressInfo.shipperName || '暂无物流公司'}}</view>
      <view class="txt">🕐 {{expressInfo.updateTime || '暂无更新时间'}}</view>
    </view>
    <view class='right' wx:if="{{expressInfo.isFinish === 1}}">
      <view class='update-btn' bindtap="updateExpress">
        <text>🔄 更新物流</text>
      </view>
    </view>
  </view>
  <view class='express-body'>
    <view class='current-icon' wx:if="{{expressTraces.length > 0}}"></view>
    <view class="express-item item-{{index}}" wx:for="{{expressTraces}}" wx:key="{{index}}">
      <view class='left'></view>
      <view class='right'>
        <view class="info">{{item.content}}</view>
        <view class="time">{{item.datetime}}</view>
      </view>
    </view>
    <view class="empty-state" wx:if="{{expressTraces.length === 0}}">
      <view class="empty-icon">🚀</view>
      <view class="empty-text">物流信息正在路上...</view>
    </view>
  </view>
</view>
