page {
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  padding-bottom: 0;
}

.container {
  height: 100%;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.express-header {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: flex-start;
  padding: 40rpx 30rpx;
  margin-bottom: 24rpx;
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.15),
    0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  border-radius: 0 0 24rpx 24rpx;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.express-header .left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.express-header .left .txt {
  font-size: 28rpx;
  line-height: 1.6;
  color: #2d3748;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  padding-left: 40rpx;
}

.express-header .left .txt::before {
  content: '';
  position: absolute;
  left: 0;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.express-header .left .txt:nth-child(2)::before {
  background: linear-gradient(135deg, #48cae4 0%, #0077b6 100%);
  box-shadow: 0 2rpx 8rpx rgba(72, 202, 228, 0.3);
}

.express-header .left .txt:nth-child(3)::before {
  background: linear-gradient(135deg, #06ffa5 0%, #00d4aa 100%);
  box-shadow: 0 2rpx 8rpx rgba(6, 255, 165, 0.3);
}

.express-header .left .txt:first-child {
  font-size: 34rpx;
  font-weight: 700;
  color: #1a202c;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.express-header .left .txt:first-child::before {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
  }
  to {
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.6);
  }
}

.express-header .right {
  display: flex;
  align-items: flex-start;
  margin-left: 20rpx;
}

.express-header .update-btn {
  padding: 18rpx 32rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
  color: #fff;
  border-radius: 30rpx;
  font-size: 26rpx;
  font-weight: 600;
  text-align: center;
  border: none;
  box-shadow:
    0 6rpx 20rpx rgba(255, 107, 107, 0.4),
    0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  transform: scale(1);
  position: relative;
  overflow: hidden;
}

.express-header .update-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.express-header .update-btn:active::before {
  left: 100%;
}

.express-header .update-btn:active {
  transform: scale(0.95);
  box-shadow:
    0 4rpx 16rpx rgba(255, 107, 107, 0.5),
    0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.express-body {
  flex: 1;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 50rpx 30rpx;
  position: relative;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow:
    0 -8rpx 32rpx rgba(102, 126, 234, 0.15),
    0 -2rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-top: auto;
  z-index: 1;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-bottom: none;
}

.express-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg,
    #ff6b6b 0%,
    #feca57 25%,
    #48cae4 50%,
    #06ffa5 75%,
    #667eea 100%);
  border-radius: 24rpx 24rpx 0 0;
}

.current-icon {
  height: 40rpx;
  width: 40rpx;
  position: absolute;
  top: 70rpx;
  left: 50rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
  border-radius: 50%;
  box-shadow:
    0 0 0 8rpx rgba(255, 107, 107, 0.2),
    0 4rpx 16rpx rgba(255, 107, 107, 0.3);
  z-index: 3;
  animation: colorfulPulse 3s infinite;
}

@keyframes colorfulPulse {
  0% {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
    box-shadow:
      0 0 0 8rpx rgba(255, 107, 107, 0.2),
      0 4rpx 16rpx rgba(255, 107, 107, 0.3);
  }
  33% {
    background: linear-gradient(135deg, #48cae4 0%, #06ffa5 50%, #667eea 100%);
    box-shadow:
      0 0 0 12rpx rgba(72, 202, 228, 0.2),
      0 4rpx 16rpx rgba(72, 202, 228, 0.3);
  }
  66% {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    box-shadow:
      0 0 0 12rpx rgba(102, 126, 234, 0.2),
      0 4rpx 16rpx rgba(102, 126, 234, 0.3);
  }
  100% {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
    box-shadow:
      0 0 0 8rpx rgba(255, 107, 107, 0.2),
      0 4rpx 16rpx rgba(255, 107, 107, 0.3);
  }
}

.express-item {
  display: flex;
  margin-left: 30rpx;
  position: relative;
  transition: all 0.3s ease;
  transform: translateX(0);
}

.express-item:hover {
  transform: translateX(10rpx);
}

.express-item::before {
  content: '';
  position: absolute;
  left: 20rpx;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(to bottom,
    #ff6b6b 0%,
    #feca57 25%,
    #48cae4 50%,
    #06ffa5 75%,
    #667eea 100%);
  border-radius: 2rpx;
  z-index: 1;
  opacity: 0.6;
}

.express-item:last-child::before {
  background: linear-gradient(to bottom,
    #ff6b6b 0%,
    #feca57 25%,
    transparent 60%);
}

.express-item .left {
  width: 40rpx;
  height: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 20rpx;
  position: relative;
  z-index: 2;
}

.express-item .left::after {
  content: '';
  width: 20rpx;
  height: 20rpx;
  background: linear-gradient(135deg, #e1e5e9 0%, #f8f9fa 100%);
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.express-item.item-0 .left::after {
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  width: 28rpx;
  height: 28rpx;
  box-shadow:
    0 0 0 6rpx rgba(255, 107, 107, 0.3),
    0 4rpx 16rpx rgba(255, 107, 107, 0.4);
  animation: currentItemGlow 2s ease-in-out infinite alternate;
}

@keyframes currentItemGlow {
  from {
    box-shadow:
      0 0 0 6rpx rgba(255, 107, 107, 0.3),
      0 4rpx 16rpx rgba(255, 107, 107, 0.4);
  }
  to {
    box-shadow:
      0 0 0 10rpx rgba(255, 107, 107, 0.2),
      0 6rpx 20rpx rgba(255, 107, 107, 0.5);
  }
}

.express-item:nth-child(2) .left::after {
  background: linear-gradient(135deg, #48cae4 0%, #06ffa5 100%);
}

.express-item:nth-child(3) .left::after {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.express-item:nth-child(4) .left::after {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.express-item:nth-child(n+5) .left::after {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.express-item .right {
  flex: 1;
  margin-left: 30rpx;
  padding: 20rpx 0 36rpx 0;
  border-bottom: 1rpx solid rgba(241, 243, 244, 0.6);
  position: relative;
}

.express-item .right::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(255, 107, 107, 0.3) 0%,
    rgba(254, 202, 87, 0.3) 25%,
    rgba(72, 202, 228, 0.3) 50%,
    rgba(6, 255, 165, 0.3) 75%,
    rgba(102, 126, 234, 0.3) 100%);
}

.express-item:last-child .right {
  border-bottom: none;
  padding-bottom: 20rpx;
}

.express-item:last-child .right::before {
  display: none;
}

.express-item .right .info {
  font-size: 30rpx;
  line-height: 1.7;
  color: #4a5568;
  margin-bottom: 12rpx;
  padding-right: 20rpx;
  word-wrap: break-word;
  transition: all 0.3s ease;
  position: relative;
}

.express-item.item-0 .right .info {
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  font-size: 32rpx;
  text-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.2);
}

.express-item .right .time {
  font-size: 26rpx;
  color: #718096;
  line-height: 1.5;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.express-item .right .time::before {
  content: '🕐';
  font-size: 20rpx;
  opacity: 0.7;
}

.express-item.item-0 .right .time {
  background: linear-gradient(135deg, #48cae4 0%, #06ffa5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  font-size: 28rpx;
}

.express-item.item-0 .right .time::before {
  content: '⚡';
  background: linear-gradient(135deg, #48cae4 0%, #06ffa5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Empty state styles */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
  position: relative;
}

.empty-state::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg,
    rgba(255, 107, 107, 0.1) 0%,
    rgba(254, 202, 87, 0.1) 25%,
    rgba(72, 202, 228, 0.1) 50%,
    rgba(6, 255, 165, 0.1) 75%,
    rgba(102, 126, 234, 0.1) 100%);
  border-radius: 50%;
  animation: emptyStateFloat 3s ease-in-out infinite;
  z-index: 0;
}

@keyframes emptyStateFloat {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
  }
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 32rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: iconBounce 2s ease-in-out infinite;
  position: relative;
  z-index: 1;
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

.empty-text {
  font-size: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.6;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .express-header {
    padding: 30rpx 24rpx;
  }

  .express-header .left .txt {
    font-size: 26rpx;
    padding-left: 36rpx;
  }

  .express-header .left .txt:first-child {
    font-size: 30rpx;
  }

  .express-header .update-btn {
    padding: 16rpx 28rpx;
    font-size: 24rpx;
  }

  .express-body {
    padding: 40rpx 24rpx;
  }

  .express-item .right .info {
    font-size: 28rpx;
  }

  .express-item.item-0 .right .info {
    font-size: 30rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  page {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #553c9a 100%);
  }

  .express-header {
    background: rgba(45, 55, 72, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.1);
  }

  .express-body {
    background: rgba(45, 55, 72, 0.95);
    border: 1rpx solid rgba(255, 255, 255, 0.1);
  }

  .express-header .left .txt {
    color: #e2e8f0;
  }

  .express-item .right .info {
    color: #cbd5e0;
  }

  .express-item .right .time {
    color: #a0aec0;
  }
}

/* 加载动画 */
.loading-shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8rpx;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #ff5252 0%, #ffc107 100%);
}
