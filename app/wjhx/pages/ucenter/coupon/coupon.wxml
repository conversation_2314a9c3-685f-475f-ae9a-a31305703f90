<!-- pages/ucenter/coupon/coupon.wxml -->
<!-- 自定义导航栏 -->
<custom-navbar
  title="我的优惠券"
  gradient-background="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
  text-color="#ffffff"
  show-back="{{true}}"
  bind:back="onNavBack">
  <view slot="right" class="nav-right-content">
    <view class="coupon-count-badge">
      <text class="count-number">{{totalCount}}</text>
      <text class="count-unit">张</text>
    </view>
  </view>
</custom-navbar>

<view class="container" style="padding-top: {{containerPaddingTop}}">
  <!-- 页面统计信息 -->
  <view class="page-stats">
    <view class="stats-desc">共{{totalCount}}张优惠券</view>
  </view>
  <!-- 状态筛选 -->
  <view class="status-tabs">
    <view class="tab-item {{activeTab === 'available' ? 'active' : ''}}" bindtap="switchTab" data-tab="available">
      <text class="tab-text">可使用</text>
      <text class="tab-count" wx:if="{{availableCount > 0}}">({{availableCount}})</text>
    </view>
    <view class="tab-item {{activeTab === 'used' ? 'active' : ''}}" bindtap="switchTab" data-tab="used">
      <text class="tab-text">已使用</text>
      <text class="tab-count" wx:if="{{usedCount > 0}}">({{usedCount}})</text>
    </view>
    <view class="tab-item {{activeTab === 'expired' ? 'active' : ''}}" bindtap="switchTab" data-tab="expired">
      <text class="tab-text">已过期</text>
      <text class="tab-count" wx:if="{{expiredCount > 0}}">({{expiredCount}})</text>
    </view>
  </view>
  <!-- 优惠券列表 -->
  <view class="coupon-list" wx:if="{{coupons.length > 0}}">
    <view class="coupon-item {{item.status === 'used' ? 'used' : item.status === 'expired' ? 'expired' : ''}}" wx:for="{{coupons}}" wx:key="id">
      <!-- 优惠券主体 -->
      <view class="coupon-main">
        <view class="coupon-left">
          <view class="coupon-amount">
            <text class="currency">¥</text>
            <text class="amount">{{item.amount}}</text>
          </view>
          <view class="coupon-type">{{item.typeName}}</view>
        </view>
        <view class="coupon-right">
          <view class="coupon-title">{{item.title}}</view>
          <view class="coupon-condition">满{{item.minAmount}}元可用</view>
          <view class="coupon-time">
            <text class="time-label">有效期：</text>
            <view class="time-content">
              <text class="time-value">{{item.timeDisplay || '永久有效'}}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 优惠券状态和操作 -->
      <view class="coupon-footer">
        <view class="coupon-status">
          <text class="status-text" wx:if="{{item.status === 'available'}}">可使用</text>
          <text class="status-text used-text" wx:if="{{item.status === 'used'}}">已使用</text>
          <text class="status-text expired-text" wx:if="{{item.status === 'expired'}}">已过期</text>
        </view>
        <view class="coupon-actions" wx:if="{{item.status === 'available'}}">
          <button class="action-btn use-btn" bindtap="useCoupon" data-id="{{item.id}}">立即使用</button>
        </view>
      </view>
      <!-- 优惠券装饰 -->
      <view class="coupon-decoration">
        <view class="decoration-circle decoration-left"></view>
        <view class="decoration-circle decoration-right"></view>
      </view>
    </view>
  </view>
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{coupons.length === 0 && !isLoading}}">
    <image class="empty-icon" src="/static/images/empty-coupon.svg" mode="aspectFit"></image>
    <view class="empty-text">
      <text wx:if="{{activeTab === 'available'}}">暂无可用优惠券</text>
      <text wx:if="{{activeTab === 'used'}}">暂无已使用优惠券</text>
      <text wx:if="{{activeTab === 'expired'}}">暂无过期优惠券</text>
    </view>
    <button class="empty-btn" bindtap="goToRecharge" wx:if="{{activeTab === 'available'}}">
      去充值获取优惠券
    </button>
  </view>
  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && coupons.length > 0}}">
    <button class="load-more-btn" bindtap="loadMore" disabled="{{isLoading}}">
      {{isLoading ? '加载中...' : '加载更多'}}
    </button>
  </view>
  <!-- 底部提示 -->
  <view class="bottom-tip" wx:if="{{coupons.length > 0 && !hasMore}}">已经到底了</view>
</view>