<view class="container">
  <!-- 顶部用户信息区域 - 渐变背景 -->
  <view class="header-section">
    <view class="avatar-row">
      <image class="avatar" wx:if="{{hasLogin && userInfo.avatar}}" src="{{userInfo.avatar}}"></image>
      <image class="avatar" wx:else src="/static/images/sicon/customer.png"></image>
      <view class="user-info">
        <view class="username" wx:if="{{hasLogin}}">{{userInfo.nickname}}</view>
        <view class="username" wx:else bindtap="navigateTo" data-url="/pages/auth/login/login">登录/注册</view>
        <view class="user-tag" wx:if="{{hasLogin}}">
          <image class="tag-icon" src="/static/images/me/vip.png"></image>
          <text>默认等级</text>
        </view>
      </view>
      <view class="header-icons">
        <view class="qr-code" bindtap="showQrCode">
          <image src="/static/images/me/qrcode.png"></image>
        </view>
        <view class="settings" bindtap="showSettings">
          <image src="/static/images/me/settings.png"></image>
        </view>
      </view>
    </view>

    <!-- 用户资产栏 -->
    <view class="user-assets" wx:if="{{hasLogin}}">
      <view class="asset-item" style="display: none;">
        <view class="asset-value">{{userInfo.balance || 0}}</view>
        <view class="asset-label">余额</view>
      </view>
      <view class="asset-item">
        <view class="asset-value">{{userInfo.points || 0}}</view>
        <view class="asset-label">积分</view>
      </view>
      <view class="asset-item" bindtap="navigateToRedemptionRecords">
        <view class="asset-value">{{cardCount || 0}}</view>
        <view class="asset-label">礼券</view>
      </view>
    </view>
    <!-- 未登录状态显示登录按钮 -->
    <view class="login-button-area" wx:else>
      <!-- <button class="login-button" type="primary" open-type="getUserInfo" bindgetuserinfo="onWechatLogin">微信登录</button> -->
       <view class="login-button" bindtap="navigateTo" data-url="/pages/auth/login/login">立即登录</view>
    </view>
  </view>

  <!-- 我的订单区域 -->
  <view class="card-section">
    <view class="section-header">
      <text class="section-title">我的订单</text>
      <view class="more-link" bindtap="navigateToOrderList">
        <text class="more-link-text">查看全部</text>
        <text class="arrow">></text>
      </view>
    </view>
    <view class="order-tools">
      <view class="tool-item" bindtap="navigateTo" data-url="/pages/ucenter/order/order?type=0">
        <image class="tool-icon" src="/static/images/me/money-tax-refund.png"></image>
        <text class="tool-text">待付款</text>
      </view>
      <view class="tool-item" bindtap="navigateTo" data-url="/pages/ucenter/order/order?type=1">
        <image class="tool-icon" src="/static/images/me/logistics-tracking-fill.png"></image>
        <text class="tool-text">待发货</text>
      </view>
      <view class="tool-item" bindtap="navigateTo" data-url="/pages/ucenter/order/order?type=2">
        <image class="tool-icon" src="/static/images/me/logistics-land-transport-fill.png"></image>
        <text class="tool-text">待收货</text>
      </view>
      <!-- <view class="tool-item" bindtap="navigateTo" data-url="/pages/ucenter/order/order?type=3">
        <image class="tool-icon" src="/static/images/me/comment.png"></image>
        <text class="tool-text">评价</text>
      </view> -->
      <view class="tool-item" bindtap="navigateTo" data-url="/pages/ucenter/order/order?type=3">
        <image class="tool-icon" src="/static/images/me/money-finance-seller-fill.png"></image>
        <text class="tool-text">已完成</text>
      </view>
    </view>
  </view>

  <!-- 快捷工具区域 -->
  <view class="card-section">
    <view class="section-header">
      <text class="section-title">快捷工具</text>
      <view class="more-link" bindtap="showMoreTools">
        <text class="more-link-text">更多</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <view class="tools-grid">
      <view class="grid-item" bindtap="navigateTo" data-url="/pages/ucenter/redemption/redemption">
        <image class="grid-icon" src="/static/images/me/gift.png"></image>
        <text class="grid-text">礼券兑换</text>
      </view>
      <view class="grid-item" bindtap="navigateTo" data-url="/pages/ucenter/collect/collect">
        <image class="grid-icon" src="/static/images/me/star.png"></image>
        <text class="grid-text">我的收藏</text>
      </view>
      <!-- <view class="grid-item" bindtap="navigateTo" data-url="/pages/ucenter/coupon/coupon">
        <image class="grid-icon" src="/static/images/me/coupon.png"></image>
        <text class="grid-text">优惠券</text>
      </view> -->
      
      <view class="grid-item" bindtap="navigateTo" data-url="/pages/ucenter/footprint/footprint">
        <image class="grid-icon" src="/static/images/me/footprint.png"></image>
        <text class="grid-text">我的足迹</text>
      </view>
      <view class="grid-item" bindtap="navigateTo" data-url="/pages/shopping/address/address">
        <image class="grid-icon" src="/static/images/me/location.png"></image>
        <text class="grid-text">收货地址</text>
      </view>
      <view class="grid-item customer-service">
        <button open-type="contact" bindcontact="handleContact" session-from="profile" class="contact-button">
          <image class="grid-icon" src="/static/images/me/customerred.png"></image>
          <text class="grid-text">在线客服</text>
        </button>
      </view>
      
    </view>
    
    <view class="tools-grid" style="display: none;" >
      <view class="grid-item" bindtap="navigateTo" data-url="/pages/ucenter/points/points">
        <image class="grid-icon" src="/static/images/me/hot.png"></image>
        <text class="grid-text">热卖推荐</text>
      </view>
      <view class="grid-item" bindtap="navigateTo" data-url="/pages/ucenter/points/points">
        <image class="grid-icon" src="/static/images/me/speaker.png"></image>
        <text class="grid-text">上新公告</text>
      </view>
      <view class="grid-item" bindtap="navigateTo" data-url="/pages/ucenter/balance/balance">
        <image class="grid-icon" src="/static/images/me/wallet-fill.png"></image>
        <text class="grid-text">余额明细</text>
      </view>
      <view class="grid-item" bindtap="navigateTo" data-url="/pages/ucenter/points/points">
        <image class="grid-icon" src="/static/images/me/credit.png"></image>
        <text class="grid-text">积分排行</text>
      </view>
      <view class="grid-item" bindtap="navigateTo" data-url="/pages/ucenter/balance/balance">
        <image class="grid-icon" src="/static/images/me/wallet.png"></image>
        <text class="grid-text">消费排行</text>
      </view>
    </view>
  </view>
  
  <!-- 快捷菜单区域 -->
  <view class="menu-links" style="display: none;" >
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/ucenter/footprint/footprint">
      <image class="menu-icon" src="/static/images/me/footprint.png"></image>
      <text class="menu-text">我的足迹</text>
      <image class="menu-arrow" src="/static/images/me/chevron-right.png"></image>
    </view>
    
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/ucenter/collect/collect">
      <image class="menu-icon" src="/static/images/me/star.png"></image>
      <text class="menu-text">我的收藏</text>
      <image class="menu-arrow" src="/static/images/me/chevron-right.png"></image>
    </view>
    
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/ucenter/points/points">
      <image class="menu-icon" src="/static/images/me/credit.png"></image>
      <text class="menu-text">积分排行</text>
      <image class="menu-arrow" src="/static/images/me/chevron-right.png"></image>
    </view>
    
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/ucenter/balance/balance">
      <image class="menu-icon" src="/static/images/me/wallet.png"></image>
      <text class="menu-text">消费排行</text>
      <image class="menu-arrow" src="/static/images/me/chevron-right.png"></image>
    </view>
    
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/ucenter/distribution/distribution">
      <image class="menu-icon" src="/static/images/me/share.png"></image>
      <text class="menu-text">分销中心</text>
      <image class="menu-arrow" src="/static/images/me/chevron-right.png"></image>
    </view>
  </view>
  
  <!-- 绑定手机号提示栏 - 只在登录后且未绑定手机号时显示 -->
  <view class="bind-phone-bar" wx:if="{{hasLogin && !hasBindPhone}}">
    <image class="wechat-icon" src="/static/images/sicon/wc.png"></image>
    <text class="bind-tip">绑定手机号可获取更多信息</text>
    <view class="bind-btn" bindtap="bindPhoneNumber">立即绑定</view>
  </view>
  
  <!-- 已绑定手机号提示栏 - 只在登录后且已绑定手机号时显示 -->
  <view class="bind-phone-bar phone-bound" wx:if="{{hasLogin && hasBindPhone}}">
    <image class="wechat-icon" src="/static/images/sicon/wc.png"></image>
    <text class="bind-tip">手机号: {{userInfo.mobile || ''}}</text>
    <view class="phone-status">已绑定</view>
  </view>
  
  <view class="logout-btn" wx:if="{{hasSub}}" bindtap="requestSubscribeMsg" >订阅通知</view>

  <!-- 退出登录按钮 - 只在登录后显示 -->
  <view class="logout-btn" bindtap="logout" wx:if="{{hasLogin}}">退出登录</view>
  
  <!-- 底部提示 -->
  <view class="bottom-tip">已经到底了</view>
  
  <!-- 手机绑定弹窗 -->
  <view class="bind-phone-modal" wx:if="{{showBindPhoneModal}}">
    <view class="modal-mask" bindtap="closeBindPhoneModal"></view>
    <view class="modal-dialog">
      <view class="modal-header">
        <text class="modal-icon-text">🔗</text>
      </view>
      <view class="modal-title">需要获取您的手机号</view>
     
      <button class="modal-btn-primary" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">授权获取</button>
      <view class="modal-btn-secondary" bindtap="closeBindPhoneModal">暂不授权</view>
    </view>
  </view>
</view> 