var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');
var user = require('../../../services/user.js');
const pay = require('../../../services/pay.js');
var app = getApp();

Page({
  data: {
    userInfo: {
      nickname: '',
      avatar: '',
      id: '',
      balance: 0,
      points: 0,
      couponCount: 0,
      cardCount: 0,
      giftCardCount: 0,
      collectCount: 0,
      footprintCount: 3
    },
    cardCount: 0,
    hasLogin: false,
    hasBindPhone: false, // 是否已绑定手机号
    showBindPhoneModal: false,
    hasSub: false,
  },

  onLoad: function (options) {
    // 页面初始化
    wx.setStorageSync('fromCheckout', false);
  },

  onReady: function () {
    // 页面渲染完成
    
  },

  onShow: function () {
    // 页面显示
    this.checkLoginStatus();
    // 登录后获取最新的用户信息
    if (this.data.hasLogin) {
      this.getUserInfo();
    }
  },

  onHide: function () {
    // 页面隐藏
  },

  onUnload: function () {
    // 页面关闭
  },

  // 用户点击右上角分享
  onShareAppMessage: function () {
    return {
      title: '伍俊惠选 - 个人中心',
      desc: '精选优质商品，品质生活选择',
      path: '/pages/index/index'
    }
  },

  checkLoginStatus: function () {
    try {
      const token = wx.getStorageSync('token');
      const hasLogin = !!token;
      this.setData({
        hasLogin: hasLogin
      });
      const userInfo = wx.getStorageSync('userInfo');
      const hasBindPhone = userInfo.mobile && userInfo.mobile.length > 0;
      let hasSub = false;
      if(userInfo && userInfo.userLevelId==1){
        hasSub = true;
      }
      this.setData({
        userInfo: userInfo,
        hasBindPhone: hasBindPhone,
        hasSub: hasSub
      });
      console.log('Login status:', hasLogin);
    } catch (e) {
      console.error('Error checking login status:', e);
    }
  },
  onWechatLogin: function (e) {

    if (e.detail.errMsg !== 'getUserInfo:ok') {
      if (e.detail.errMsg === 'getUserInfo:fail auth deny') {
        return false
      }
      wx.showToast({
        title: '微信登录失败',
      })
      return false
    }
    util.login().then((res) => {
      return util.request(api.AuthLoginByWeixin, {
        code: res,
        userInfo: e.detail
      }, 'POST');
    }).then((res) => {
      console.log(res)
      if (res.success == false) {
        wx.showToast({
          title: '微信登录失败',
        })
        return false;
      }
      const hasBindPhone = res.data.mobile && res.data.mobile.length > 0;
      // 设置用户信息
      this.setData({
        userInfo: res.data.userInfo,
        showLoginDialog: false
      });
      app.globalData.userInfo = res.data.userInfo;
      app.globalData.token = res.data.token;
      wx.setStorageSync('userInfo', res.data.userInfo);
      wx.setStorageSync('token', res.data.token);
    }).catch((err) => {
      console.log(err)
    })
  },

  getUserInfo: function () {
    let that = this;
    wx.showLoading({
      title: '加载中...',
    });

    util.request(api.UserInfo).then(function (res) {
      wx.hideLoading();
      if (res.success) {
        // 更新用户数据
        that.setData({
          cardCount: res.data.cardCount
        });
      } else {
        wx.showToast({
          title: res.msg || '获取用户信息失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(function (err) {
      wx.hideLoading();
      console.error('获取用户信息失败:', err);
      wx.showToast({
        title: '获取用户信息失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  navigateTo: function (e) {
    pay.messageNotice()
    let url = e.currentTarget.dataset.url;
    util.toPage(url)
  },

  navigateToOrderList: function () {
    wx.navigateTo({
      url: '/pages/ucenter/order/order'
    });
  },

  navigateToRedemptionRecords: function () {
    // 只有当礼券数不为0时，才跳转到礼券兑换页面的兑换记录选项卡
    if (this.data.cardCount && this.data.cardCount > 0) {
      // 兑换记录选项卡的索引，假设是第二个选项卡 (index=1)
      const tabIndex = 1;
      wx.switchTab({
        url: '/pages/ucenter/redemption/redemption',
        success: function() {
          // 页面跳转成功后，发送自定义事件通知页面切换到指定选项卡
          wx.setStorageSync('redemptionActiveTab', tabIndex);
        }
      });
    }
  },

  showQrCode: function () {
    wx.showToast({
      title: '二维码功能开发中',
      icon: 'none'
    });
  },

  showSettings: function () {
    wx.showToast({
      title: '设置功能开发中',
      icon: 'none'
    });
  },

  bindPhoneNumber: function () {
    // 显示手机号绑定弹窗
    this.setData({
      showBindPhoneModal: true
    });
  },

  getPhoneNumber: function (e) {
    let that = this;
    console.log('getPhoneNumber event:', e);

    // 检查用户是否同意授权获取手机号
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      wx.showToast({
        title: '授权失败',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '手机号绑定中',
    });

    // 关闭弹窗
    this.setData({
      showBindPhoneModal: false
    });

    // 获取登录凭证code
    wx.login({
      success: function (loginRes) {
        if (loginRes.code) {
          // 将code、encryptedData和iv发送到后端解密获取手机号
          util.request(api.BindMobile, {
            code: loginRes.code,
            encryptedData: e.detail.encryptedData,
            iv: e.detail.iv
          }, 'POST').then(function (res) {
            wx.hideLoading();

            if (res.success) {
              // 更新用户信息
              let userInfo = wx.getStorageSync('userInfo');
              userInfo.mobile = res.data.phoneNumber; // 假设后端返回的字段名为phoneNumber

              // 更新全局数据和缓存
              app.globalData.userInfo = userInfo;
              wx.setStorageSync('userInfo', userInfo);

              that.setData({
                userInfo: userInfo,
                hasBindPhone: true
              });

              wx.showToast({
                title: '绑定成功',
                icon: 'success',
                duration: 2000
              });
            } else {
              wx.showToast({
                title: res.msg || '绑定失败',
                icon: 'none',
                duration: 2000
              });
            }
          }).catch(function (err) {
            wx.hideLoading();
            console.error('绑定手机号失败:', err);
            wx.showToast({
              title: '绑定失败，请重试',
              icon: 'none',
              duration: 2000
            });
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '获取登录状态失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: function () {
        wx.hideLoading();
        wx.showToast({
          title: '获取登录状态失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  closeBindPhoneModal: function () {
    // 关闭弹窗
    this.setData({
      showBindPhoneModal: false
    });
  },

  logout: function () {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: function (res) {
        if (res.confirm) {
          // 清除登录信息
          try {
            wx.removeStorageSync('token');
            wx.removeStorageSync('userInfo');
            app.globalData.userInfo = {};
            app.globalData.token = '';

            // 跳转到登录页面
            wx.reLaunch({
              url: '/pages/auth/login/login'
            });
          } catch (e) {
            console.log(e);
          }
        }
      }
    });
  },

  // 点击查看更多工具
  showMoreTools: function () {
    wx.showToast({
      title: '更多工具开发中',
      icon: 'none',
      duration: 2000
    });
  },

  // 处理客服消息回调
  handleContact: function (e) {
    console.log('Customer service contact event:', e);
    console.log('Path:', e.detail.path);
    console.log('Query:', e.detail.query);
    
    // 如果客服消息中包含小程序路径，可以在这里处理跳转
    if (e.detail.path) {
      // 根据路径和参数进行跳转
      wx.navigateTo({
        url: e.detail.path + this.parseQuery(e.detail.query)
      });
    }
  },
  
  // 将查询参数对象转换为URL参数字符串
  parseQuery: function (query) {
    if (!query || Object.keys(query).length === 0) {
      return '';
    }
    
    let queryString = '?';
    for (let key in query) {
      queryString += key + '=' + query[key] + '&';
    }
    // 移除末尾的 '&'
    return queryString.slice(0, -1);
  },
  requestSubscribeMsg:function(){
    pay.requestOrderSubscribeMessage().then(res => {
      wx.showToast({
          title: '订阅成功',
          icon: 'success'
      });
  }).catch(err => {
      wx.showToast({
          title: '订阅失败',
          icon: 'none'
      });
  });
  }
}) 
