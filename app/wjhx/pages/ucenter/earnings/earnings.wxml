<!-- 我的收益页面 -->
<wxs src="../../../utils/format.wxs" module="format" />

<!-- 自定义导航栏 -->
<custom-navbar
  title="我的收益"
  gradient-background="linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%)"
  text-color="#ffffff"
  opacity="{{navOpacity}}"
  bind:back="onNavBack">
  <view slot="right" bindtap="viewEarningsRule">
    <text style="color: #ffffff; font-size: 28rpx;">规则</text>
  </view>
</custom-navbar>

<scroll-view
  class="container"
  style="padding-top: {{navbarHeight}}px;"
  scroll-y="{{true}}"
  bindscroll="onPageScroll"
  enhanced="{{true}}"
  show-scrollbar="{{false}}">
  <!-- 头部收入统计 -->
  <view class="earnings-header">
    <view class="total-earnings">
      <view class="earnings-label">收入累计 (元)</view>
      <view class="total-amount">¥{{totalEarnings || '0.00'}}</view>
    </view>
    <view class="earnings-stats">
      <view class="stat-item">
        <view class="stat-label">本日确认收益</view>
        <view class="stat-amount">¥{{todayEarnings || '0.00'}}</view>
      </view>
      <view class="stat-item">
        <view class="stat-label">上月确认收益</view>
        <view class="stat-amount">¥{{lastMonthEarnings || '0.00'}}</view>
      </view>
      <view class="stat-item">
        <view class="stat-label">待确认收益</view>
        <view class="stat-amount">¥{{pendingEarnings || '0.00'}}</view>
      </view>
    </view>
    <view class="earnings-notice">每月25号可提现上月确认收益的订单收入</view>
  </view>
  <!-- 今日情况和本月情况 - 紧凑布局 -->
  <view class="compact-stats-container">
    <!-- 今日情况 -->
    <view class="compact-section-card">
      <view class="compact-section-header">
        <view class="compact-section-title">日情况</view>
        <view class="compact-detail-link" bindtap="viewTodayDetail">详情</view>
      </view>
      <view class="compact-info-row">
        <view class="compact-info-item">
          <view class="compact-info-label">预估收入</view>
          <view class="compact-info-value red">¥{{todayEstimated || '0.00'}}</view>
        </view>
        <view class="compact-info-item">
          <view class="compact-info-label">订单数</view>
          <view class="compact-info-value">{{todayOrders || 0}}</view>
        </view>
      </view>
    </view>
    <!-- 本月情况 -->
    <view class="compact-section-card">
      <view class="compact-section-header">
        <view class="compact-section-title">月情况</view>
        <view class="compact-detail-link" bindtap="viewMonthDetail">详情</view>
      </view>
      <view class="compact-info-row">
        <view class="compact-info-item">
          <view class="compact-info-label">预估收入</view>
          <view class="compact-info-value red">¥{{monthEstimated || '0.00'}}</view>
        </view>
        <view class="compact-info-item">
          <view class="compact-info-label">订单数</view>
          <view class="compact-info-value">{{monthOrders || 0}}</view>
        </view>
      </view>
    </view>
  </view>
  <!-- 收益明细列表 -->
  <view class="earnings-list" wx:if="{{earningsList.length > 0}}">
    <view class="list-header">
      <text class="list-title">最近收益</text>
      <text class="list-more" bindtap="viewAllEarnings">查看全部</text>
    </view>
    <view class="earnings-item" wx:for="{{earningsList}}" wx:key="id" bindtap="viewEarningsDetail" data-order-id="{{item.orderId}}">
      <!-- 订单头部信息 -->
      <view class="item-header">
        <view class="order-info">
          <view class="order-no">订单号：{{item.orderNo}}</view>
          <view class="order-time">{{item.createTimeFormatted}}</view>
        </view>
        <view class="item-status-wrapper">
          <view class="status-badge {{item.status}}">
            {{item.status === 'confirmed' ? '已确认' : item.status === 'pending' ? '待确认' : '已取消'}}
          </view>
        </view>
      </view>

      <!-- 用户信息 -->
      <view class="customer-info" wx:if="{{item.consignee}}">
        <text class="customer-label">收货人：</text>
        <text class="customer-name">{{item.consignee}}</text>
        <text class="customer-mobile" wx:if="{{item.mobile}}">（{{format.formatMobile(item.mobile)}}）</text>
      </view>

      <!-- 商品信息 -->
      <view class="goods-section" wx:if="{{item.orderGoods && item.orderGoods.length > 0}}">
        <view class="goods-header">
          <text class="goods-title">商品详情</text>
          <text class="goods-count">共{{item.orderGoods.length}}件</text>
        </view>
        <view class="goods-list">
          <view class="goods-item" wx:for="{{item.orderGoods}}" wx:key="{{item.id}}" wx:for-item="goods" wx:if="{{index < 2}}">
            <view class="goods-image-wrapper">
              <image class="goods-image" src="{{format.formatImageUrl(goods.listPicUrl)}}" mode="aspectFill"></image>
            </view>
            <view class="goods-info">
              <view class="goods-name">{{goods.goodsName}}</view>
              <view class="goods-price-row">
                <text class="goods-price">¥{{goods.retailPrice}}</text>
                <text class="goods-quantity">×{{goods.number}}</text>
              </view>
            </view>
          </view>
          <view class="goods-more" wx:if="{{item.orderGoods.length > 2}}">
            <text class="more-text">还有{{item.orderGoods.length - 2}}件商品...</text>
          </view>
        </view>
      </view>

      <!-- 收益信息 -->
      <view class="earnings-summary">
        <view class="summary-row">
          <view class="summary-item main-income">
            <text class="summary-label">收入</text>
            <text class="summary-value income {{item.status === 'confirmed' ? 'confirmed' : 'pending'}}">
              {{item.status === 'confirmed' ? '+' : ''}}¥{{item.commissionAmount || item.amount}}
            </text>
          </view>
          <view class="summary-item">
            <text class="summary-label">订单</text>
            <text class="summary-value">¥{{item.orderAmount || item.amount}}</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">佣金比例</text>
            <text class="summary-value">{{item.commissionRate || '10'}}%</text>
          </view>
        </view>
      </view>

    </view>
  </view>
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{earningsList.length === 0 && !isLoading}}">
    <view class="empty-icon">💰</view>
    <view class="empty-title">暂无收益记录</view>
    <view class="empty-desc">快去推广商品获得佣金收益吧！</view>
    <view class="empty-action" bindtap="goToPromotion">
      <text class="action-icon">🎯</text>
      <text class="action-text">去推广</text>
    </view>
  </view>
  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
</scroll-view>

<!-- 底部操作 - 移到scroll-view外部 -->
<view class="bottom-actions">
  <view class="action-btn primary" bindtap="showWithdrawModal" >
    <text class="btn-icon">💳</text>
    <text class="btn-text">申请提现</text>
  </view>
  <view class="action-btn secondary" bindtap="viewEarningsRule">
    <text class="btn-icon">📋</text>
    <text class="btn-text">收益规则</text>
  </view>
</view>

<!-- 提现申请弹窗 -->
<view class="withdraw-modal" wx:if="{{showWithdrawModal}}">
  <view class="modal-mask" bindtap="closeWithdrawModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">申请提现</text>
      <view class="modal-close" bindtap="closeWithdrawModal">✕</view>
    </view>
    <view class="withdraw-info">
      <view class="info-item">
        <text class="info-label">可提现金额：</text>
        <text class="info-value">¥{{withdrawableAmount || '0.00'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">提现手续费：</text>
        <text class="info-value">¥0.00</text>
      </view>
    </view>
    <view class="withdraw-form">
      <view class="form-item">
        <text class="form-label">提现金额</text>
        <input class="form-input" type="digit" placeholder="请输入提现金额" bindinput="onWithdrawAmountInput" value="{{withdrawAmount}}" />
      </view>
      <view class="form-item">
        <text class="form-label">收款方式</text>
        <picker bindchange="onPaymentMethodChange" value="{{paymentMethodIndex}}" range="{{paymentMethods}}" range-key="name">
          <view class="picker-view">
            {{paymentMethods[paymentMethodIndex].name}}
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>
    </view>
    <view class="withdraw-actions">
      <view class="withdraw-btn cancel" bindtap="closeWithdrawModal">取消</view>
      <view class="withdraw-btn confirm" bindtap="submitWithdraw">确认提现</view>
    </view>
  </view>
</view>
<!-- 收益规则弹窗 -->
<view class="rule-modal" wx:if="{{showRuleModal}}">
  <view class="modal-mask" bindtap="closeRuleModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">收益规则</text>
      <view class="modal-close" bindtap="closeRuleModal">✕</view>
    </view>
    <view class="rule-content">
      <view class="rule-item">
        <view class="rule-title">📊 佣金比例</view>
        <view class="rule-desc">推广用户下单金额的10%作为佣金收益</view>
      </view>
      <view class="rule-item">
        <view class="rule-title">⏰ 结算时间</view>
        <view class="rule-desc">佣金需要在用户确认收货后才能生效</view>
      </view>
      <view class="rule-item">
        <view class="rule-title">💳 提现规则</view>
        <view class="rule-desc">每月25号可提现上月确认收益，无手续费</view>
      </view>
      <view class="rule-item">
        <view class="rule-title">🎯 推广要求</view>
        <view class="rule-desc">仅通过您的推广二维码注册的用户订单才能获得佣金</view>
      </view>
    </view>
    <view class="rule-actions">
      <view class="rule-btn" bindtap="closeRuleModal">我知道了</view>
    </view>
  </view>
</view>