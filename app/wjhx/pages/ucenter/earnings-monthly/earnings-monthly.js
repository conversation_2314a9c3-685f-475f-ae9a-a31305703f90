const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');

Page({
  data: {
    monthlyData: [],
    chartType: 'income', // income 或 orders
    totalStats: {
      income: '0.00',
      orders: 0
    },
    analysis: {
      bestMonth: '',
      avgMonthlyIncome: '0.00',
      trendText: '持平',
      trendClass: 'neutral'
    },
    isLoading: true,
    hasLoaded: false // 标记是否已经加载过数据
  },

  onLoad: function (options) {
    this.loadMonthlyData();
  },

  onShow: function () {
    // 只有在页面已经加载过数据且从后台返回时才重新加载
    if (this.data.hasLoaded) {
      this.loadMonthlyData();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.loadMonthlyData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 加载月度数据
   */
  loadMonthlyData: function () {
    const that = this;
    that.setData({
      isLoading: true
    });

    util.request(api.GetMonthlyEarningsStats).then(res => {
      console.log('月度收益数据:', res);
      
      if (res.success && res.data) {
        const data = res.data;
        const monthlyData = that.processMonthlyData(data.monthlyList || []);
        
        that.setData({
          monthlyData: monthlyData,
          totalStats: {
            income: data.totalStats.income || '0.00',
            orders: data.totalStats.orders || 0
          },
          analysis: that.calculateAnalysis(monthlyData),
          isLoading: false,
          hasLoaded: true // 标记已经加载过数据
        });
      } else {
        that.setData({
          isLoading: false
        });
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('加载月度数据失败:', err);
      that.setData({
        isLoading: false
      });
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 处理月度数据
   */
  processMonthlyData: function (monthlyList) {
    const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月',
                       '七月', '八月', '九月', '十月', '十一月', '十二月'];
    const currentDate = new Date();
    const processedData = [];

    // 生成近6个月的数据（按最新月份降序排序）
    for (let i = 0; i <= 5; i++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const monthKey = `${year}-${String(month).padStart(2, '0')}`;

      // 查找对应月份的数据
      const monthData = monthlyList.find(item => item.month === monthKey) || {};

      // 计算平均日收益
      const daysInMonth = new Date(year, month, 0).getDate();
      const avgDailyIncome = monthData.estimatedIncome ?
        (parseFloat(monthData.estimatedIncome) / daysInMonth).toFixed(2) : '0.00';

      processedData.push({
        month: monthKey,
        year: year,
        monthNumber: month,
        monthName: monthNames[month - 1],
        monthLabel: `${month}月`,
        estimatedIncome: monthData.estimatedIncome || '0.00',
        orderCount: monthData.orderCount || 0,
        avgDailyIncome: avgDailyIncome,
        isCurrentMonth: year === currentDate.getFullYear() && month === currentDate.getMonth() + 1,
        monthTimestamp: date.getTime() // 添加时间戳用于排序
      });
    }
    
    // 计算趋势和增长率（注意：现在数据是按最新月份降序排列）
    for (let i = 0; i < processedData.length; i++) {
      const current = processedData[i];
      const previous = i < processedData.length - 1 ? processedData[i + 1] : null; // 上一个月是下一个索引

      if (previous) {
        const currentIncome = parseFloat(current.estimatedIncome);
        const previousIncome = parseFloat(previous.estimatedIncome);

        if (currentIncome > previousIncome) {
          current.trend = 'up';
          current.trendText = '上升';
        } else if (currentIncome < previousIncome) {
          current.trend = 'down';
          current.trendText = '下降';
        } else {
          current.trend = 'equal';
          current.trendText = '持平';
        }

        // 计算增长率
        if (previousIncome > 0) {
          current.growthRate = (((currentIncome - previousIncome) / previousIncome) * 100).toFixed(1);
        }
      }
    }
    
    // 计算图表百分比
    const maxIncome = Math.max(...processedData.map(item => parseFloat(item.estimatedIncome)));
    const maxOrders = Math.max(...processedData.map(item => item.orderCount));
    
    processedData.forEach(item => {
      item.incomePercent = maxIncome > 0 ? (parseFloat(item.estimatedIncome) / maxIncome * 100) : 0;
      item.ordersPercent = maxOrders > 0 ? (item.orderCount / maxOrders * 100) : 0;
    });

    // 确保按最新月份降序排序（最新月份在前）
    processedData.sort((a, b) => b.monthTimestamp - a.monthTimestamp);

    return processedData;
  },

  /**
   * 计算分析数据
   */
  calculateAnalysis: function (monthlyData) {
    if (monthlyData.length === 0) {
      return {
        bestMonth: '',
        avgMonthlyIncome: '0.00',
        trendText: '持平',
        trendClass: 'neutral'
      };
    }
    
    // 找出最佳月份
    const bestMonth = monthlyData.reduce((best, current) => {
      return parseFloat(current.estimatedIncome) > parseFloat(best.estimatedIncome) ? current : best;
    });
    
    // 计算平均月收益
    const totalIncome = monthlyData.reduce((sum, item) => sum + parseFloat(item.estimatedIncome), 0);
    const avgMonthlyIncome = (totalIncome / monthlyData.length).toFixed(2);
    
    // 分析整体趋势（注意：现在数据是按最新月份降序排列）
    const currentMonth = monthlyData[0]; // 最新月份
    const oldestMonth = monthlyData[monthlyData.length - 1]; // 最早月份
    const currentIncome = parseFloat(currentMonth.estimatedIncome);
    const oldestIncome = parseFloat(oldestMonth.estimatedIncome);

    let trendText = '持平';
    let trendClass = 'neutral';

    if (currentIncome > oldestIncome) {
      trendText = '上升趋势';
      trendClass = 'positive';
    } else if (currentIncome < oldestIncome) {
      trendText = '下降趋势';
      trendClass = 'negative';
    }
    
    return {
      bestMonth: `${bestMonth.year}年${bestMonth.monthNumber}月`,
      avgMonthlyIncome: avgMonthlyIncome,
      trendText: trendText,
      trendClass: trendClass
    };
  },

  /**
   * 切换图表类型
   */
  switchChart: function (e) {
    const chartType = e.currentTarget.dataset.type;
    this.setData({
      chartType: chartType
    });
  },

  /**
   * 查看某月订单明细
   */
  viewMonthOrderDetail: function (e) {
    const month = e.currentTarget.dataset.month;
    const monthName = e.currentTarget.dataset.monthName;
    const year = e.currentTarget.dataset.year;

    wx.navigateTo({
      url: `/pages/ucenter/month-order-detail/month-order-detail?month=${month}&monthName=${monthName}&year=${year}`
    });
  }
});
