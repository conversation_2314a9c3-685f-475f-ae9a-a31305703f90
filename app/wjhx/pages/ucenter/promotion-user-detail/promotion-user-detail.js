const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');

Page({
  data: {
    userId: '',
    userInfo: {},
    ordersList: [],
    filteredOrders: [],
    invitesList: [],
    orderFilter: 'all',
    isLoading: true,
    fromAdmin: false, // 是否从管理员页面进入

    // 自定义导航栏相关
    navOpacity: 0.95,
    navbarHeight: 0,
  },

  onLoad: function (options) {
    const userId = options.userId;
    const nickname = decodeURIComponent(options.nickname || '用户详情');
    const fromAdmin = options.fromAdmin === 'true';
    
    this.setData({
      userId: userId,
      'userInfo.nickname': nickname,
      fromAdmin: fromAdmin
    });
    
    this.initNavbar();
    this.loadUserDetail();
  },

  /**
   * 加载用户详情数据
   */
  loadUserDetail: function () {
    const that = this;
    const userId = that.data.userId;
    
    if (!userId) {
      wx.showToast({
        title: '用户ID不能为空',
        icon: 'none'
      });
      return;
    }

    that.setData({
      isLoading: true
    });

    util.request(api.GetPromotionUserDetail, {
      userId: userId
    }, 'POST').then(res => {
      console.log('推广用户详情:', res);
      
      if (res.success && res.data) {
        const data = res.data;
        
        // 格式化用户信息
        const userInfo = {
          ...data.userInfo,
          promotionTimeFormatted: that.formatDateTime(data.userInfo.promotionTime),
          registerTimeFormatted: that.formatDateTime(data.userInfo.registerTime)
        };

        // 格式化订单列表
        const ordersList = (data.ordersList || []).map(order => ({
          ...order,
          createTimeFormatted: that.formatDateTime(order.createTime),
          orderGoods: order.orderGoods || []
        }));

        // 格式化邀请用户列表
        const invitesList = (data.invitesList || []).map(invite => ({
          ...invite,
          inviteTimeFormatted: that.formatDateTime(invite.inviteTime)
        }));

        that.setData({
          userInfo: userInfo,
          ordersList: ordersList,
          filteredOrders: ordersList,
          invitesList: invitesList,
          isLoading: false
        });
      } else {
        that.setData({
          isLoading: false
        });
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('加载用户详情失败:', err);
      that.setData({
        isLoading: false
      });
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 设置订单筛选条件
   */
  setOrderFilter: function (e) {
    const filterType = e.currentTarget.dataset.type;
    const that = this;
    
    that.setData({
      orderFilter: filterType
    });

    // 根据筛选条件过滤订单
    let filteredOrders = [];
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000);
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    switch (filterType) {
      case 'all':
        filteredOrders = that.data.ordersList;
        break;
      case 'today':
        filteredOrders = that.data.ordersList.filter(order => {
          const createTime = new Date(order.createTime);
          return createTime >= today;
        });
        break;
      case 'week':
        filteredOrders = that.data.ordersList.filter(order => {
          const createTime = new Date(order.createTime);
          return createTime >= weekStart;
        });
        break;
      case 'month':
        filteredOrders = that.data.ordersList.filter(order => {
          const createTime = new Date(order.createTime);
          return createTime >= monthStart;
        });
        break;
    }

    that.setData({
      filteredOrders: filteredOrders
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail: function (e) {
    const orderId = e.currentTarget.dataset.orderId;
    if (orderId) {
      wx.navigateTo({
        url: `/pages/ucenter/orderDetail/orderDetail?id=${orderId}`
      });
    }
  },

  /**
   * 查看邀请用户详情
   */
  viewInviteUserDetail: function (e) {
    const userId = e.currentTarget.dataset.userId;
    if (userId) {
      wx.navigateTo({
        url: `/pages/ucenter/promotion-user-detail/promotion-user-detail?userId=${userId}`
      });
    }
  },

  /**
   * 格式化日期时间
   */
  formatDateTime: function (date) {
    if (!date) return '未知时间';
    
    // 如果传入的不是Date对象，尝试转换
    if (!(date instanceof Date)) {
      date = new Date(date);
    }

    // 检查Date对象是否有效
    if (isNaN(date.getTime())) {
      return '无效日期';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.loadUserDetail();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 初始化导航栏
   */
  initNavbar: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const titleBarHeight = 44;
    const navbarHeight = statusBarHeight + titleBarHeight;

    this.setData({
      navbarHeight: navbarHeight
    });
  },

  /**
   * 导航栏返回事件
   */
  onNavBack: function() {
    // 如果是从管理员页面进入的，返回管理员用户页面
    if (this.data.fromAdmin) {
      wx.navigateTo({
        url: '/pages/ucenter/admin/users/users'
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 页面滚动事件
   */
  onPageScroll: function(e) {
    const scrollTop = e.detail.scrollTop;
    let opacity = Math.min(1, Math.max(0.95, 0.95 + (scrollTop / 200)));

    if (Math.abs(opacity - this.data.navOpacity) > 0.02) {
      this.setData({
        navOpacity: opacity
      });
    }
  }
});