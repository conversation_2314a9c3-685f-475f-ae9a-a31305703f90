<!--收益明细列表页面-->
<wxs src="../../../utils/format.wxs" module="format" />

<!-- 自定义导航栏 -->
<custom-navbar
  title="收益明细"
  gradient-background="linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%)"
  text-color="#ffffff"
  bind:back="onNavBack">
</custom-navbar>

<view class="container" style="padding-top: {{navbarHeight}}px;">
  <!-- 头部筛选 -->
  <view class="filter-header">
    <view class="filter-tabs">
      <view class="filter-tab {{filterStatus === 'all' ? 'active' : ''}}" bindtap="setFilter" data-status="all">
        全部
      </view>
      <view class="filter-tab {{filterStatus === 'confirmed' ? 'active' : ''}}" bindtap="setFilter" data-status="confirmed">
        已确认
      </view>
      <view class="filter-tab {{filterStatus === 'pending' ? 'active' : ''}}" bindtap="setFilter" data-status="pending">
        待确认
      </view>
      <view class="filter-tab {{filterStatus === 'cancelled' ? 'active' : ''}}" bindtap="setFilter" data-status="cancelled">
        已取消
      </view>
    </view>
    
    <!-- 时间筛选 -->
    <view class="time-filter">
      <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
        <view class="date-picker">
          <text class="date-text">{{startDate || '开始日期'}}</text>
          <text class="picker-icon">📅</text>
        </view>
      </picker>
      <text class="date-separator">至</text>
      <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
        <view class="date-picker">
          <text class="date-text">{{endDate || '结束日期'}}</text>
          <text class="picker-icon">📅</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-summary" wx:if="{{summaryStats}}">
    <view class="stats-card">
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-label">总收益</text>
          <text class="stat-value income">¥{{summaryStats.totalEarnings || '0.00'}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">订单数</text>
          <text class="stat-value">{{summaryStats.totalOrders || 0}}单</text>
        </view>
      </view>
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-label">已确认</text>
          <text class="stat-value confirmed">¥{{summaryStats.confirmedEarnings || '0.00'}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">待确认</text>
          <text class="stat-value pending">¥{{summaryStats.pendingEarnings || '0.00'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 收益列表 -->
  <view class="earnings-list" wx:if="{{earningsList.length > 0}}">
    <view class="earnings-item" wx:for="{{earningsList}}" wx:key="id" bindtap="viewEarningsDetail" data-order-id="{{item.orderId}}">
      <!-- 订单头部信息 -->
      <view class="item-header">
        <view class="order-info">
          <view class="order-no">订单号：{{item.orderNo}}</view>
          <view class="order-time">{{item.createTimeFormatted}}</view>
        </view>
        <view class="item-status-wrapper">
          <view class="status-badge {{item.status}}">
            {{item.status === 'confirmed' ? '已确认' : item.status === 'pending' ? '待确认' : '已取消'}}
          </view>
        </view>
      </view>

      <!-- 用户信息 -->
      <view class="customer-info" wx:if="{{item.consignee}}">
        <view class="customer-avatar">
          <image src="{{item.userAvatar || '/static/images/svg/头像.svg'}}" mode="aspectFill"></image>
        </view>
        <view class="customer-detail">
          <view class="customer-name">{{item.consignee}}</view>
          <view class="customer-mobile">{{format.formatMobile(item.mobile) || ''}}</view>
        </view>
      </view>

      <!-- 商品信息 -->
      <view class="goods-section" wx:if="{{item.orderGoods && item.orderGoods.length > 0}}">
        <view class="goods-header">
          <text class="goods-title">商品详情</text>
          <text class="goods-count">共{{item.orderGoods.length}}件商品</text>
        </view>
        <view class="goods-list">
          <view class="goods-item" wx:for="{{item.orderGoods}}" wx:key="{{item.id}}" wx:for-item="goods">
            <view class="goods-image-wrapper">
              <image class="goods-image" src="{{format.formatImageUrl(goods.listPicUrl)}}" mode="aspectFill"></image>
            </view>
            <view class="goods-info">
              <view class="goods-name">{{goods.goodsName}}</view>
              <view class="goods-spec" wx:if="{{goods.goodsSpecificationNameValue}}">{{goods.goodsSpecificationNameValue}}</view>
              <view class="goods-price-row">
                <text class="goods-price">¥{{goods.retailPrice}}</text>
                <text class="goods-quantity">×{{goods.number}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 收益信息 -->
      <view class="earnings-summary">
        <view class="summary-row">
          <view class="summary-item">
            <text class="summary-label">订单金额</text>
            <text class="summary-value">¥{{item.orderAmount || item.amount}}</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">佣金比例</text>
            <text class="summary-value">{{item.commissionRate || '10'}}%</text>
          </view>
        </view>
        <view class="summary-row">
          <view class="summary-item">
            <text class="summary-label">预估收入</text>
            <text class="summary-value income {{item.status === 'confirmed' ? 'confirmed' : 'pending'}}">
              {{item.status === 'confirmed' ? '+' : ''}}¥{{item.commissionAmount || item.amount}}
            </text>
          </view>
          <view class="summary-item" wx:if="{{item.confirmTime}}">
            <text class="summary-label">确认时间</text>
            <text class="summary-value time">{{item.confirmTimeFormatted}}</text>
          </view>
        </view>
      </view>

      <!-- 查看详情箭头 -->
      <view class="detail-arrow">
        <text class="arrow-icon">></text>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !isLoading}}" bindtap="loadMore">
    <text class="load-more-text">加载更多</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">{{earningsList.length > 0 ? '加载更多中...' : '加载中...'}}</view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{earningsList.length === 0 && !isLoading}}">
    <view class="empty-icon">💰</view>
    <view class="empty-title">暂无收益记录</view>
    <view class="empty-desc">
      {{filterStatus === 'all' ? '暂无收益记录' :
        filterStatus === 'confirmed' ? '暂无已确认收益' : 
        filterStatus === 'pending' ? '暂无待确认收益' : '暂无已取消收益'}}
    </view>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && earningsList.length > 0 && !isLoading}}">
    <text class="no-more-text">没有更多数据了</text>
  </view>
</view>
