/* 收益明细列表页面样式 */
.container {
  background: #f8fafc;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 筛选头部 */
.filter-header {
  background: #ffffff;
  padding: 32rpx 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.filter-tabs {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
  background: #f1f5f9;
  border-radius: 12rpx;
  padding: 8rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  font-size: 26rpx;
  color: #64748b;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #667eea;
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.time-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  border-radius: 8rpx;
  padding: 20rpx 16rpx;
  min-width: 240rpx;
}

.date-text {
  font-size: 26rpx;
  color: #334155;
}

.picker-icon {
  font-size: 24rpx;
  margin-left: 8rpx;
}

.date-separator {
  font-size: 24rpx;
  color: #64748b;
  margin: 0 16rpx;
}

/* 统计信息 */
.stats-summary {
  margin: 0 24rpx 24rpx;
}

.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 32rpx;
  color: #ffffff;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-item:not(:last-child) {
  border-right: 1rpx solid rgba(255, 255, 255, 0.2);
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
  display: block;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.stat-value.income {
  font-size: 36rpx;
}

.stat-value.confirmed {
  color: #a7f3d0;
}

.stat-value.pending {
  color: #fde68a;
}

/* 收益列表 */
.earnings-list {
  margin: 0 24rpx;
}

.earnings-item {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.earnings-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border-color: #cbd5e1;
}

.earnings-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 2rpx 2rpx 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.earnings-item:active::before {
  opacity: 1;
}

/* 订单头部信息 - 优化版本 */
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.order-info {
  flex: 1;
}

.order-no {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 6rpx;
  line-height: 1.3;
}

.order-time {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 400;
}

.item-status-wrapper {
  margin-left: 16rpx;
  flex-shrink: 0;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
  min-width: 80rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.status-badge.confirmed {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
  border: 1rpx solid #a7f3d0;
}

.status-badge.pending {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border: 1rpx solid #fcd34d;
}

.status-badge.cancelled {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border: 1rpx solid #fca5a5;
}

/* 用户信息样式 - 优化版本 */
.customer-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8rpx;
  padding: 16rpx;
  border: 1rpx solid #e2e8f0;
  gap: 12rpx;
}

.customer-info::before {
  content: '👤';
  font-size: 20rpx;
  opacity: 0.7;
  flex-shrink: 0;
}

.customer-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  overflow: hidden;
  background: #e2e8f0;
  border: 1rpx solid #cbd5e1;
  flex-shrink: 0;
}

.customer-avatar image {
  width: 100%;
  height: 100%;
}

.customer-detail {
  flex: 1;
  min-width: 0;
}

.customer-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #334155;
  margin-bottom: 4rpx;
  line-height: 1.3;
}

.customer-mobile {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 400;
}

/* 商品信息样式 - 优化版本 */
.goods-section {
  margin-bottom: 16rpx;
}

.goods-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  padding: 0 4rpx;
}

.goods-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #334155;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.goods-title::before {
  content: '📦';
  font-size: 20rpx;
}

.goods-count {
  font-size: 22rpx;
  color: #64748b;
  background: #f1f5f9;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.goods-list {
  background: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;
  border: 1rpx solid #e2e8f0;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.goods-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border-bottom: 1rpx solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-item:active {
  background: #f8fafc;
}

.goods-image-wrapper {
  width: 60rpx;
  height: 60rpx;
  border-radius: 6rpx;
  overflow: hidden;
  margin-right: 16rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  flex-shrink: 0;
}

.goods-image {
  width: 100%;
  height: 100%;
}

.goods-info {
  flex: 1;
  min-width: 0;
}

.goods-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 6rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-spec {
  font-size: 22rpx;
  color: #64748b;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.goods-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 24rpx;
  font-weight: 600;
  color: #dc2626;
}

.goods-quantity {
  font-size: 22rpx;
  color: #64748b;
  background: #f1f5f9;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

/* 收益信息样式 - 优化版本 */
.earnings-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 10rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 0;
  border: 1rpx solid #e2e8f0;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.earnings-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 10rpx 10rpx 0 0;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  gap: 16rpx;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-item {
  flex: 1;
  text-align: center;
  min-width: 0;
  position: relative;
}

.summary-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 32rpx;
  background: #e2e8f0;
}

.summary-item:not(:last-child) {
  border-right: 1rpx solid #e2e8f0;
  margin-right: 16rpx;
  padding-right: 16rpx;
}

.summary-label {
  font-size: 22rpx;
  color: #64748b;
  margin-bottom: 8rpx;
  display: block;
}

.summary-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #1e293b;
}

.summary-value.income {
  color: #dc2626;
  font-size: 28rpx;
}

.summary-value.income.confirmed {
  color: #059669;
}

.summary-value.income.pending {
  color: #d97706;
}

.summary-value.time {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 400;
}

/* 详情箭头 */
.detail-arrow {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 详情箭头 - 优化版本 */
.detail-arrow {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 50%;
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s ease;
}

.arrow-icon {
  font-size: 24rpx;
  color: #94a3b8;
  font-weight: bold;
  transform: rotate(0deg);
  transition: all 0.3s ease;
}

.earnings-item:active .detail-arrow {
  background: #667eea;
  border-color: #667eea;
  transform: translateY(-50%) scale(1.1);
}

.earnings-item:active .arrow-icon {
  color: #ffffff;
  transform: rotate(90deg);
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 32rpx;
  margin: 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 2rpx dashed #e2e8f0;
  transition: all 0.3s ease;
}

.load-more:active {
  background: #f8fafc;
  border-color: #667eea;
}

.load-more-text {
  font-size: 26rpx;
  color: #64748b;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx 32rpx;
  color: #64748b;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e2e8f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #64748b;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
  color: #64748b;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #334155;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  line-height: 1.5;
  opacity: 0.8;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 32rpx;
  margin: 24rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #94a3b8;
  position: relative;
}

.no-more-text::before,
.no-more-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 80rpx;
  height: 1rpx;
  background: #e2e8f0;
}

.no-more-text::before {
  left: -100rpx;
}

.no-more-text::after {
  right: -100rpx;
}
