var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');

Page({
    data: {
        orderList: [],
        currentTab: 'all',
        currentTabText: '全部',
        searchText: '',
        orderStatus: '',
        noOrder: false,
        loading: false,
        showSearch: false, // 控制搜索栏显示/隐藏
        navbarHeight: 0, // 导航栏高度
        orderCounts: {
            all: 0,
            pendingPay: 0,
            pendingShip: 0,
            pendingReceive: 0,
            completed: 0
        },
        statusValMap: {
            '': 'all',
            0: 'pendingPay', // 待付款
            1: 'pendingShip', // 待发货
            2: 'pendingReceive', // 待收货
            3: 'completed' // 已完成
        },
        tabTextMap: {
            'all': '全部',
            'pendingPay': '待付款',
            'pendingShip': '待发货',
            'pendingReceive': '待收货',
            'completed': '已完成'
        }
    },
    onLoad: function (options) {
        // 页面初始化 options为页面跳转所带来的参数
        let type = options.type || '';
        this.setData({
            currentTab: this.data.statusValMap[type],
            orderStatus:type
        });

        this.getOrderList();
    },
    getOrderList() {
        let that = this;

        this.setData({
            loading: true
        });

        util.request(api.OrderList, {
            orderStatus: this.data.orderStatus,
            searchText: this.data.searchText
        }).then(function (res) {
            if (res.success) {
                console.log(res.data);
                let orderList = res.data || [];

                // 计算各状态订单数量
                const orderCounts = {
                    all: orderList.length,
                    pendingPay: orderList.filter(order => order.orderStatus === 0).length,
                    pendingShip: orderList.filter(order => order.orderStatus === 1).length,
                    pendingReceive: orderList.filter(order => order.orderStatus === 2).length,
                    completed: orderList.filter(order => order.orderStatus === 3).length
                };

                // 根据搜索文本过滤
                if (that.data.searchText) {
                    orderList = orderList.filter(order =>
                        order.orderSN.includes(that.data.searchText) ||
                        order.goodsList.some(goods =>
                            goods.goodsName.includes(that.data.searchText)
                        )
                    );
                }

                that.setData({
                    orderList: orderList,
                    orderCounts: orderCounts,
                    noOrder: orderList.length === 0,
                    loading: false
                });
            } else {
                that.setData({
                    loading: false
                });
                util.showErrorToast(res.errmsg || '获取订单列表失败');
            }
        }).catch(function (error) {
            that.setData({
                loading: false
            });
            util.showErrorToast('网络错误，请重试');
        });
    },
    
    // 搜索输入事件
    onSearchInput(e) {
        this.setData({
            searchText: e.detail.value
        });
    },
    
    // 搜索确认事件
    onSearch() {
        this.getOrderList();
    },

    // 清空搜索
    clearSearch() {
        this.setData({
            searchText: ''
        });
        this.getOrderList();
    },

    // 复制订单号
    copyOrderNumber(e) {
        const orderSN = e.currentTarget.dataset.orderSn;
        wx.setClipboardData({
            data: orderSN,
            success: function () {
                wx.showToast({
                    title: '订单号已复制',
                    icon: 'success',
                    duration: 2000
                });
            },
            fail: function () {
                wx.showToast({
                    title: '复制失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        });
    },
    
    // 切换标签事件
    switchTab(e) {
        const tab = e.currentTarget.dataset.tab;
        if (tab === this.data.currentTab) return;

        const statusMap = {
            'all': '',
            'pendingPay': 0, // 待付款
            'pendingShip': 1, // 待发货
            'pendingReceive': 2, // 待收货
            'completed': 3 // 已完成
        };

        const orderStatus = statusMap[tab];

        this.setData({
            currentTab: tab,
            currentTabText: this.data.tabTextMap[tab],
            orderStatus: orderStatus,
            searchText: '' // 切换标签时清空搜索
        });

        this.getOrderList();
    },
    
    // 去付款
    payOrder(e) {
        const orderIndex = e.currentTarget.dataset.orderIndex;
        const order = this.data.orderList[orderIndex];
        wx.navigateTo({
            url: '/pages/pay/pay?orderId=' + order.id + '&actualPrice=' + order.actualPrice,
        });
    },
    // 确认收货
    confirmOrder(e) {
        const that = this;
        const orderId = e.currentTarget.dataset.id;

        // 查找对应的订单信息
        const orderItem = that.data.orderList.find(order => order.id === orderId);

        wx.showModal({
            title: '确认收货',
            content: '确认已收到商品并满意吗？确认后订单将完成。',
            confirmText: '确认收货',
            cancelText: '再想想',
            success(res) {
                if (res.confirm) {
                    wx.showLoading({
                        title: '处理中...'
                    });

                    util.request(api.OrderConfirm, {
                        orderId: orderId
                    }).then(function (res) {
                        wx.hideLoading();
                        if (res.success) {
                            wx.showToast({
                                title: '收货成功',
                                icon: 'success',
                                duration: 1500
                            });

                            // 更新订单列表
                            that.getOrderList();

                            // 延迟跳转到评价页面
                            setTimeout(() => {
                                that.navigateToEvaluate(orderItem);
                            }, 1500);
                        } else {
                            util.showErrorToast(res.errmsg || '确认收货失败');
                        }
                    }).catch(function (error) {
                        wx.hideLoading();
                        util.showErrorToast('网络错误，请重试');
                    });
                }
            }
        });
    },

    // 跳转到商品评价页面
    navigateToEvaluate(orderItem) {
        if (!orderItem || !orderItem.goodsList || orderItem.goodsList.length === 0) {
            console.error('订单信息不完整，无法跳转到评价页面');
            return;
        }

        // 如果订单有多个商品，跳转到第一个商品的评价页面
        // 实际项目中可能需要为每个商品分别评价
        const firstGoods = orderItem.goodsList[0];
        const goodsInfo = {
            goodsName: firstGoods.goodsName,
            listPicUrl: firstGoods.listPicUrl,
            specifications: firstGoods.specifications,
            retailPrice: firstGoods.retailPrice
        };

        wx.navigateTo({
            url: `/pages/goodsEvaluate/goodsEvaluate?orderId=${orderItem.id}&goodsId=${firstGoods.goodsId || firstGoods.id}&goodsInfo=${encodeURIComponent(JSON.stringify(goodsInfo))}`
        });
    },

    // 取消订单
    cancelOrder(e) {
        const that = this;
        const orderId = e.currentTarget.dataset.id;

        // 查找对应的订单信息
        const orderItem = that.data.orderList.find(order => order.id === orderId);

        // 构建取消提示内容
        let content = '确定要取消这个订单吗？取消后无法恢复。';
        const refundItems = [];

        if (orderItem) {
            // 检查是否有优惠券抵扣
            if (orderItem.couponPrice && orderItem.couponPrice > 0) {
                refundItems.push('优惠券');
            }

            // 检查是否有积分抵扣
            if (orderItem.integralMoney && orderItem.integralMoney > 0) {
                refundItems.push('积分');
            }

            // 检查是否有余额抵扣
            if (orderItem.balancePrice && orderItem.balancePrice > 0) {
                refundItems.push('余额');
            }

            // 如果有抵扣项目，添加退回提示
            if (refundItems.length > 0) {
                content += '\n\n已使用的' + refundItems.join('、') + '将自动退回。';
            }
        }

        wx.showModal({
            title: '取消订单',
            content: content,
            confirmText: '确定取消',
            cancelText: '再想想',
            confirmColor: '#ff6b6b',
            success(res) {
                if (res.confirm) {
                    wx.showLoading({
                        title: '处理中...'
                    });

                    util.request(api.OrderCancel, {
                        orderId: orderId
                    }).then(function (res) {
                        wx.hideLoading();
                        if (res.success) {
                            wx.showToast({
                                title: '订单已取消',
                                icon: 'success',
                                duration: 2000
                            });
                            that.getOrderList();
                        } else {
                            util.showErrorToast(res.errmsg || '取消订单失败');
                        }
                    }).catch(function (error) {
                        wx.hideLoading();
                        util.showErrorToast('网络错误，请重试');
                    });
                }
            }
        });
    },
    
    onReady: function () {
        // 页面渲染完成
        this.getNavbarHeight();
    },

    /**
     * 获取导航栏高度
     */
    getNavbarHeight() {
        const systemInfo = wx.getSystemInfoSync();
        const statusBarHeight = systemInfo.statusBarHeight || 0;
        const titleBarHeight = 44; // 标准导航栏高度

        this.setData({
            navbarHeight: statusBarHeight + titleBarHeight
        });
    },

    /**
     * 导航栏返回按钮点击事件
     */
    onNavBack() {
        const pages = getCurrentPages();

        if (pages.length > 1) {
            // 检查上一页是否是 tabBar 页面
            const prevPage = pages[pages.length - 2];

            if (prevPage.route === 'pages/ucenter/me/me') {
                // 如果上一页是 me 页面（tabBar页面），使用 switchTab
                wx.switchTab({
                    url: '/pages/ucenter/me/me'
                });
            } else {
                // 否则正常返回
                wx.navigateBack({
                    delta: 1
                });
            }
        } else {
            // 如果是第一页，跳转到个人中心页面
            wx.switchTab({
                url: '/pages/ucenter/me/me'
            });
        }
    },

    /**
     * 切换搜索栏显示/隐藏
     */
    toggleSearch() {
        this.setData({
            showSearch: !this.data.showSearch
        });
    },
    
    onShow: function () {
        // 页面显示
        this.getOrderList();
    },
    
    onHide: function () {
        // 页面隐藏
    },
    
    onUnload: function () {
        // 页面关闭
    }
})
