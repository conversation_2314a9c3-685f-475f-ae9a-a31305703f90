
<wxs src="../../../utils/format.wxs" module="format" />

<!-- 自定义导航栏 -->
<custom-navbar
  title="我的订单"
  gradient-background="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
  text-color="#ffffff"
  bind:back="onNavBack">
  <view slot="right" bindtap="toggleSearch">
    <text class="search-toggle-icon" style="color: #ffffff; font-size: 32rpx;">🔍</text>
  </view>
</custom-navbar>

<view class="page-wrapper" style="padding-top: {{navbarHeight}}px;">
    <!-- 搜索栏 (可展开/收起) -->
    <view class="search-container {{showSearch ? 'search-expanded' : 'search-collapsed'}}">
        <view class="search-box">
            <view class="search-icon-wrapper">
                <text class="search-icon">🔍</text>
            </view>
            <input
                type="text"
                placeholder="搜索订单或商品"
                confirm-type="search"
                bindinput="onSearchInput"
                bindconfirm="onSearch"
                value="{{searchText}}"
                class="search-input"
            />
            <view class="search-clear" wx:if="{{searchText}}" bindtap="clearSearch">
                <text class="clear-icon">×</text>
            </view>
        </view>
    </view>

    <!-- 状态选项卡 -->
    <view class="tabs-section">
        <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
            <view class="tab-list">
                <view
                    class="tab-item {{currentTab == 'all' ? 'active' : ''}}"
                    bindtap="switchTab"
                    data-tab="all"
                >
                    <view class="tab-content">
                        <text class="tab-label">全部</text>
                        <view class="tab-badge" wx:if="{{orderCounts.all > 0}}">{{orderCounts.all}}</view>
                    </view>
                </view>

                <view
                    class="tab-item {{currentTab == 'pendingPay' ? 'active' : ''}}"
                    bindtap="switchTab"
                    data-tab="pendingPay"
                >
                    <view class="tab-content">
                        <text class="tab-label">待付款</text>
                        <view class="tab-badge" wx:if="{{orderCounts.pendingPay > 0}}">{{orderCounts.pendingPay}}</view>
                    </view>
                </view>

                <view
                    class="tab-item {{currentTab == 'pendingShip' ? 'active' : ''}}"
                    bindtap="switchTab"
                    data-tab="pendingShip"
                >
                    <view class="tab-content">
                        <text class="tab-label">待发货</text>
                        <view class="tab-badge" wx:if="{{orderCounts.pendingShip > 0}}">{{orderCounts.pendingShip}}</view>
                    </view>
                </view>

                <view
                    class="tab-item {{currentTab == 'pendingReceive' ? 'active' : ''}}"
                    bindtap="switchTab"
                    data-tab="pendingReceive"
                >
                    <view class="tab-content">
                        <text class="tab-label">待收货</text>
                        <view class="tab-badge" wx:if="{{orderCounts.pendingReceive > 0}}">{{orderCounts.pendingReceive}}</view>
                    </view>
                </view>

                <view
                    class="tab-item {{currentTab == 'completed' ? 'active' : ''}}"
                    bindtap="switchTab"
                    data-tab="completed"
                >
                    <view class="tab-content">
                        <text class="tab-label">已完成</text>
                        <view class="tab-badge" wx:if="{{orderCounts.completed > 0}}">{{orderCounts.completed}}</view>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>

    <!-- 订单列表 -->
    <view class="main-content">
        <block wx:if="{{orderList.length > 0}}">
            <view class="order-item" wx:for="{{orderList}}" wx:key="index">
                <!-- 订单头部信息 -->
                <view class="order-header">
                    <view class="header-left">
                        <view class="order-meta">
                            <view class="order-id-section">
                                <text class="order-label">订单号</text>
                                <text class="order-number">{{item.orderSN}}</text>
                                <view class="copy-btn" bindtap="copyOrderNumber" data-order-sn="{{item.orderSN}}">
                                    <text class="copy-icon">📋</text>
                                </view>
                            </view>
                            <text class="order-date" wx:if="{{item.addTime}}">{{item.addTime}}</text>
                        </view>
                    </view>
                    <view class="header-right">
                        <view class="order-status status-{{item.orderStatus}}">
                            <text class="status-text">{{item.orderStatusText}}</text>
                        </view>
                    </view>
                </view>

                <!-- 商品展示区域 -->
                <navigator url="../orderDetail/orderDetail?id={{item.id}}" class="goods-container">
                    <view class="goods-list">
                        <view class="product-item" wx:for="{{item.goodsList}}" wx:key="id" wx:for-item="product" wx:for-index="pidx">
                            <view class="product-image-box">
                                <image
                                    src="{{format.formatImageUrl(product.listPicUrl)}}"
                                    class="product-image"
                                    mode="aspectFill"
                                    lazy-load="true"
                                />
                                <view class="quantity-tag">×{{product.number}}</view>
                            </view>
                            <view class="product-info">
                                <view class="product-title">{{product.goodsName}}</view>
                                <view class="product-spec" wx:if="{{product.specifications}}">{{product.specifications}}</view>
                                <view class="product-price-section">
                                    <view class="price-wrapper">
                                        <text class="price-symbol">¥</text>
                                        <text class="price-amount">{{product.retailPrice}}</text>
                                    </view>
                                    <view class="price-unit">单价</view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="goods-summary">
                        <view class="summary-left">
                            <text class="summary-text">共{{item.goodsList.length}}件商品</text>
                            <text class="goods-total">商品总额：¥{{item.goodsPrice || item.actualPrice}}</text>
                        </view>
                        <view class="view-detail">
                            <text class="detail-text">查看详情</text>
                            <text class="arrow-icon">→</text>
                        </view>
                    </view>
                </navigator>

                <!-- 订单底部操作区 -->
                <view class="order-footer">
                    <view class="footer-content">
                        <view class="price-info">
                            <view class="total-amount">
                                <text class="amount-label">实付金额</text>
                                <view class="final-price">
                                    <text class="currency-symbol">¥</text>
                                    <text class="amount-value">{{item.actualPrice}}</text>
                                </view>
                            </view>
                        </view>

                        <view class="operation-area">
                            <view class="btn-group">
                                <button
                                    class="operation-btn cancel-btn"
                                    wx:if="{{item.handleOption.cancel}}"
                                    catchtap="cancelOrder"
                                    data-id="{{item.id}}"
                                >
                                    取消订单
                                </button>

                                <button
                                    class="operation-btn confirm-btn"
                                    wx:if="{{item.handleOption.confirm}}"
                                    catchtap="confirmOrder"
                                    data-id="{{item.id}}"
                                >
                                    确认收货
                                </button>

                                <button
                                    class="operation-btn pay-btn"
                                    wx:if="{{item.handleOption.pay}}"
                                    catchtap="payOrder"
                                    data-order-index="{{index}}"
                                >
                                    立即付款
                                </button>

                                <navigator
                                    class="operation-btn detail-btn"
                                    wx:if="{{item.orderStatus == 3}}"
                                    url="../orderDetail/orderDetail?id={{item.id}}"
                                >
                                    查看详情
                                </navigator>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </block>

        <!-- 空状态展示 -->
        <view class="empty-container" wx:if="{{orderList.length === 0 && !loading}}">
            <view class="empty-illustration">
                <view class="empty-image">📋</view>
                <view class="empty-decoration">
                    <view class="deco-dot dot-1"></view>
                    <view class="deco-dot dot-2"></view>
                    <view class="deco-dot dot-3"></view>
                </view>
            </view>
            <view class="empty-content">
                <text class="empty-title">暂无{{currentTabText}}订单</text>
                <text class="empty-desc">快去挑选心仪的商品吧～</text>
            </view>
            <navigator class="empty-btn" url="/pages/index/index" open-type="switchTab">
                <text class="btn-text">去购物</text>
            </navigator>
        </view>

        <!-- 加载状态 -->
        <view class="loading-container" wx:if="{{loading}}">
            <view class="loading-animation">
                <view class="loading-dot dot1"></view>
                <view class="loading-dot dot2"></view>
                <view class="loading-dot dot3"></view>
            </view>
            <text class="loading-tip">正在加载订单...</text>
        </view>
    </view>
</view>
