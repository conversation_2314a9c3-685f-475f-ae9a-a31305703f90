/* ===== 页面基础样式 ===== */
page {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    color: #333333;
}

.page-wrapper {
    width: 100%;
    min-height: 100vh;
    position: relative;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 30%, #f8f9fa 30%);
}

/* ===== 顶部区域样式 ===== */
.header-section {
    position: relative;
    padding: 60rpx 30rpx 40rpx;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    overflow: hidden;
}

.header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.deco-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: floatShape 8s ease-in-out infinite;
}

.shape-1 {
    width: 200rpx;
    height: 200rpx;
    top: -50rpx;
    right: -50rpx;
    animation-delay: 0s;
}

.shape-2 {
    width: 120rpx;
    height: 120rpx;
    top: 100rpx;
    left: -30rpx;
    animation-delay: 2s;
}

.shape-3 {
    width: 80rpx;
    height: 80rpx;
    bottom: 20rpx;
    right: 100rpx;
    animation-delay: 4s;
}

.page-title {
    text-align: center;
    margin-bottom: 30rpx;
    position: relative;
    z-index: 2;
}

.title-text {
    display: block;
    font-size: 48rpx;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8rpx;
    letter-spacing: 2rpx;
}

.title-subtitle {
    display: block;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
    letter-spacing: 1rpx;
}

.collect-stats {
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.stats-item {
    display: flex;
    align-items: baseline;
    gap: 8rpx;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10rpx);
    padding: 16rpx 24rpx;
    border-radius: 24rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.stats-number {
    font-size: 32rpx;
    font-weight: 700;
    color: #ffffff;
}

.stats-label {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* ===== 主要内容区域 ===== */
.main-content {
    flex: 1;
    padding: 24rpx 20rpx 40rpx;
    background: #f8f9fa;
    border-radius: 32rpx 32rpx 0 0;
    margin-top: -32rpx;
    position: relative;
    z-index: 8;
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
    min-height: 60vh;
}

.collect-container {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
}

.collect-item {
    background: #ffffff;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.9);
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    display: flex;
    align-items: stretch;
}

.collect-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #ff9a9e, #fecfef);
    border-radius: 24rpx 24rpx 0 0;
}

.collect-item:active {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

/* ===== 商品图片区域 ===== */
.product-image-wrapper {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    flex-shrink: 0;
    border-radius: 20rpx;
    overflow: hidden;
    margin: 24rpx;
}

.product-image {
    width: 100%;
    height: 100%;
    border-radius: 20rpx;
    background: #f0f0f0;
    border: 2rpx solid #ffffff;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 154, 158, 0.1) 100%);
    border-radius: 20rpx;
    pointer-events: none;
}

/* ===== 商品信息区域 ===== */
.product-info {
    flex: 1;
    padding: 24rpx 24rpx 24rpx 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 200rpx;
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12rpx;
}

.product-name {
    flex: 1;
    font-size: 32rpx;
    color: #333333;
    font-weight: 600;
    line-height: 1.4;
    margin-right: 16rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
}

.collect-badge {
    flex-shrink: 0;
    width: 48rpx;
    height: 48rpx;
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(255, 154, 158, 0.3);
    animation: heartBeat 2s ease-in-out infinite;
}

.badge-icon {
    font-size: 20rpx;
}

.product-desc {
    font-size: 26rpx;
    color: #8e8e93;
    line-height: 1.4;
    margin-bottom: 16rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-section {
    display: flex;
    align-items: baseline;
    gap: 4rpx;
}

.price-symbol {
    font-size: 28rpx;
    color: #ff6b6b;
    font-weight: 600;
}

.price-amount {
    font-size: 36rpx;
    color: #ff6b6b;
    font-weight: 700;
    letter-spacing: 0.5rpx;
}

.action-buttons {
    display: flex;
    gap: 12rpx;
}

.action-btn {
    height: 64rpx;
    border-radius: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    font-weight: 500;
    transition: all 200ms ease-in-out;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
}

.action-btn:active {
    transform: scale(0.95);
}

.delete-btn {
    width: 64rpx;
    background: rgba(255, 107, 107, 0.1);
    border: 2rpx solid rgba(255, 107, 107, 0.2);
}

.delete-btn:active {
    background: rgba(255, 107, 107, 0.2);
}

.btn-icon {
    font-size: 24rpx;
}

.view-btn {
    padding: 0 24rpx;
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    color: #ffffff;
    box-shadow: 0 4rpx 15rpx rgba(255, 154, 158, 0.3);
    min-width: 100rpx;
}

.view-btn:active {
    box-shadow: 0 2rpx 8rpx rgba(255, 154, 158, 0.4);
}

.btn-text {
    letter-spacing: 0.5rpx;
}

/* ===== 空状态样式 ===== */
.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;
    text-align: center;
    min-height: 60vh;
}

.empty-illustration {
    position: relative;
    margin-bottom: 48rpx;
}

.empty-image {
    font-size: 160rpx;
    opacity: 0.8;
    animation: floatEmpty 3s ease-in-out infinite;
}

.empty-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.deco-heart {
    position: absolute;
    font-size: 24rpx;
    opacity: 0.6;
    animation: heartFloat 3s ease-in-out infinite;
}

.heart-1 {
    top: 20rpx;
    left: 20rpx;
    animation-delay: 0s;
}

.heart-2 {
    top: 40rpx;
    right: 30rpx;
    animation-delay: 1s;
}

.heart-3 {
    bottom: 30rpx;
    left: 40rpx;
    animation-delay: 2s;
}

.empty-content {
    margin-bottom: 48rpx;
}

.empty-title {
    font-size: 36rpx;
    color: #333333;
    font-weight: 600;
    margin-bottom: 16rpx;
    letter-spacing: 1rpx;
}

.empty-desc {
    font-size: 28rpx;
    color: #8e8e93;
    line-height: 1.4;
    font-weight: 400;
}

.empty-btn {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    color: #ffffff;
    padding: 20rpx 48rpx;
    border-radius: 32rpx;
    font-size: 30rpx;
    font-weight: 600;
    box-shadow: 0 8rpx 24rpx rgba(255, 154, 158, 0.4);
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-btn:active {
    transform: scale(0.95) translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(255, 154, 158, 0.5);
}

/* ===== 加载状态样式 ===== */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 40rpx;
    min-height: 40vh;
}

.loading-animation {
    display: flex;
    gap: 8rpx;
    margin-bottom: 32rpx;
}

.loading-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background: #ff9a9e;
    animation: loadingBounce 1.4s ease-in-out infinite both;
}

.dot1 {
    animation-delay: -0.32s;
}

.dot2 {
    animation-delay: -0.16s;
}

.dot3 {
    animation-delay: 0s;
}

.loading-tip {
    font-size: 28rpx;
    color: #8e8e93;
    font-weight: 400;
    letter-spacing: 1rpx;
}

/* ===== 动画效果 ===== */
@keyframes floatShape {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-30rpx) rotate(180deg);
        opacity: 0.8;
    }
}

@keyframes heartBeat {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes floatEmpty {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20rpx);
    }
}

@keyframes heartFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-15rpx) scale(1.2);
        opacity: 0.8;
    }
}

@keyframes loadingBounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* ===== 响应式设计 ===== */
@media screen and (max-width: 750rpx) {
    .header-section {
        padding: 50rpx 20rpx 30rpx;
    }

    .title-text {
        font-size: 42rpx;
    }

    .title-subtitle {
        font-size: 22rpx;
    }

    .main-content {
        padding: 20rpx 16rpx 32rpx;
    }

    .collect-item {
        margin-bottom: 20rpx;
        border-radius: 20rpx;
    }

    .product-image-wrapper {
        width: 160rpx;
        height: 160rpx;
        margin: 20rpx;
    }

    .product-info {
        padding: 20rpx 20rpx 20rpx 0;
        min-height: 160rpx;
    }

    .product-name {
        font-size: 28rpx;
    }

    .product-desc {
        font-size: 24rpx;
    }

    .price-amount {
        font-size: 32rpx;
    }

    .action-btn {
        height: 56rpx;
        font-size: 24rpx;
    }

    .view-btn {
        padding: 0 20rpx;
        min-width: 80rpx;
    }

    .deco-shape {
        opacity: 0.5;
    }

    .shape-1 {
        width: 150rpx;
        height: 150rpx;
    }

    .shape-2 {
        width: 100rpx;
        height: 100rpx;
    }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
    .header-section {
        padding: 40rpx 16rpx 24rpx;
    }

    .title-text {
        font-size: 36rpx;
    }

    .main-content {
        padding: 16rpx 12rpx 28rpx;
    }

    .collect-item {
        border-radius: 16rpx;
    }

    .product-image-wrapper {
        width: 140rpx;
        height: 140rpx;
        margin: 16rpx;
    }

    .product-info {
        padding: 16rpx 16rpx 16rpx 0;
        min-height: 140rpx;
    }

    .product-name {
        font-size: 26rpx;
    }

    .price-amount {
        font-size: 28rpx;
    }

    .action-btn {
        height: 52rpx;
        font-size: 22rpx;
    }

    .action-buttons {
        gap: 8rpx;
    }

    .deco-shape {
        display: none;
    }
}
