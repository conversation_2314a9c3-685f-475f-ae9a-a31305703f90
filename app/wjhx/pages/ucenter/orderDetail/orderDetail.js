var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');
const pay = require('../../../services/pay.js');

Page({
  data: {
    orderId: 0,
    orderInfo: {},
    orderGoods: [],
    handleOption: {}
  },
  onLoad: function (options) {
    this.setData({
      orderId: options.id
    });
    this.getOrderDetail();
  },
  getOrderDetail() {
    let that = this;
    util.request(api.OrderDetail, {
      orderId: that.data.orderId
    }).then(function (res) {
      if (res.success) {
        console.log(res.data);
        // 格式化订单创建时间
        if (res.data.orderInfo && res.data.orderInfo.createTime) {
          res.data.orderInfo.createTime = util.formatOrderTime(res.data.orderInfo.createTime);
        }

        that.setData({
          orderInfo: res.data.orderInfo,
          orderGoods: res.data.orderGoods,
          handleOption: res.data.handleOption
        });
      }
    });
  },

  payOrder() {
    wx.navigateTo({
      url: '/pages/pay/pay?orderId=' + this.data.orderInfo.id + '&actualPrice=' + this.data.orderInfo.actualPrice,
    });
  },
  expressInfo() {
    wx.navigateTo({
      url: '/pages/ucenter/express/express?orderId=' + this.data.orderInfo.id
    });
  },
  cancelOrder() {
    const that = this;

    // 构建取消提示内容
    let content = '确定要取消此订单吗？';
    const orderInfo = that.data.orderInfo;
    const refundItems = [];

    // 检查是否有优惠券抵扣
    if (orderInfo.couponPrice && orderInfo.couponPrice > 0) {
      refundItems.push('优惠券');
    }

    // 检查是否有积分抵扣
    if (orderInfo.integralMoney && orderInfo.integralMoney > 0) {
      refundItems.push('积分');
    }

    // 检查是否有余额抵扣
    if (orderInfo.balancePrice && orderInfo.balancePrice > 0) {
      refundItems.push('余额');
    }

    // 如果有抵扣项目，添加退回提示
    if (refundItems.length > 0) {
      content += '\n\n已使用的' + refundItems.join('、') + '将自动退回。';
    }

    wx.showModal({
      title: '确认取消',
      content: content,
      confirmText: '确定取消',
      cancelText: '再想想',
      confirmColor: '#ff6b6b',
      success(res) {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...'
          });

          util.request(api.OrderCancel, {
            orderId: that.data.orderId
          }).then(function (res) {
            wx.hideLoading();
            if (res.success) {
              wx.showToast({
                title: '订单已取消',
                icon: 'success',
                duration: 2000
              });
              that.getOrderDetail();
            } else {
              util.showErrorToast(res.errmsg || '取消订单失败');
            }
          }).catch(function (error) {
            wx.hideLoading();
            util.showErrorToast('网络错误，请重试');
          });
        }
      }
    });
  },
  onReady: function () {
    // 页面渲染完成
  },
  onShow: function () {
    // 页面显示
  },
  onHide: function () {
    // 页面隐藏
  },
  onUnload: function () {
    // 页面关闭
  },

  // 复制订单号
  copyOrderNumber(e) {
    const orderSN = e.currentTarget.dataset.orderSn;
    wx.setClipboardData({
      data: orderSN,
      success: function () {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success',
          duration: 2000
        });
      },
      fail: function () {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  // 用户点击右上角分享
  onShareAppMessage: function () {
    let that = this;
    // 如果订单中有商品，分享第一个商品
    if (that.data.orderGoods && that.data.orderGoods.length > 0) {
      const firstGoods = that.data.orderGoods[0];
      return {
        title: '我购买了 ' + firstGoods.goodsName,
        desc: '伍俊惠选，品质生活之选',
        path: '/pages/goods/goods?id=' + firstGoods.goodsId
      }
    }
    // 默认分享首页
    return {
      title: '伍俊惠选 - 品质生活',
      desc: '精选优质商品，品质生活选择',
      path: '/pages/index/index'
    }
  },

  // 测试评价功能
  testEvaluate() {
    const orderInfo = this.data.orderInfo;
    const orderGoods = this.data.orderGoods;

    if (!orderGoods || orderGoods.length === 0) {
      wx.showToast({
        title: '订单商品信息不完整',
        icon: 'none'
      });
      return;
    }

    // 使用第一个商品进行测试
    const firstGoods = orderGoods[0];
    const goodsInfo = {
      goodsName: firstGoods.goodsName,
      listPicUrl: firstGoods.listPicUrl,
      specifications: firstGoods.specifications,
      retailPrice: firstGoods.retailPrice
    };

    wx.navigateTo({
      url: `/pages/goodsEvaluate/goodsEvaluate?orderId=${orderInfo.id}&goodsId=${firstGoods.goodsId || firstGoods.id}&goodsInfo=${encodeURIComponent(JSON.stringify(goodsInfo))}`
    });
  }
})
