<wxs src="../../../utils/format.wxs" module="format" />

<view class="page-wrapper">
    <!-- 顶部区域 -->
    <view class="header-section">
        <!-- 背景装饰 -->
        <view class="header-decoration">
            <view class="deco-shape shape-1"></view>
            <view class="deco-shape shape-2"></view>
        </view>

        <!-- 页面标题 -->
        <view class="page-title">
            <text class="title-text">订单详情</text>
            <text class="title-subtitle">Order Details</text>
        </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
        <!-- 订单状态卡片 -->
        <view class="status-card">
            <view class="status-header">
                <view class="status-icon">
                    <text class="icon-text" wx:if="{{orderInfo.orderStatus === 'CANCELLED'}}">❌</text>
                    <text class="icon-text" wx:elif="{{orderInfo.orderStatus === 'COMPLETED'}}">✅</text>
                    <text class="icon-text" wx:elif="{{orderInfo.payStatus === 'PAID'}}">💳</text>
                    <text class="icon-text" wx:else>⏰</text>
                </view>
                <view class="status-info">
                    <text class="status-text">{{orderInfo.orderStatusText}}</text>
                    <text class="status-desc" wx:if="{{orderInfo.orderStatus === 'CANCELLED'}}">订单已取消</text>
                    <text class="status-desc" wx:elif="{{orderInfo.orderStatus === 'COMPLETED'}}">订单已完成</text>
                    <text class="status-desc" wx:elif="{{orderInfo.payStatus === 'PAID'}}">等待发货</text>
                    <text class="status-desc" wx:else>等待付款</text>
                </view>
            </view>

            <view class="order-meta">
                <view class="meta-item">
                    <text class="meta-label">下单时间</text>
                    <text class="meta-value">{{orderInfo.createTime}}</text>
                </view>
                <view class="meta-item">
                    <text class="meta-label">订单编号</text>
                    <text class="meta-value">{{orderInfo.orderSN}}</text>
                    <view class="copy-btn" bindtap="copyOrderNumber" data-order-sn="{{orderInfo.orderSN}}">
                        <text class="copy-icon">📋</text>
                    </view>
                </view>
            </view>

            <view class="action-section">
                <view class="price-display">
                    <text class="price-label">实付金额</text>
                    <view class="price-amount">
                        <text class="currency-symbol">¥</text>
                        <text class="amount-value">{{orderInfo.actualPrice}}</text>
                    </view>
                </view>

                <view class="action-buttons">
                    <button
                        class="action-btn cancel-btn"
                        bindtap="cancelOrder"
                        wx:if="{{orderInfo.orderStatus != 'CANCELLED' && orderInfo.payStatus !== 'PAID'}}"
                    >
                        取消订单
                    </button>
                    <button
                        class="action-btn pay-btn"
                        bindtap="payOrder"
                        wx:if="{{orderInfo.orderStatus != 'CANCELLED' && orderInfo.payStatus !== 'PAID'}}"
                    >
                        立即付款
                    </button>
                    <button
                        class="action-btn express-btn"
                        bindtap="expressInfo"
                        wx:if="{{orderInfo.orderStatus === 'WAIT_RECEIVE' || orderInfo.orderStatus === 'COMPLETED'}}"
                    >
                        物流信息
                    </button>
                    <view
                        class="status-badge paid-badge"
                        wx:if="{{orderInfo.payStatus === 'PAID' && orderInfo.orderStatus !== 'COMPLETED'}}"
                    >
                        已付款
                    </view>
                    <view
                        class="status-badge completed-badge"
                        wx:if="{{orderInfo.orderStatus === 'COMPLETED'}}"
                    >
                        已完成
                    </view>
                    <view
                        class="status-badge cancelled-badge"
                        wx:if="{{orderInfo.orderStatus === 'CANCELLED'}}"
                    >
                        已取消
                    </view>
                </view>
            </view>
        </view>

        <!-- 商品信息卡片 -->
        <view class="goods-card">
            <view class="card-header">
                <view class="header-icon">📦</view>
                <text class="header-title">商品信息</text>
                <text class="goods-count">{{orderGoods.length}}件商品</text>
            </view>

            <view class="goods-list">
                <view class="goods-item" wx:for="{{orderGoods}}" wx:key="{{item.id}}">
                    <view class="goods-image-wrapper">
                        <image
                            class="goods-image"
                            src="{{format.formatImageUrl(item.listPicUrl)}}"
                            mode="aspectFill"
                            lazy-load="true"
                        />
                    </view>
                    <view class="goods-info">
                        <view class="goods-header">
                            <text class="goods-name">{{item.goodsName}}</text>
                            <text class="goods-quantity">×{{item.number}}</text>
                        </view>
                        <view class="goods-spec" wx:if="{{item.goodsSpecificationNameValue}}">
                            {{item.goodsSpecificationNameValue}}
                        </view>
                        <view class="goods-price">¥{{item.retailPrice}}</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 收货地址卡片 -->
        <view class="address-card">
            <view class="card-header">
                <view class="header-icon">📍</view>
                <text class="header-title">收货地址</text>
            </view>

            <view class="address-content">
                <view class="recipient-info">
                    <text class="recipient-name">{{orderInfo.consignee}}</text>
                    <text class="recipient-phone">{{orderInfo.mobile}}</text>
                </view>
                <view class="address-detail">
                    <text class="address-text">{{orderInfo.fullRegion + orderInfo.address}}</text>
                </view>
            </view>
        </view>

        <!-- 费用明细卡片 -->
        <view class="price-card">
            <view class="card-header">
                <view class="header-icon">💰</view>
                <text class="header-title">费用明细</text>
            </view>

            <view class="price-details">
                <view class="price-item">
                    <text class="price-label">商品合计</text>
                    <text class="price-value">¥{{orderInfo.goodsPrice}}</text>
                </view>
                <view class="price-item">
                    <text class="price-label">运费</text>
                    <text class="price-value">¥{{orderInfo.freightPrice}}</text>
                </view>
                <view class="price-item discount-item" wx:if="{{orderInfo.couponPrice > 0}}">
                    <text class="price-label">优惠券</text>
                    <text class="price-value discount-value">-¥{{orderInfo.couponPrice}}</text>
                </view>
                <view class="price-item discount-item" wx:if="{{orderInfo.integralMoney > 0}}">
                    <text class="price-label">积分抵扣</text>
                    <text class="price-value discount-value">-¥{{orderInfo.integralMoney}} ({{orderInfo.integral}}积分)</text>
                </view>
                <view class="price-item discount-item" wx:if="{{orderInfo.balancePrice > 0}}">
                    <text class="price-label">余额抵扣</text>
                    <text class="price-value discount-value">-¥{{orderInfo.balancePrice}}</text>
                </view>
                <view class="price-divider"></view>
                <view class="price-item total-item">
                    <text class="price-label">实付金额</text>
                    <text class="price-value total-value">¥{{orderInfo.actualPrice}}</text>
                </view>
            </view>
        </view>

        <!-- 测试评价按钮 (仅用于测试) -->
        <view class="test-evaluate-section" wx:if="{{orderInfo.orderStatus === 3}}">
            <button class="test-evaluate-btn" bindtap="testEvaluate">测试商品评价</button>
        </view>
    </view>
</view>
