/* ===== 页面基础样式 ===== */
page {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    color: #333333;
}

.page-wrapper {
    width: 100%;
    min-height: 100vh;
    position: relative;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 30%, #f8f9fa 30%);
}

/* ===== 顶部区域样式 ===== */
.header-section {
    position: relative;
    padding: 60rpx 30rpx 40rpx;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    overflow: hidden;
}

.header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.deco-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: floatShape 8s ease-in-out infinite;
}

.shape-1 {
    width: 200rpx;
    height: 200rpx;
    top: -50rpx;
    right: -50rpx;
    animation-delay: 0s;
}

.shape-2 {
    width: 120rpx;
    height: 120rpx;
    top: 100rpx;
    left: -30rpx;
    animation-delay: 3s;
}

.page-title {
    text-align: center;
    position: relative;
    z-index: 2;
}

.title-text {
    display: block;
    font-size: 48rpx;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8rpx;
    letter-spacing: 2rpx;
}

.title-subtitle {
    display: block;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
    letter-spacing: 1rpx;
}

/* ===== 主要内容区域 ===== */
.main-content {
    flex: 1;
    padding: 24rpx 20rpx 40rpx;
    background: #f8f9fa;
    border-radius: 32rpx 32rpx 0 0;
    margin-top: -32rpx;
    position: relative;
    z-index: 8;
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
}

/* ===== 通用卡片样式 ===== */
.status-card,
.goods-card,
.address-card,
.price-card {
    background: #ffffff;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.9);
    transition: all 200ms ease-in-out;
}

.card-header {
    display: flex;
    align-items: center;
    padding: 24rpx 24rpx 16rpx;
    background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
    border-bottom: 2rpx solid #f5f6fa;
}

.header-icon {
    font-size: 28rpx;
    margin-right: 12rpx;
}

.header-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    flex: 1;
}

.goods-count {
    font-size: 24rpx;
    color: #8e8e93;
    background: rgba(142, 142, 147, 0.1);
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
}

/* ===== 订单状态卡片样式 ===== */
.status-header {
    display: flex;
    align-items: center;
    padding: 32rpx 24rpx 24rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.status-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    box-shadow: 0 6rpx 20rpx rgba(79, 172, 254, 0.3);
}

.icon-text {
    font-size: 32rpx;
}

.status-info {
    flex: 1;
}

.status-text {
    font-size: 36rpx;
    font-weight: 700;
    color: #333333;
    display: block;
    margin-bottom: 8rpx;
}

.status-desc {
    font-size: 26rpx;
    color: #8e8e93;
    display: block;
}

.order-meta {
    padding: 0 24rpx 20rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 2rpx solid #f5f6fa;
}

.meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    gap: 16rpx;
}

.meta-item:last-child {
    margin-bottom: 0;
}

.meta-label {
    font-size: 26rpx;
    color: #8e8e93;
    min-width: 120rpx;
}

.meta-value {
    font-size: 26rpx;
    color: #333333;
    font-weight: 500;
    flex: 1;
    font-family: 'Courier New', monospace;
}

.copy-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    height: 48rpx;
    background: rgba(79, 172, 254, 0.1);
    border-radius: 24rpx;
    transition: all 200ms ease;
}

.copy-btn:active {
    background: rgba(79, 172, 254, 0.2);
    transform: scale(0.9);
}

.copy-icon {
    font-size: 20rpx;
    color: #4facfe;
}

.action-section {
    padding: 24rpx;
    background: #ffffff;
}

.price-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24rpx;
    padding: 20rpx;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 16rpx;
    border: 2rpx dashed #e0e0e0;
}

.price-label {
    font-size: 26rpx;
    color: #666666;
    margin-bottom: 8rpx;
}

.price-amount {
    display: flex;
    align-items: baseline;
    gap: 4rpx;
}

.currency-symbol {
    font-size: 32rpx;
    color: #ff6b6b;
    font-weight: 600;
}

.amount-value {
    font-size: 48rpx;
    color: #ff6b6b;
    font-weight: 700;
    letter-spacing: 1rpx;
}

.action-buttons {
    display: flex;
    gap: 16rpx;
    flex-wrap: wrap;
    justify-content: center;
}

.action-btn {
    height: 72rpx;
    line-height: 72rpx;
    padding: 0 32rpx;
    font-size: 28rpx;
    border-radius: 36rpx;
    transition: all 200ms ease-in-out;
    border: none;
    font-weight: 600;
    min-width: 140rpx;
    text-align: center;
    box-sizing: border-box;
}

.action-btn:active {
    transform: scale(0.95);
}

.pay-btn {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: #ffffff;
    box-shadow: 0 6rpx 20rpx rgba(79, 172, 254, 0.4);
}

.express-btn {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: #ffffff;
    box-shadow: 0 6rpx 20rpx rgba(78, 205, 196, 0.4);
}

.cancel-btn {
    background: #ffffff;
    color: #ff6b6b;
    border: 2rpx solid #ff6b6b;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.2);
}

.status-badge {
    padding: 12rpx 24rpx;
    border-radius: 24rpx;
    font-size: 26rpx;
    font-weight: 600;
    text-align: center;
}

.paid-badge {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: #ffffff;
}

.completed-badge {
    background: linear-gradient(135deg, #96c93d, #02aab0);
    color: #ffffff;
}

.cancelled-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: #ffffff;
}

/* ===== 商品列表样式 ===== */
.goods-list {
    padding: 0 24rpx 16rpx;
}

.goods-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f5f6fa;
}

.goods-item:last-child {
    border-bottom: none;
}

.goods-image-wrapper {
    width: 140rpx;
    height: 140rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.goods-image {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
    background: #f0f0f0;
    border: 2rpx solid #ffffff;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.goods-info {
    flex: 1;
    min-width: 0;
}

.goods-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12rpx;
}

.goods-name {
    flex: 1;
    font-size: 30rpx;
    color: #333333;
    font-weight: 500;
    line-height: 1.4;
    margin-right: 16rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
}

.goods-quantity {
    font-size: 28rpx;
    color: #8e8e93;
    flex-shrink: 0;
    background: rgba(142, 142, 147, 0.1);
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
}

.goods-spec {
    font-size: 24rpx;
    color: #8e8e93;
    margin-bottom: 12rpx;
    background: rgba(142, 142, 147, 0.1);
    padding: 6rpx 12rpx;
    border-radius: 8rpx;
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.goods-price {
    font-size: 32rpx;
    color: #ff6b6b;
    font-weight: 700;
    letter-spacing: 0.5rpx;
}

/* ===== 地址卡片样式 ===== */
.address-content {
    padding: 24rpx;
}

.recipient-info {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    gap: 20rpx;
}

.recipient-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
}

.recipient-phone {
    font-size: 28rpx;
    color: #666666;
    font-family: 'Courier New', monospace;
}

.address-detail {
    margin-bottom: 8rpx;
}

.address-text {
    font-size: 28rpx;
    color: #666666;
    line-height: 1.5;
    word-break: break-all;
}

/* ===== 价格明细样式 ===== */
.price-details {
    padding: 16rpx 24rpx 24rpx;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
}

.price-item:last-child {
    margin-bottom: 0;
}

.price-label {
    font-size: 28rpx;
    color: #666666;
    font-weight: 400;
}

.price-value {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
}

.discount-item .price-label {
    color: #ff6b6b;
}

.discount-value {
    color: #ff6b6b !important;
    font-weight: 600;
}

.price-divider {
    height: 1rpx;
    background: #f0f0f0;
    margin: 16rpx 0;
}

.total-item {
    margin-top: 8rpx;
}

.total-item .price-label {
    font-size: 32rpx;
    color: #333333;
    font-weight: 600;
}

.total-value {
    font-size: 36rpx;
    color: #ff6b6b !important;
    font-weight: 700;
}

/* ===== 动画效果 ===== */
@keyframes floatShape {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-30rpx) rotate(180deg);
        opacity: 0.8;
    }
}

/* ===== 响应式设计 ===== */
@media screen and (max-width: 750rpx) {
    .header-section {
        padding: 50rpx 20rpx 30rpx;
    }

    .title-text {
        font-size: 42rpx;
    }

    .title-subtitle {
        font-size: 22rpx;
    }

    .main-content {
        padding: 20rpx 16rpx 32rpx;
    }

    .status-card,
    .goods-card,
    .address-card,
    .price-card {
        margin-bottom: 20rpx;
        border-radius: 20rpx;
    }

    .card-header {
        padding: 20rpx 20rpx 12rpx;
    }

    .header-title {
        font-size: 28rpx;
    }

    .status-header {
        padding: 28rpx 20rpx 20rpx;
    }

    .status-icon {
        width: 70rpx;
        height: 70rpx;
        border-radius: 35rpx;
    }

    .icon-text {
        font-size: 28rpx;
    }

    .status-text {
        font-size: 32rpx;
    }

    .order-meta {
        padding: 0 20rpx 16rpx;
    }

    .action-section {
        padding: 20rpx;
    }

    .amount-value {
        font-size: 42rpx;
    }

    .action-btn {
        height: 64rpx;
        line-height: 64rpx;
        font-size: 26rpx;
        min-width: 120rpx;
        padding: 0 28rpx;
    }

    .goods-list {
        padding: 0 20rpx 12rpx;
    }

    .goods-image-wrapper {
        width: 120rpx;
        height: 120rpx;
    }

    .goods-name {
        font-size: 28rpx;
    }

    .address-content {
        padding: 20rpx;
    }

    .recipient-name {
        font-size: 28rpx;
    }

    .recipient-phone {
        font-size: 26rpx;
    }

    .price-details {
        padding: 12rpx 20rpx 20rpx;
    }

    .deco-shape {
        opacity: 0.5;
    }

    .shape-1 {
        width: 150rpx;
        height: 150rpx;
    }

    .shape-2 {
        width: 100rpx;
        height: 100rpx;
    }
}

/* 超小屏幕适配 */
@media screen and (max-width: 600rpx) {
    .header-section {
        padding: 40rpx 16rpx 24rpx;
    }

    .title-text {
        font-size: 36rpx;
    }

    .main-content {
        padding: 16rpx 12rpx 28rpx;
    }

    .card-header {
        padding: 16rpx 16rpx 8rpx;
    }

    .status-header {
        padding: 24rpx 16rpx 16rpx;
    }

    .status-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 30rpx;
    }

    .icon-text {
        font-size: 24rpx;
    }

    .status-text {
        font-size: 28rpx;
    }

    .order-meta {
        padding: 0 16rpx 12rpx;
    }

    .action-section {
        padding: 16rpx;
    }

    .amount-value {
        font-size: 36rpx;
    }

    .action-btn {
        height: 60rpx;
        line-height: 60rpx;
        font-size: 24rpx;
        min-width: 100rpx;
        padding: 0 24rpx;
    }

    .action-buttons {
        gap: 12rpx;
    }

    .goods-list {
        padding: 0 16rpx 8rpx;
    }

    .goods-image-wrapper {
        width: 100rpx;
        height: 100rpx;
    }

    .goods-name {
        font-size: 26rpx;
    }

    .address-content {
        padding: 16rpx;
    }

    .price-details {
        padding: 8rpx 16rpx 16rpx;
    }

    .deco-shape {
        display: none;
    }
}

/* ===== 测试按钮样式 ===== */
.test-section {
    margin: 30rpx;
    padding: 20rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 20rpx;
}

.test-btn {
    flex: 1;
    height: 88rpx;
    border: none;
    border-radius: 44rpx;
    font-size: 30rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.evaluate-btn {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.comment-btn {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(79, 172, 254, 0.3);
}

.test-btn:active {
    transform: translateY(2rpx);
}

.evaluate-btn:active {
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.comment-btn:active {
    box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.3);
}
