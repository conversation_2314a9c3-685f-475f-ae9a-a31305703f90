.container {
  background-color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
}

/* 顶部导航栏样式 */
.navbar {
  display: flex;
  width: 100%;
  height: 80rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
}

.tab.active {
  color: #07c160;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #07c160;
}

/* 选项卡内容样式 */
.tab-content {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 兑换礼品选项卡 */
.instructions {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  padding: 0 30rpx;
}

.input-area {
  display: flex;
  width: 95%;
  height: 100rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 auto;
  margin-bottom: 150rpx;
  background-color: #ffffff;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08), 
              0 2rpx 4rpx rgba(0, 0, 0, 0.04),
              inset 0 0 2rpx rgba(255, 255, 255, 0.8);
  transform: translateY(-3rpx);
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
}

.input-area::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3rpx;
  background: linear-gradient(to right, #07c160, #49e68a);
  transition: width 0.3s ease;
  z-index: 1;
}

.input-area.focused::after {
  width: 100%;
}

.input-area.focused {
  border-color: rgba(7, 193, 96, 0.3);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1), 
              0 2rpx 6rpx rgba(0, 0, 0, 0.05),
              inset 0 0 2rpx rgba(255, 255, 255, 0.8),
              0 0 0 1rpx rgba(7, 193, 96, 0.2);
  transform: translateY(-4rpx);
}

.input-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
  z-index: 1;
}

.input-area:active {
  transform: translateY(-1rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.06),
              inset 0 1rpx 2rpx rgba(0, 0, 0, 0.04);
  transition: all 0.1s ease;
}

.coupon-input {
  flex: 1;
  height: 100%;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  border: none;
  letter-spacing: 1rpx;
  font-weight: 500;
}

/* Make the placeholder text lighter */
.coupon-input::placeholder {
  color: #bbb;
  font-weight: 400;
  font-size: 26rpx;
}

.scan-btn {
  width: 80rpx;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fafafa;
  border-left: 1rpx solid #f0f0f0;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.scan-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.2s;
}

.scan-btn:active::after {
  background-color: rgba(0, 0, 0, 0.05);
}

.scan-icon {
  width: 40rpx;
  height: 40rpx;
  z-index: 1;
  opacity: 0.6;
  transition: opacity 0.3s;
}

.input-area.focused .scan-icon {
  opacity: 0.9;
}

.redeem-btn-area {
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx;
  padding: 20rpx 0;
  position: relative;
}

.ring-container {
  position: relative;
  width: 320rpx;
  height: 320rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.glow-ring {
  position: absolute;
  width: 320rpx;
  height: 320rpx;
  border-radius: 50%;
  background: transparent;
  border: 3rpx solid rgba(255, 255, 255, 0.7);
  box-shadow: 0 0 20rpx rgba(0, 160, 70, 0.5),
              inset 0 0 20rpx rgba(0, 160, 70, 0.5);
  animation: glowing 3s infinite ease-in-out;
  z-index: 0;
}

@keyframes glowing {
  0%, 100% {
    transform: scale(1.02);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.08);
    opacity: 0.5;
  }
}

.redeem-btn {
  width: 300rpx;
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: radial-gradient(circle at 30% 30%, #ffffff, #e0e0e0);
  color: rgb(70, 70, 70);
  font-size: 40rpx;
  font-weight: 700;
  border-radius: 50%;
  position: relative;
  box-shadow: 12rpx 12rpx 24rpx rgba(0, 0, 0, 0.2),
              -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
              inset 2rpx 2rpx 6rpx rgba(255, 255, 255, 0.9),
              inset -2rpx -2rpx 6rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation: float 3s infinite ease-in-out;
  cursor: pointer;
  z-index: 1;
}

.redeem-btn::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border-radius: 50%;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  opacity: 0.5;
  z-index: -1;
  animation: shadowPulse 3s infinite ease-in-out;
}

@keyframes shadowPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.5;
  }
}

.redeem-btn::before {
  content: '';
  position: absolute;
  top: 5%;
  left: 10%;
  width: 40%;
  height: 20%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  transform: rotate(-30deg);
  filter: blur(5rpx);
  opacity: 0.8;
}

.redeem-btn:active {
  transform: scale(0.95);
  box-shadow: 6rpx 6rpx 12rpx rgba(0, 0, 0, 0.2),
              -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.7),
              inset 4rpx 4rpx 8rpx rgba(0, 0, 0, 0.1),
              inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.5);
  animation-play-state: paused;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
    box-shadow: 12rpx 12rpx 24rpx rgba(0, 0, 0, 0.2),
                -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
                inset 2rpx 2rpx 6rpx rgba(255, 255, 255, 0.9),
                inset -2rpx -2rpx 6rpx rgba(0, 0, 0, 0.1),
                0 8rpx 16rpx rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: translateY(-15rpx);
    box-shadow: 12rpx 24rpx 32rpx rgba(0, 0, 0, 0.25),
                -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
                inset 2rpx 2rpx 6rpx rgba(255, 255, 255, 0.9),
                inset -2rpx -2rpx 6rpx rgba(0, 0, 0, 0.1),
                0 16rpx 30rpx rgba(0, 0, 0, 0.25);
  }
}

.redeem-image {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}

.redeem-icon {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.3;
}

.usage-guide {
  text-align: center;
  margin-top: 80rpx;
  margin-bottom: 40rpx;
}

.guide-link {
  font-size: 28rpx;
  color: #07c160;
  padding: 10rpx 30rpx;
  display: inline-flex;
  align-items: center;
  border: 1rpx solid #07c160;
  border-radius: 30rpx;
  position: relative;
  background-color: #fff;
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.1);
  transition: all 0.3s;
  overflow: hidden;
}

.guide-link::before {
  content: '';
  position: absolute;
  top: -1rpx;
  left: -1rpx;
  right: -1rpx;
  bottom: -1rpx;
  border: 1rpx solid rgba(7, 193, 96, 0.3);
  border-radius: 32rpx;
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.guide-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.guide-arrow {
  margin-left: 6rpx;
  font-size: 24rpx;
}

.guide-link:active {
  background-color: rgba(7, 193, 96, 0.05);
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 2rpx rgba(7, 193, 96, 0.05);
}

/* 兑换记录选项卡 */
.record-list {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}

.record-item {
  position: relative;
  margin-bottom: 30rpx;
  height: 240rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.record-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #333, #111);
  z-index: 1;
}

.card-shine {
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 45%,
    rgba(255, 255, 255, 0.1) 55%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: shineEffect 3s infinite ease-in-out;
  z-index: 2;
}

@keyframes shineEffect {
  0% {
    transform: translate(-100%, -100%) rotate(25deg);
  }
  100% {
    transform: translate(100%, 100%) rotate(25deg);
  }
}

.card-content {
  position: relative;
  padding: 30rpx;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 3;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.card-body {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.status-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin-left: 20rpx;
}

.vip-badge {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #000;
  padding: 5rpx 15rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
  letter-spacing: 1rpx;
}

.card-type {
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
}

.record-info {
  flex: 1;
  padding: 20rpx 0;
}

.record-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.record-name {
  font-size: 34rpx;
  color: #fff;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  flex: 1;
}

.record-time {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.record-status {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8rpx 16rpx;
  min-width: 100rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  cursor: pointer;
}

.record-status:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.record-action {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.use-btn {
  background: linear-gradient(135deg, #07c160, #06ad55);
  color: #fff;
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.4);
  transition: all 0.3s;
}

.use-btn:active {
  background: linear-gradient(135deg, #06ad55, #069c4c);
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.3);
}

.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: rgb(97, 94, 94);
}

/* 蒙层背景 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* 使用说明弹窗样式 */
.instructions-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-height: 85%;
  background-color: #fff;
  border-radius: 12rpx;
  z-index: 1000;
  overflow: hidden;
}

.instructions-content {
  position: relative;
  padding: 40rpx 30rpx;
  max-height: 85vh;
  overflow-y: auto;
}

.close-btn {
  position: absolute;
  top: 10rpx;
  right: 20rpx;
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  z-index: 1001;
}

.instructions-list {
  margin-bottom: 30rpx;
}

.instruction-item {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  margin-bottom: 14rpx;
}

.instructions-banner {
  position: relative;
  margin-top: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  height: 500rpx;
}

.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff5a5f, #ff424a);
}

.banner-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  z-index: 1;
  text-align: center;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffe458;
  margin-bottom: 16rpx;
}

.banner-subtitle {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffe458;
  margin-bottom: 16rpx;
}

.banner-desc {
  font-size: 30rpx;
  color: #fff;
  margin-bottom: 24rpx;
}

.banner-button {
  display: inline-block;
  font-size: 26rpx;
  background-color: #ffe458;
  color: #ff5a5f;
  padding: 14rpx 40rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.banner-products {
  position: absolute;
  bottom: 20rpx;
  right: 0;
  left: 0;
  text-align: center;
  z-index: 2;
}

.product-image {
  width: 90%;
  height: 220rpx;
  opacity: 0.9;
  border-radius: 8rpx;
  box-shadow: 0 5rpx 20rpx rgba(0, 0, 0, 0.15);
}

.banner-coins {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.coin {
  position: absolute;
  background-color: #ffe458;
  opacity: 0.2;
  border-radius: 50%;
}

.coin-1 {
  width: 60rpx;
  height: 60rpx;
  top: 100rpx;
  right: 80rpx;
  animation: float 3s infinite ease-in-out;
}

.coin-2 {
  width: 80rpx;
  height: 80rpx;
  bottom: 150rpx;
  left: 60rpx;
  animation: float 4s infinite ease-in-out;
}

.coin-3 {
  width: 50rpx;
  height: 50rpx;
  top: 200rpx;
  left: 100rpx;
  animation: float 2.5s infinite ease-in-out;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15rpx);
  }
}

/* 密钥输入弹窗样式 */
.password-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 600rpx;
  z-index: 1001;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -45%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

.password-modal-content {
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.15);
}

.password-header {
  position: relative;
  padding: 30rpx 0;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.password-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.password-close {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 40rpx;
  height: 40rpx;
  line-height: 36rpx;
  text-align: center;
  font-size: 40rpx;
  color: #999;
}

.password-body {
  padding: 30rpx;
}

.password-info {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  word-break: break-all;
}

.password-input-area {
  display: flex;
  height: 80rpx;
  border: 1rpx solid #e6e6e6;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  background-color: #fff;
}

.password-input {
  flex: 1;
  height: 100%;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  border: none;
}

.password-toggle {
  width: 80rpx;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.password-eye {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.password-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.password-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.password-btn {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  font-size: 30rpx;
}

.cancel-btn {
  background-color: #f7f7f7;
  color: #666;
  border-radius: 0 0 0 20rpx;
}

.confirm-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 0 0 20rpx 0;
}

/* 商品选择弹窗样式 */
.goods-selection-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 650rpx;
  z-index: 1001;
  animation: modalFadeIn 0.3s ease;
}

.goods-selection-content {
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.15);
}

.goods-selection-header {
  position: relative;
  padding: 30rpx 0;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.goods-selection-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.goods-selection-close {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 40rpx;
  height: 40rpx;
  line-height: 36rpx;
  text-align: center;
  font-size: 40rpx;
  color: #999;
}

.goods-selection-body {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.goods-selection-tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.goods-selection-list {
  max-height: 60vh;
  overflow-y: auto;
}

.goods-selection-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  background-color: #f9f9f9;
  transition: all 0.3s;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.goods-selection-item:active {
  background-color: #f0f0f0;
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.03);
}

.goods-selection-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #fff;
  object-fit: contain;
}

.goods-selection-info {
  flex: 1;
  padding-right: 10rpx;
}

.goods-selection-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-selection-price {
  font-size: 30rpx;
  color: #ff424a;
  font-weight: 500;
}

.goods-selection-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
  width: 80rpx;
  height: 80rpx;
}

.goods-selection-arrow image {
  width: 52rpx;
  height: 52rpx;
}

.goods-selection-footer {
  display: flex;
  justify-content: center;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx 0;
}

.goods-selection-btn {
  width: 80%;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f7f7f7;
  color: #666;
  font-size: 30rpx;
  border-radius: 40rpx;
  transition: all 0.3s;
}

.goods-selection-btn:active {
  background-color: #ececec;
} 