/* 管理员中心样式 */

/* 自定义导航栏右侧内容 */
.nav-right-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-badge {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.admin-icon {
  font-size: 20rpx;
  line-height: 1;
}

.admin-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rpx;
}

.admin-text {
  font-size: 18rpx;
  font-weight: 700;
  line-height: 1;
  letter-spacing: 1rpx;
}

.admin-count {
  font-size: 16rpx;
  font-weight: 500;
  line-height: 1;
  opacity: 0.9;
}

.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 0 30rpx 40rpx;
  /* padding-top 通过内联样式动态设置 */
  box-sizing: border-box;
}

/* 页面欢迎信息 */
.page-welcome {
  text-align: center;
  padding: 30rpx 0 40rpx;
  color: white;
}

.welcome-text {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.welcome-time {
  font-size: 24rpx;
  opacity: 0.7;
  background: rgba(255, 255, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  display: inline-block;
  backdrop-filter: blur(10rpx);
}

/* 统计数据区域 */
.stats-section {
  margin-bottom: 40rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 管理菜单 */
.admin-menu {
  margin-bottom: 40rpx;
}

.menu-section {
  margin-bottom: 30rpx;
}

.menu-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: rgba(102, 126, 234, 0.1);
  border-radius: 15rpx;
  margin: 0 -15rpx;
  padding: 25rpx 15rpx;
}

.item-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-wrapper image {
  width: 50rpx;
  height: 50rpx;
}

/* 不同图标的背景色 */
.order-icon {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.pending-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.user-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.active-user-icon {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.stats-icon {
  background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}

.refresh-icon {
  background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.item-subtitle {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.arrow-icon {
  color: #ccc;
  font-size: 36rpx;
  font-weight: bold;
}

/* 快速操作 */
.quick-actions {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.action-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 15rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.action-btn:active {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(0.95);
}

.btn-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 24rpx;
  color: #666;
}

/* 统计详情弹窗 */
.stats-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.modal-content {
  padding: 30rpx;
}

.stats-detail {
  space-y: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 15rpx;
  }
  
  .stats-card {
    padding: 20rpx;
  }
  
  .stats-number {
    font-size: 32rpx;
  }
}
