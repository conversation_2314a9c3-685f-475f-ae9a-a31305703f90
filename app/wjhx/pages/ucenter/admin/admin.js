// 管理员中心页面
const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');

Page({
  data: {
    hasLogin: false,
    userInfo: {},
    showStatsModal: false,
    containerPaddingTop: '200rpx', // 容器顶部间距
    currentTime: '', // 当前时间
    statsData: {
      totalOrders: 0,
      totalUsers: 0,
      totalAmount: '0.00',
      todayOrders: 0,
      todayNewUsers: 0,
      todayAmount: '0.00',
      monthOrders: 0,
      monthAmount: '0.00'
    }
  },

  onLoad: function (options) {
    this.setNavbarPadding();
    this.updateCurrentTime();
    this.checkAdminPermission();
    this.loadStatsData();
  },

  onShow: function () {
    this.checkAdminPermission();
    this.loadStatsData();
  },

  /**
   * 设置导航栏间距
   */
  setNavbarPadding: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const titleBarHeight = 44; // 导航栏标准高度 44px
    const navbarHeight = statusBarHeight + titleBarHeight;
    const paddingTop = navbarHeight * 2 + 32; // 转换为rpx并添加额外间距

    this.setData({
      containerPaddingTop: paddingTop + 'rpx'
    });
  },

  /**
   * 更新当前时间
   */
  updateCurrentTime: function() {
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
    this.setData({
      currentTime: timeStr
    });
  },

  /**
   * 自定义导航栏返回事件
   */
  onNavBack: function() {
    const pages = getCurrentPages();

    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];

      // 检查是否是 tabBar 页面
      const tabBarPages = [
        'pages/index/index',
        'pages/allGoods/allGoods',
        'pages/cart/cart',
        'pages/ucenter/me/me'
      ];

      if (tabBarPages.includes(prevPage.route)) {
        wx.switchTab({ url: `/${prevPage.route}` });
      } else {
        wx.navigateBack({ delta: 1 });
      }
    } else {
      // 默认返回到个人中心页面
      wx.switchTab({ url: '/pages/ucenter/me/me' });
    }
  },

  /**
   * 检查管理员权限
   */
  checkAdminPermission: function () {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');

      if (!userInfo || !token) {
        this.redirectToLogin();
        return;
      }

      // 检查是否是管理员 (userLevelId == 1)
      if (!userInfo.userLevelId || userInfo.userLevelId != 1) {
        wx.showModal({
          title: '权限不足',
          content: '您没有访问管理员中心的权限',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }

      this.setData({
        hasLogin: true,
        userInfo: userInfo
      });
    } catch (e) {
      console.error('检查管理员权限失败:', e);
      this.redirectToLogin();
    }
  },

  /**
   * 重定向到登录页面
   */
  redirectToLogin: function () {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    });
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
    }, 1500);
  },

  /**
   * 加载统计数据
   */
  loadStatsData: function () {
    if (!this.data.hasLogin) {
      return;
    }

    wx.showLoading({
      title: '加载中...'
    });

    util.request(api.AdminStats).then(res => {
      wx.hideLoading();
      if (res.success) {
        this.setData({
          statsData: {
            totalOrders: res.data.totalOrders || 0,
            totalUsers: res.data.totalUsers || 0,
            totalAmount: res.data.totalAmount || '0.00',
            todayOrders: res.data.todayOrders || 0,
            todayNewUsers: res.data.todayNewUsers || 0,
            todayAmount: res.data.todayAmount || '0.00',
            monthOrders: res.data.monthOrders || 0,
            monthAmount: res.data.monthAmount || '0.00'
          }
        });
      } else {
        // 如果接口不存在，使用模拟数据
        this.setMockData();
      }
    }).catch(err => {
      wx.hideLoading();
      console.log('获取统计数据失败，使用模拟数据:', err);
      this.setMockData();
    });
  },

  /**
   * 设置模拟数据（用于演示）
   */
  setMockData: function () {
    this.setData({
      statsData: {
        totalOrders: 1256,
        totalUsers: 3428,
        totalAmount: '125,680.50',
        todayOrders: 23,
        todayNewUsers: 8,
        todayAmount: '2,350.00',
        monthOrders: 456,
        monthAmount: '45,230.80'
      }
    });
  },

  /**
   * 刷新统计数据
   */
  refreshStats: function () {
    wx.showToast({
      title: '正在刷新...',
      icon: 'loading'
    });
    this.loadStatsData();
  },

  /**
   * 显示统计详情弹窗
   */
  showStatsModal: function () {
    this.setData({
      showStatsModal: true
    });
  },

  /**
   * 关闭统计详情弹窗
   */
  closeStatsModal: function () {
    this.setData({
      showStatsModal: false
    });
  },

  /**
   * 通用导航方法
   */
  navigateTo: function (e) {
    const url = e.currentTarget.dataset.url;
    if (url) {
      wx.navigateTo({
        url: url
      });
    }
  },

  /**
   * 返回到我的页面
   */
  goBack: function () {
    wx.navigateTo({
      url: '/pages/ucenter/me/me'
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage: function () {
    return {
      title: '管理员中心',
      path: '/pages/ucenter/admin/admin',
      imageUrl: ''
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '管理员中心 - 系统管理',
      query: '',
      imageUrl: ''
    };
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.loadStatsData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});
