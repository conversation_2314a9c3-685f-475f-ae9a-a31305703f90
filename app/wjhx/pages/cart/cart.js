var util = require('../../utils/util.js');
var api = require('../../config/api.js');
var user = require('../../services/user.js');
var cartService = require('../../services/cart.js');

var app = getApp();

Page({
  data: {
    cartGoods: [],
    cartTotal: {
      "goodsCount": 0,
      "goodsAmount": 0.00,
      "checkedGoodsCount": 0,
      "checkedGoodsAmount": 0.00
    },
    isEditCart: false,
    checkedAllStatus: true,
    editCartList: [],
    // 推荐商品列表
    recommendGoods: [],
    // 飞入购物车动画相关
    openFly: false,
    flyImg: '',
    flyTop: 0,
    flyLeft: 0,
    flyWidth: 50,
    flyHeight: 50,
    flyTime: 0.8,
    loadingRecommend: false
  },
  onLoad: function (options) {
    // 确保清除任何可能存在的结算页面参数
    wx.removeStorageSync('orderOptions');
  },
  onReady: function () {
    // 页面渲染完成
    wx.removeStorageSync('orderOptions');
  },
  onShow: function () {
    // 确保在每次显示购物车页面时都清除结算页面参数
    wx.removeStorageSync('orderOptions');

    // 检查登录状态，避免频繁跳转
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    console.log('购物车页面onShow - 登录状态检查:', {
      hasUserInfo: !!userInfo,
      hasToken: !!token,
      userInfoType: typeof userInfo,
      tokenType: typeof token
    });

    // 只有在确实登录的情况下才加载购物车
    if (userInfo && token && typeof token === 'string' && token.length > 0) {
      // 页面显示
      this.getCartList();
    } else {
      console.log('用户未登录或登录信息无效，清空购物车显示');
      // 清空购物车显示
      this.setData({
        cartGoods: [],
        cartTotal: {
          "goodsCount": 0,
          "goodsAmount": 0.00,
          "checkedGoodsCount": 0,
          "checkedGoodsAmount": 0.00
        }
      });
    }

    // 推荐商品可以无需登录显示
    this.getRecommendProducts();
  },
  onHide: function () {
    // 页面隐藏

  },
  onUnload: function () {
    // 页面关闭

  },
  // 格式化分享图片URL，确保符合微信分享要求
  formatShareImageUrl: function(url) {
    if (!url) {
      return '';
    }

    // 使用项目的formatImageUrl函数
    let formattedUrl = util.formatImageUrl(url);

    // 如果是localhost，需要替换为实际的域名
    if (formattedUrl.includes('localhost:9999')) {
      // 使用配置文件中的域名
      formattedUrl = formattedUrl.replace('http://localhost:9999', 'https://www.sxwjsm.com');
    }

    // 确保使用HTTPS协议（微信分享要求）
    if (formattedUrl.startsWith('http://')) {
      formattedUrl = formattedUrl.replace('http://', 'https://');
    }

    return formattedUrl;
  },

  // 用户点击右上角分享
  onShareAppMessage: function () {
    let shareImageUrl = '';
    let shareTitle = '伍俊惠选 - 购物车';
    let shareDesc = '查看我的购物车，发现更多优惠商品';

    // 如果购物车有商品，使用第一个商品的图片作为分享图片
    if (this.data.cartGoods && this.data.cartGoods.length > 0) {
      const firstGoods = this.data.cartGoods[0];
      if (firstGoods.listPicUrl) {
        shareImageUrl = this.formatShareImageUrl(firstGoods.listPicUrl);
      }
      shareTitle = `我的购物车有${this.data.cartGoods.length}件商品 - 伍俊惠选`;
    } else if (this.data.recommendGoods && this.data.recommendGoods.length > 0) {
      // 如果购物车为空，使用推荐商品的图片
      const firstRecommend = this.data.recommendGoods[0];
      if (firstRecommend.listPicUrl) {
        shareImageUrl = this.formatShareImageUrl(firstRecommend.listPicUrl);
      }
    }

    return {
      title: shareTitle,
      desc: shareDesc,
      path: '/pages/cart/cart',
      imageUrl: shareImageUrl
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function() {
    let shareImageUrl = '';
    let shareTitle = '伍俊惠选 - 精选购物车';

    // 根据购物车状态生成分享内容
    if (this.data.cartGoods && this.data.cartGoods.length > 0) {
      // 购物车有商品
      const cartCount = this.data.cartGoods.length;
      const totalAmount = this.data.cartTotal.goodsAmount;

      shareTitle = `我的购物车有${cartCount}件好物`;
      if (totalAmount && parseFloat(totalAmount) > 0) {
        shareTitle += `，总价值¥${totalAmount}`;
      }
      shareTitle += ' - 伍俊惠选';

      // 使用第一个商品的图片作为分享图片
      const firstGoods = this.data.cartGoods[0];
      if (firstGoods.listPicUrl) {
        shareImageUrl = this.formatShareImageUrl(firstGoods.listPicUrl);
      }
    } else {
      // 购物车为空，推广推荐商品
      shareTitle = '伍俊惠选 - 精选优质商品，品质生活选择';

      // 使用推荐商品的图片
      if (this.data.recommendGoods && this.data.recommendGoods.length > 0) {
        const firstRecommend = this.data.recommendGoods[0];
        if (firstRecommend.listPicUrl) {
          shareImageUrl = this.formatShareImageUrl(firstRecommend.listPicUrl);
        }
      }
    }

    console.log('分享到朋友圈 - 标题:', shareTitle);
    console.log('分享到朋友圈 - 图片URL:', shareImageUrl);

    return {
      title: shareTitle,
      query: '', // 购物车页面不需要特殊查询参数
      imageUrl: shareImageUrl
    };
  },
  getCartList: function () {
    let that = this;
    that.setData({
      cartGoods: [],
      cartTotal: {
        "goodsCount": 0,
        "goodsAmount": 0.00,
        "checkedGoodsCount": 0,
        "checkedGoodsAmount": 0.00
      }
    });

    // 增强登录状态检查
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    if (!userInfo || !token) {
      console.log('用户未登录，跳过购物车加载');
      return;
    }

    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    util.request(api.CartList).then(function (res) {
      wx.hideLoading();

      // 检查是否是登录错误
      if (res.code === 616) {
        console.log('Token已过期，需要重新登录');
        // 清除过期的登录信息
        wx.removeStorageSync('userInfo');
        wx.removeStorageSync('token');

        // 更新全局数据
        const app = getApp();
        app.globalData.userInfo = {
          nickname: '点击登录',
          avatar: 'http://yanxuan.nosdn.127.net/8945ae63d940cc42406c3f67019c5cb6.png'
        };
        app.globalData.token = '';

        return;
      }

      if (res.success) {
        that.setData({
          cartGoods: res.data.cartList || [],
          cartTotal: res.data.cartTotal || {
            "goodsCount": 0,
            "goodsAmount": 0.00,
            "checkedGoodsCount": 0,
            "checkedGoodsAmount": 0.00
          }
        });
      } else {
        wx.showToast({
          title: res.errmsg || '获取购物车失败',
          icon: 'none',
          duration: 2000
        });
      }

      that.setData({
        checkedAllStatus: that.isCheckedAll()
      });
    }).catch(function (error) {
      wx.hideLoading();
      console.error('Failed to get cart list:', error);

      // 如果是网络错误，不显示错误提示，避免频繁弹窗
      if (error && error.errMsg && error.errMsg.includes('request:fail')) {
        console.log('网络请求失败，静默处理');
      } else {
        wx.showToast({
          title: '网络异常，请下拉刷新',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  isCheckedAll: function () {
    //判断购物车商品已全选
    return this.data.cartGoods.every(function (element, index, array) {
      if (element.checked == true) {
        return true;
      } else {
        return false;
      }
    });
  },
  checkedItem: function (event) {
    // 使用 currentTarget 而不是 target，确保获取到正确的元素
    let itemIndex = event.currentTarget.dataset.itemIndex;
    let that = this;

    // 添加安全检查
    if (itemIndex === undefined || itemIndex === null) {
      console.error('itemIndex is undefined');
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      return;
    }

    // 转换为数字类型
    itemIndex = parseInt(itemIndex);

    // 检查索引是否有效
    if (isNaN(itemIndex) || itemIndex < 0 || itemIndex >= this.data.cartGoods.length) {
      console.error('Invalid itemIndex:', itemIndex);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      return;
    }

    let cartItem = this.data.cartGoods[itemIndex];

    // 检查商品项是否存在
    if (!cartItem) {
      console.error('cartItem is undefined for index:', itemIndex);
      wx.showToast({
        title: '商品信息错误，请刷新页面',
        icon: 'none'
      });
      return;
    }

    if (!this.data.isEditCart) {
      util.request(api.CartChecked, {
        productIds: cartItem.productId,
        checked: cartItem.checked ? 0 : 1
      }, 'POST').then(function (res) {
        if (res.success) {
          console.log(res.data);
          that.setData({
            cartGoods: res.data.cartList,
            cartTotal: res.data.cartTotal
          });
        }

        that.setData({
          checkedAllStatus: that.isCheckedAll()
        });
      }).catch(function (error) {
        console.error('选择商品失败:', error);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      });
    } else {
      //编辑状态
      let tmpCartData = this.data.cartGoods.map(function (element, index, array) {
        if (index == itemIndex) {
          element.checked = !element.checked;
        }

        return element;
      });

      that.setData({
        cartGoods: tmpCartData,
        checkedAllStatus: that.isCheckedAll(),
        'cartTotal.checkedGoodsCount': that.getCheckedGoodsCount()
      });
    }
  },
  getCheckedGoodsCount: function () {
    let checkedGoodsCount = 0;
    this.data.cartGoods.forEach(function (v) {
      if (v.checked === true) {
        checkedGoodsCount += v.number;
      }
    });
    console.log(checkedGoodsCount);
    return checkedGoodsCount;
  },

  // 本地计算购物车总计（用于即时更新UI）
  calculateCartTotal: function () {
    let goodsCount = 0;
    let goodsAmount = 0;
    let checkedGoodsCount = 0;
    let checkedGoodsAmount = 0;

    this.data.cartGoods.forEach(function (item) {
      if (item && item.number && item.retailPrice) {
        goodsCount += item.number;
        goodsAmount += item.number * parseFloat(item.retailPrice);

        if (item.checked === true) {
          checkedGoodsCount += item.number;
          checkedGoodsAmount += item.number * parseFloat(item.retailPrice);
        }
      }
    });

    // 更新本地购物车总计
    this.setData({
      'cartTotal.goodsCount': goodsCount,
      'cartTotal.goodsAmount': goodsAmount.toFixed(2),
      'cartTotal.checkedGoodsCount': checkedGoodsCount,
      'cartTotal.checkedGoodsAmount': checkedGoodsAmount.toFixed(2)
    });

    return {
      goodsCount: goodsCount,
      goodsAmount: goodsAmount.toFixed(2),
      checkedGoodsCount: checkedGoodsCount,
      checkedGoodsAmount: checkedGoodsAmount.toFixed(2)
    };
  },
  checkedAll: function () {
    let that = this;

    if (!this.data.isEditCart) {
      var productIds = this.data.cartGoods.map(function (v) {
        return v.productId;
      });
      util.request(api.CartChecked, {
        productIds: productIds.join(','),
        checked: that.isCheckedAll() ? 0 : 1
      }, 'POST').then(function (res) {
        if (res.success) {
          console.log(res.data);
          that.setData({
            cartGoods: res.data.cartList,
            cartTotal: res.data.cartTotal
          });
        }

        that.setData({
          checkedAllStatus: that.isCheckedAll()
        });
      });
    } else {
      //编辑状态
      let checkedAllStatus = that.isCheckedAll();
      let tmpCartData = this.data.cartGoods.map(function (v) {
        v.checked = !checkedAllStatus;
        return v;
      });

      that.setData({
        cartGoods: tmpCartData,
        checkedAllStatus: that.isCheckedAll(),
        'cartTotal.checkedGoodsCount': that.getCheckedGoodsCount()
      });
    }

  },
  editCart: function () {
    var that = this;
    if (this.data.isEditCart) {
      this.getCartList();
      this.setData({
        isEditCart: !this.data.isEditCart
      });
    } else {
      //编辑状态
      let tmpCartList = this.data.cartGoods.map(function (v) {
        v.checked = false;
        return v;
      });
      this.setData({
        editCartList: this.data.cartGoods,
        cartGoods: tmpCartList,
        isEditCart: !this.data.isEditCart,
        checkedAllStatus: that.isCheckedAll(),
        'cartTotal.checkedGoodsCount': that.getCheckedGoodsCount()
      });
    }

  },
  updateCart: function (productId, goodsId, number, id) {
    let that = this;

    wx.showLoading({
      title: '更新中...',
      mask: true
    });

    util.request(api.CartUpdate, {
      productId: productId,
      goodsId: goodsId,
      number: number,
      id: id
    }, 'POST').then(function (res) {
      wx.hideLoading();
      if (res.success) {
        that.setData({
          cartGoods: res.data.cartList,
          cartTotal: res.data.cartTotal
        });

        // 更新选中状态
        that.setData({
          checkedAllStatus: that.isCheckedAll()
        });
      } else {
        wx.showToast({
          title: res.errmsg || '更新失败',
          icon: 'none',
          duration: 2000
        });
        // 更新失败，重新获取购物车列表
        that.getCartList();
      }
    }).catch(function (error) {
      wx.hideLoading();
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none',
        duration: 2000
      });
      // 发生错误，重新获取购物车列表
      that.getCartList();
    });
  },
  cutNumber: function (event) {
    // 使用 currentTarget 而不是 target，确保获取到正确的元素
    let itemIndex = event.currentTarget.dataset.itemIndex;

    // 添加安全检查
    if (itemIndex === undefined || itemIndex === null) {
      console.error('itemIndex is undefined');
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      return;
    }

    // 转换为数字类型
    itemIndex = parseInt(itemIndex);

    // 检查索引是否有效
    if (isNaN(itemIndex) || itemIndex < 0 || itemIndex >= this.data.cartGoods.length) {
      console.error('Invalid itemIndex:', itemIndex);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      return;
    }

    let cartItem = this.data.cartGoods[itemIndex];

    // 检查商品项是否存在
    if (!cartItem) {
      console.error('cartItem is undefined for index:', itemIndex);
      wx.showToast({
        title: '商品信息错误，请刷新页面',
        icon: 'none'
      });
      return;
    }

    // 如果数量已经是1，提示用户是否要删除
    if (cartItem.number <= 1) {
      wx.showModal({
        title: '提示',
        content: '确定要删除该商品吗？',
        success: function (res) {
          if (res.confirm) {
            // 用户点击确定，删除商品
            let productIds = [cartItem.productId];
            util.request(api.CartDelete, {
              productIds: productIds.join(',')
            }, 'POST').then(function (res) {
              if (res.success) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                // 重新获取购物车列表
                this.getCartList();
              } else {
                wx.showToast({
                  title: res.errmsg || '删除失败',
                  icon: 'none'
                });
              }
            }.bind(this));
          }
        }.bind(this)
      });
      return;
    }

    let number = cartItem.number - 1;
    cartItem.number = number;

    // 更新页面数据
    this.setData({
      cartGoods: this.data.cartGoods
    });

    // 立即更新本地购物车总计（提供即时反馈）
    this.calculateCartTotal();

    // 调用API更新购物车（API会返回服务器端的准确总计）
    this.updateCart(cartItem.productId, cartItem.goodsId, number, cartItem.id);
  },
  addNumber: function (event) {
    // 使用 currentTarget 而不是 target，确保获取到正确的元素
    let itemIndex = event.currentTarget.dataset.itemIndex;

    // 添加安全检查
    if (itemIndex === undefined || itemIndex === null) {
      console.error('itemIndex is undefined');
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      return;
    }

    // 转换为数字类型
    itemIndex = parseInt(itemIndex);

    // 检查索引是否有效
    if (isNaN(itemIndex) || itemIndex < 0 || itemIndex >= this.data.cartGoods.length) {
      console.error('Invalid itemIndex:', itemIndex);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      return;
    }

    let cartItem = this.data.cartGoods[itemIndex];

    // 检查商品项是否存在
    if (!cartItem) {
      console.error('cartItem is undefined for index:', itemIndex);
      wx.showToast({
        title: '商品信息错误，请刷新页面',
        icon: 'none'
      });
      return;
    }

    // 限制最大购买数量为99
    if (cartItem.number >= 99) {
      wx.showToast({
        title: '已达最大购买数量',
        icon: 'none'
      });
      return;
    }

    let number = cartItem.number + 1;

    // 检查库存
    if (cartItem.goodsNumber != null && cartItem.goodsNumber !== undefined && number > cartItem.goodsNumber) {
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      });
      return;
    }

    // 更新商品数量
    cartItem.number = number;

    // 更新页面数据
    this.setData({
      cartGoods: this.data.cartGoods
    });

    // 立即更新本地购物车总计（提供即时反馈）
    this.calculateCartTotal();

    // 调用API更新购物车（API会返回服务器端的准确总计）
    this.updateCart(cartItem.productId, cartItem.goodsId, number, cartItem.id);
  },
  checkoutOrder: function () {
    console.log('checkoutOrder function called');

    // 获取已选择的商品
    let that = this;
    let checkedGoods = this.data.cartGoods.filter(function (element) {
      return element.checked == true;
    });

    console.log('Checked goods:', checkedGoods.length);

    if (checkedGoods.length <= 0) {
      wx.showToast({
        title: '请选择商品',
        icon: 'none',
        duration: 2000
      });
      return false;
    }

    // 验证库存
    for (let i = 0; i < checkedGoods.length; i++) {
      let item = checkedGoods[i];
      if (item.goodsNumber != null && item.goodsNumber !== undefined && item.number > item.goodsNumber) {
        wx.showToast({
          title: item.goodsName + ' 库存不足',
          icon: 'none',
          duration: 2000
        });
        return false;
      }
    }

    console.log('Proceeding to checkout');

    // 使用购物车服务导航到结算页面
    cartService.navigateToCheckout({
      fail: function (err) {
        wx.showToast({
          title: '系统错误，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  deleteCart: function () {
    //获取已选择的商品
    let that = this;

    let productIds = this.data.cartGoods.filter(function (element, index, array) {
      if (element.checked == true) {
        return true;
      } else {
        return false;
      }
    });

    if (productIds.length <= 0) {
      return false;
    }

    productIds = productIds.map(function (element, index, array) {
      if (element.checked == true) {
        return element.productId;
      }
    });


    util.request(api.CartDelete, {
      productIds: productIds.join(',')
    }, 'POST').then(function (res) {
      if (res.success) {
        console.log(res.data);
        let cartList = res.data.cartList.map(v => {
          console.log(v);
          v.checked = false;
          return v;
        });

        that.setData({
          cartGoods: cartList,
          cartTotal: res.data.cartTotal
        });
      }

      that.setData({
        checkedAllStatus: that.isCheckedAll()
      });
    });
  },
  // 获取推荐商品列表
  getRecommendProducts: function () {
    let that = this;

    // 显示加载状态
    this.setData({
      loadingRecommend: true
    });

    // 从API获取推荐商品
    util.request(api.GoodsList, {
      page: 1,
      size: 10
    })
      .then(function (res) {
        that.setData({
          loadingRecommend: false
        });

        if (res.success && res.data.goodsList && res.data.goodsList.length > 0) {
          // 从热门商品中获取推荐，显示最多4个
          let recommendList = res.data.goodsList;

          // 处理商品数据，确保所有必要字段存在
          recommendList = recommendList.map(item => {
            return {
              ...item,
              // 确保图片地址正确，如果没有图片则使用默认图片
              listPicUrl: item.listPicUrl || '../../static/images/default.png'
            };
          });

          that.setData({
            recommendGoods: recommendList
          });
          console.log("recommendGoods",that.data.recommendGoods)
        } else {
          // 如果没有获取到商品，显示空数组
          that.setData({
            recommendGoods: []
          });

          if (!res.success) {
            console.log('Failed to get recommend products:', res.errmsg);
          }
        }
      })
      .catch(function (error) {
        that.setData({
          loadingRecommend: false,
          recommendGoods: []
        });
        console.error('Failed to fetch recommended products:', error);
      });
  },
  // 添加推荐商品到购物车
  addToCart: function (e) {
    const goodsId = e.currentTarget.dataset.goodsId;
    let that = this;

    // 检查用户登录状态
    if (!wx.getStorageSync('userInfo')) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
      return;
    }

    // 获取点击位置信息用于动画
    if (that.data.recommendGoods && that.data.recommendGoods.length > 0) {
      cartService.getFlyAnimation(e, goodsId, that.data.recommendGoods, that);
    }

    cartService.addToCart({
      goodsId: goodsId,
      number: 1,
      directAdd: false, // 让cartService检查是否需要选择规格
      success: function (res) {
        console.log('添加到购物车成功:', res);
        // 更新购物车列表和推荐商品状态
        that.getCartList();
      },
      fail: function (error) {
        console.error('添加到购物车失败:', error);
        // 如果添加失败，可以显示更详细的错误信息
        if (error && error.message) {
          wx.showToast({
            title: error.message,
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  // 获取飞入购物车动画所需信息
  getFlyAnimation: function (e, goodsId) {
    let that = this;
    const product = this.data.recommendGoods.find(item => item.id === goodsId);
    if (!product) return;

    // 获取商品图片
    const flyImg = product.listPicUrl || '../../static/images/tip/1.jpg';

    // 获取点击位置
    let touch = e.touches[0] || e.changedTouches[0];
    let flyTop = touch.clientY - 25; // 减去一半的飞行元素高度
    let flyLeft = touch.clientX - 25; // 减去一半的飞行元素宽度

    // 设置动画参数
    that.setData({
      openFly: true,
      flyImg: flyImg,
      flyTop: flyTop,
      flyLeft: flyLeft
    });

    // 动画结束后关闭
    setTimeout(function () {
      that.setData({
        openFly: false
      });
    }, 800); // 800ms与动画时长匹配
  },

  // 修改商品数量
  updateNumber: function (event) {
    let that = this;
    let productId = event.target.dataset.productId;
    let itemIndex = event.target.dataset.itemIndex;
    let number = this.data.cartGoods[itemIndex].number;

    util.request(api.CartUpdate, {
      productId: productId,
      number: number
    }, 'POST').then(function (res) {
      if (res.success) {
        that.setData({
          cartGoods: res.data.cartList,
          cartTotal: res.data.cartTotal
        });
      }
    });
  },
  // 点击商品跳转到商品详情
  navigateToGoodsDetail: function (event) {
    // 获取商品ID
    const goodsId = event.currentTarget.dataset.goodsId;

    // 检查是否在编辑模式下，编辑模式下不跳转
    if (this.data.isEditCart) {
      return;
    }

    // 跳转到商品详情页
    wx.navigateTo({
      url: '/pages/goods/goods?id=' + goodsId
    });
  },

  // 跳转到首页
  goToIndex: function () {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 跳转到分类页面
  goToCategory: function () {
    wx.switchTab({
      url: '/pages/allGoods/allGoods'
    });
  }
})
