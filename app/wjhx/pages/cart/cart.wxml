<wxs src="../../utils/format.wxs" module="format" />
<view class="container  scrollable-page">
  <view class="service-policy">
  </view>

  <!-- Empty cart state -->
  <view class="no-cart" wx:if="{{cartGoods.length <= 0}}">
    <view class="empty-cart">
      <!-- 空购物车图标 -->
      <view class="empty-cart-icon">
        <view class="cart-icon">🛒</view>
        <view class="empty-indicator">
          <view class="dot dot-1"></view>
          <view class="dot dot-2"></view>
          <view class="dot dot-3"></view>
        </view>
      </view>

      <!-- 文字内容 -->
      <view class="empty-cart-content">
        <text class="empty-cart-title">购物车空空如也</text>
        <text class="empty-cart-subtitle">快去挑选心仪的商品吧</text>
      </view>

      <!-- 操作按钮 -->
      <view class="empty-cart-actions">
        <view class="empty-cart-btn primary" bindtap="goToIndex">
          <view class="btn-icon">🛍️</view>
          <text class="btn-text">去逛逛</text>
        </view>
        <view class="empty-cart-btn secondary" bindtap="goToCategory">
          <view class="btn-icon">📂</view>
          <text class="btn-text">商品浏览</text>
        </view>
      </view>

      <!-- 装饰元素 -->
      <view class="decoration-elements">
        <view class="floating-element element-1">✨</view>
        <view class="floating-element element-2">💫</view>
        <view class="floating-element element-3">🌟</view>
        <view class="floating-element element-4">⭐</view>
      </view>
    </view>
  </view>

  <!-- Cart with items -->
  <view class="cart-view" wx:if="{{cartGoods.length > 0}}">
    <view class="cart-list">
      <view class="cart-item {{isEditCart ? 'edit-mode' : ''}}" wx:for="{{cartGoods}}" wx:key="id">
        <view class="checkbox {{item.checked ? 'checked' : ''}}" bindtap="checkedItem" data-item-index="{{index}}">
          <icon wx:if="{{item.checked}}" type="success" color="#42A5F5" size="18" />
        </view>

        <view class="cart-goods" bindtap="navigateToGoodsDetail" data-goods-id="{{item.goodsId}}">
          <image class="goods-image" src="{{format.formatImageUrl(item.listPicUrl)}}"></image>

          <view class="goods-info">
            <view class="goods-name">{{item.goodsName}}</view>
            <view class="goods-spec">{{item.goodsSpecificationNameValue}}</view>
            <view class="goods-price-quantity">
              <text class="goods-price">¥{{item.retailPrice}}</text>

              <view class="quantity-control" catchtap>
                <view class="quantity-btn minus {{item.number <= 1 ? 'disabled' : ''}}" catchtap="cutNumber" data-item-index="{{index}}">-</view>
                <input value="{{item.number}}" class="quantity-input" disabled="true" type="number" />
                <view class="quantity-btn plus" catchtap="addNumber" data-item-index="{{index}}">+</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- Cart footer with actions -->
    <view class="cart-footer">
      <view class="select-all" bindtap="checkedAll">
        <view class="checkbox {{checkedAllStatus ? 'checked' : ''}}">
          <icon wx:if="{{checkedAllStatus}}" type="success" color="#42A5F5" size="18" />
        </view>
        <text>全选</text>
        <text class="selected-count" wx:if="{{cartTotal.checkedGoodsCount > 0}}">({{cartTotal.checkedGoodsCount}})</text>
      </view>

      <view class="action-section">
        <view class="total-price" wx:if="{{!isEditCart}}">
          <text class="label">合计:</text>
          <text class="price">¥{{cartTotal.checkedGoodsAmount}}</text>
        </view>

        <view class="cart-actions">
          <view class="action-btn edit-btn" catchtap="editCart">
            {{!isEditCart ? '编辑' : '完成'}}
          </view>

          <view class="action-btn delete-btn" catchtap="deleteCart" wx:if="{{isEditCart}}">
            删除所选
          </view>

          <view id="checkout-btn" class="action-btn checkout-btn" catchtap="checkoutOrder" wx:if="{{!isEditCart}}">
            去结算
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 您可能喜欢的商品 -->
  <view class="recommend-section">
    <view class="section-header">
      <view class="section-title">您可能还喜欢</view>
      <view class="divider-line"></view>
    </view>

    <view class="recommend-goods">
      <view class="recommend-item" wx:for="{{recommendGoods}}" wx:key="id">
        <navigator url="../goods/goods?id={{item.id}}" class="recommend-link">
          <view class="official-tag">产地直发</view>
          <image class="recommend-img" src="{{format.formatImageUrl(item.listPicUrl) || '../../static/images/tip/1.jpg'}}" mode="aspectFill"></image>
          <view class="recommend-info">
            <view class="recommend-name">{{item.name}}</view>
            <view class="recommend-brief">{{item.brief || '精选商品 品质保障'}}</view>
            <view class="recommend-price-info">
              <view class="recommend-price">
                <text class="symbol">¥</text>
                <text class="price-num">{{item.unitPrice || item.retailPrice}}</text>
              </view>
              <view class="cart-add" catchtap="addToCart" data-goods-id="{{item.id}}">
                <image src="/static/images/ic_menu_shoping_nor.png"></image>
              </view>
            </view>
          </view>
        </navigator>
      </view>
    </view>
  </view>

  <!-- 飞入购物车动画元素 -->
  <view class="fly-item" hidden="{{!openFly}}" style="top:{{flyTop}}px;left:{{flyLeft}}px;width:{{flyWidth}}px;height:{{flyHeight}}px;animation-duration:{{flyTime}}s">
    <image class="fly-img" src="{{flyImg}}"></image>
  </view>
</view>
