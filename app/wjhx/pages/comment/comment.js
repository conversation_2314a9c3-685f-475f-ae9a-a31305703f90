var app = getApp();
var util = require('../../utils/util.js');
var api = require('../../config/api.js');

Page({
    data: {
        // 基础数据
        comments: [],
        allCommentList: [],
        picCommentList: [],
        goodCommentList: [],
        normalCommentList: [],
        badCommentList: [],

        // 页面参数
        typeId: 0,
        valueId: 0,
        showType: 0, // 0:全部 1:有图 2:好评 3:中评 4:差评

        // 统计数据
        allCount: 0,
        hasPicCount: 0,
        goodCount: 0,
        normalCount: 0,
        badCount: 0,
        averageRating: 5.0,
        satisfactionRate: 100,

        // 分页数据
        currentPage: 1,
        pageSize: 20,
        hasMore: true,

        // 状态控制
        loading: false,
        loadingMore: false,
        refreshing: false
    },
    // 获取评论统计数据
    getCommentCount: function () {
        const that = this;

        util.request(api.CommentCount, {
            valueId: that.data.valueId,
            typeId: that.data.typeId
        }).then(function (res) {
            if (res.success) {
                that.setData({
                    allCount: res.data.allCount || 0,
                    hasPicCount: res.data.hasPicCount || 0,
                    goodCount: res.data.goodCount || 0,
                    normalCount: res.data.normalCount || 0,
                    badCount: res.data.badCount || 0,
                    averageRating: res.data.averageRating || 5.0,
                    satisfactionRate: res.data.satisfactionRate || 100
                });
            }
        }).catch(function (error) {
            console.error('获取评论统计失败:', error);
            util.showErrorToast('获取评论统计失败');
        });
    },
    // 获取评论列表
    getCommentList: function (isRefresh = false) {
        const that = this;

        // 设置加载状态
        if (isRefresh) {
            that.setData({ refreshing: true });
        } else if (that.data.currentPage === 1) {
            that.setData({ loading: true });
        } else {
            that.setData({ loadingMore: true });
        }

        const requestData = {
            valueId: that.data.valueId,
            typeId: that.data.typeId,
            pageSize: that.data.pageSize,
            pageNum: that.data.currentPage,
            showType: that.data.showType
        };

        util.request(api.CommentList, requestData).then(function (res) {
            if (res.success) {
                const newComments = res.data || [];
                let updatedComments = [];

                if (isRefresh || that.data.currentPage === 1) {
                    // 刷新或首次加载
                    updatedComments = newComments;
                } else {
                    // 加载更多
                    updatedComments = that.data.comments.concat(newComments);
                }

                // 处理评论数据
                const processedComments = that.processCommentData(updatedComments);

                that.setData({
                    comments: processedComments,
                    hasMore: newComments.length >= that.data.pageSize,
                    loading: false,
                    loadingMore: false,
                    refreshing: false
                });

                // 缓存不同类型的评论列表
                that.cacheCommentsByType(processedComments);
            }
        }).catch(function (error) {
            console.error('获取评论列表失败:', error);
            that.setData({
                loading: false,
                loadingMore: false,
                refreshing: false
            });
            util.showErrorToast('获取评论失败');
        });
    },

    // 处理评论数据
    processCommentData: function (comments) {
        return comments.map(comment => {
            // 格式化时间
            if (comment.createTime) {
                comment.createTime = util.formatTime(new Date(comment.createTime));
            }

            // 确保用户信息存在
            if (!comment.userInfo) {
                comment.userInfo = {
                    nickname: '匿名用户',
                    avatar: 'https://via.placeholder.com/80x80/f0f0f0/999999?text=头像'
                };
            } else {
                // 处理头像为空的情况
                if (!comment.userInfo.avatar || comment.userInfo.avatar === '') {
                    comment.userInfo.avatar = 'https://via.placeholder.com/80x80/f0f0f0/999999?text=头像';
                }
                // 处理昵称为空的情况
                if (!comment.userInfo.nickname || comment.userInfo.nickname === '') {
                    comment.userInfo.nickname = '匿名用户';
                }
            }

            // 确保图片列表存在
            if (!comment.picList) {
                comment.picList = [];
            }

            // 设置默认评分
            if (!comment.rating) {
                comment.rating = 5;
            }

            return comment;
        });
    },

    // 缓存不同类型的评论
    cacheCommentsByType: function (comments) {
        const that = this;
        const showType = that.data.showType;

        switch (showType) {
            case 0: // 全部
                that.setData({ allCommentList: comments });
                break;
            case 1: // 有图
                that.setData({ picCommentList: comments });
                break;
            case 2: // 好评
                that.setData({ goodCommentList: comments });
                break;
            case 3: // 中评
                that.setData({ normalCommentList: comments });
                break;
            case 4: // 差评
                that.setData({ badCommentList: comments });
                break;
        }
    },
    onLoad: function (options) {
        // 页面初始化
        this.setData({
            typeId: parseInt(options.typeId) || 0,
            valueId: parseInt(options.valueId) || 0
        });

        // 初始化数据
        this.initData();
    },

    // 初始化数据
    initData: function () {
        this.getCommentCount();
        this.getCommentList();
    },
    onReady: function () {
        // 页面渲染完成
    },

    onShow: function () {
        // 页面显示
    },

    onHide: function () {
        // 页面隐藏
    },

    onUnload: function () {
        // 页面关闭
    },

    // 下拉刷新
    onPullDownRefresh: function () {
        this.refreshComments();
    },
    // 切换标签
    switchTab: function (e) {
        const type = parseInt(e.currentTarget.dataset.type);

        if (type === this.data.showType) {
            return; // 相同标签不处理
        }

        this.setData({
            showType: type,
            currentPage: 1,
            hasMore: true
        });

        // 检查是否有缓存数据
        const cachedComments = this.getCachedComments(type);
        if (cachedComments && cachedComments.length > 0) {
            this.setData({
                comments: cachedComments
            });
        } else {
            this.getCommentList();
        }
    },

    // 获取缓存的评论
    getCachedComments: function (type) {
        switch (type) {
            case 0: return this.data.allCommentList;
            case 1: return this.data.picCommentList;
            case 2: return this.data.goodCommentList;
            case 3: return this.data.normalCommentList;
            case 4: return this.data.badCommentList;
            default: return [];
        }
    },

    // 触底加载更多
    onReachBottom: function () {
        if (!this.data.hasMore || this.data.loadingMore) {
            return;
        }
        this.loadMore();
    },

    // 加载更多
    loadMore: function () {
        this.setData({
            currentPage: this.data.currentPage + 1
        });
        this.getCommentList();
    },

    // 刷新评论
    refreshComments: function () {
        this.setData({
            currentPage: 1,
            hasMore: true
        });
        this.getCommentList(true);

        // 停止下拉刷新
        setTimeout(() => {
            wx.stopPullDownRefresh();
        }, 1000);
    },

    // 返回上一页
    goBack: function () {
        wx.navigateBack({
            delta: 1
        });
    },

    // 预览图片
    previewImage: function (e) {
        const current = e.currentTarget.dataset.current;
        const urls = e.currentTarget.dataset.urls;

        wx.previewImage({
            current: current,
            urls: urls
        });
    },

    // 点赞/取消点赞
    toggleLike: function (e) {
        const commentId = e.currentTarget.dataset.id;
        const that = this;

        // 找到对应的评论
        const comments = that.data.comments;
        const commentIndex = comments.findIndex(item => item.id === commentId);

        if (commentIndex === -1) return;

        const comment = comments[commentIndex];
        const isLiked = comment.isLiked;
        const newLikeCount = isLiked ? (comment.likeCount || 1) - 1 : (comment.likeCount || 0) + 1;

        // 乐观更新UI
        comment.isLiked = !isLiked;
        comment.likeCount = newLikeCount;

        that.setData({
            [`comments[${commentIndex}]`]: comment
        });

        // 调用API
        util.request(api.CommentLike || (api.ApiRootUrl + 'comment/like'), {
            commentId: commentId,
            action: isLiked ? 'unlike' : 'like'
        }, 'POST').then(function (res) {
            if (!res.success) {
                // 如果失败，回滚UI状态
                comment.isLiked = isLiked;
                comment.likeCount = isLiked ? newLikeCount + 1 : newLikeCount - 1;
                that.setData({
                    [`comments[${commentIndex}]`]: comment
                });
                util.showErrorToast('操作失败');
            }
        }).catch(function (error) {
            // 网络错误，回滚UI状态
            comment.isLiked = isLiked;
            comment.likeCount = isLiked ? newLikeCount + 1 : newLikeCount - 1;
            that.setData({
                [`comments[${commentIndex}]`]: comment
            });
            util.showErrorToast('网络错误');
        });
    },

    // 显示回复输入框
    showReplyInput: function (e) {
        const commentId = e.currentTarget.dataset.id;

        wx.showModal({
            title: '回复评论',
            content: '此功能正在开发中，敬请期待！',
            showCancel: false,
            confirmText: '知道了'
        });
    }
})