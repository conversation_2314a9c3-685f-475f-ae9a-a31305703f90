<!--index.wxml-->
<wxs src="../../utils/format.wxs" module="format" />
<view class="container">
  <!-- Loading Error -->
  <view class="error-view" wx:if="{{errorMessage}}">
    <image class="error-icon" src="../../static/images/icon_error.png"></image>
    <text class="error-text">{{errorMessage}}</text>
    <view class="error-btn" bindtap="getIndexData">重新加载</view>
  </view>

  <view wx:else>
    <!-- Banner Section -->
    <swiper class="banner" indicator-dots="true" indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#fff" autoplay="true" interval="3000" duration="1000" circular="true">
      <swiper-item wx:for="{{bannerList}}" wx:key="id">
        <navigator url="{{item.link || '../index/index'}}">
          <view class="banner-img-container">
            <image src="{{format.formatImageUrl(item.imageUrl)}}" mode="aspectFit" lazy-load="true" binderror="bannerImageError" data-index="{{index}}"></image>
          </view>
        </navigator>
      </swiper-item>
    </swiper>

    <!-- Category Menu -->
    <view class="m-menu" >
      <navigator class="item" url="{{item.url || '../index/index'}}" wx:for="{{channelList}}" wx:key="id">
        <view class="icon-container">
          <image src="{{format.formatImageUrl(item.iconUrl) }}" mode="aspectFit"></image>
        </view>
        <text>{{item.name || '分类'}}</text>
      </navigator>
    </view>

    <!-- Brand Section -->
    <view class="a-section a-brand" wx:if="{{brandList.length > 0}}" style="display: none;">
      <view class="h">
        <navigator url="../brand/brand">
          <text class="txt">品牌制造商直供</text>
        </navigator>
      </view>
      <view class="b">
        <view class="item" wx:for="{{brandList}}" wx:key="id" wx:if="{{index < 4}}">
          <navigator url="/pages/brandDetail/brandDetail?id={{item.id}}">
            <view class="wrap">
              <image class="img" src="{{item.newPicUrl }}" mode="aspectFill"></image>
              <view class="brand-info">
                <text class="brand">{{item.name}}</text>
                <text class="price">{{item.floorPrice}}元起</text>
              </view>
            </view>
          </navigator>
        </view>
      </view>
    </view>

    <!-- New Goods Section -->
    <view class="a-section a-new" wx:if="{{newGoodsList.length > 0}}" style="display: none;">
      <view class="h">
        <view>
          <navigator url="../newGoods/newGoods">
            <text class="txt">新品首发</text>
          </navigator>
        </view>
      </view>
      <view class="b">
        <view class="item" wx:for="{{newGoodsList}}" wx:key="id" wx:if="{{index < 6}}">
          <navigator url="../goods/goods?id={{item.id}}">
            <image class="img" src="{{item.listPicUrl || '../../static/images/banner/2.jpg'}}" mode="aspectFill"></image>
            <text class="name">{{item.name}}</text>
            <text class="price">￥{{item.retailPrice}}</text>
          </navigator>
        </view>
      </view>
    </view>

    <!-- Hot Goods Section -->
    <view class="a-section a-popular" wx:if="{{hotGoods.length > 0}}" style="display: none;">
      <view class="h">
        <view>
          <navigator url="../hotGoods/hotGoods">
            <text class="txt">人气推荐</text>
          </navigator>
        </view>
      </view>
      <view class="b">
        <view class="item" wx:for="{{hotGoods}}" wx:key="id" wx:if="{{index < 3}}">
          <navigator url="/pages/goods/goods?id={{item.id}}">
            <image class="img" src="{{item.listPicUrl || '../../static/images/banner/3.jpg'}}" mode="aspectFill"></image>
            <view class="right">
              <view class="text">
                <text class="name">{{item.name}}</text>
                <text class="desc">{{item.goodsBrief}}</text>
                <text class="price">￥{{item.retailPrice}}</text>
              </view>
            </view>
          </navigator>
        </view>
      </view>
    </view>

    <!-- Topic Section -->
    <view class="a-section a-topic" wx:if="{{topicList.length > 0}}" style="display: none;">
      <view class="h">
        <view>
          <navigator url="../topic/topic" open-type="switchTab">
            <text class="txt">专题精选</text>
          </navigator>
        </view>
      </view>
      <view class="b">
        <scroll-view scroll-x="true" class="list" enable-flex="true" enhanced="true" show-scrollbar="false">
          <view class="item" wx:for="{{topicList}}" wx:key="id">
            <navigator url="../topicDetail/topicDetail?id={{item.id}}">
              <image class="img" src="{{item.scenePicUrl || '../../static/images/banner/3.jpg'}}" mode="aspectFill"></image>
              <view class="np">
                <text class="name">{{item.title}}</text>
                <text class="price">￥{{item.priceInfo}}元起</text>
              </view>
              <text class="desc">{{item.subtitle}}</text>
            </navigator>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- Floor Goods -->
    <view class="a-section good-grid" wx:for="{{floorGoods}}" wx:key="id" wx:if="{{item.goodsList.length > 0}}" style="display: none;">
      <view class="h">
        <view>
          <text>{{item.name}}</text>
        </view>
      </view>
      <view class="b">
        <block wx:for="{{item.goodsList}}" wx:for-index="iindex" wx:for-item="iitem" wx:key="id" wx:if="{{iindex < 4}}">
          <view class="item">
            <navigator url="../goods/goods?id={{iitem.id}}" class="goods-item">
              <image class="img" src="{{format.formatImageUrl(iitem.listPicUrl) }}" mode="aspectFill"></image>
              <view class="info">
                <text class="name">{{iitem.name}}</text>
                <text class="price">￥{{iitem.retailPrice}}</text>
              </view>
            </navigator>
          </view>
        </block>
        <view class="item more-item">
          <navigator url="/pages/category/category?id={{item.id}}" class="more-a">
            <view class="txt">更多{{item.name}}好物</view>
            <image class="icon" src="../../static/images/icon_go_more.png"></image>
          </navigator>
        </view>
      </view>
    </view>

    <!-- All Goods Section -->
    <view class="a-section all-goods">
      <view class="h">
        <view>
          <text class="txt">所有商品</text>
        </view>
        <view>
          <navigator url="../category/category" class="more">
            <text>更多</text>
            <text class='arrowhead'></text>
          </navigator>
        </view>
      </view>
      <view class="goods-container">
        <view class="goods-row">
          <view class="goods-item" wx:for="{{allGoodsList}}" wx:key="id">
            <navigator url="../goods/goods?id={{item.id}}" class="goods-link">
              <!-- Product Image -->
              <view class="img-container">
                <image class="goods-img" src="{{format.formatImageUrl(item.listPicUrl)}}" mode="aspectFit" lazy-load="true" binderror="imageError" data-index="{{index}}"></image>
                <view wx:if="{{item.isHot}}" class="promo-tag hot-tag">热卖</view>
                <view wx:if="{{item.isNew}}" class="promo-tag new-tag">新品</view>
              </view>

              <!-- Product Info -->
              <view class="goods-info">
                <!-- Title -->
                <view class="goods-title">{{item.name}}</view>

                <!-- Brief Description -->
                <view class="goods-brief">{{item.goodsBrief || '精选商品 品质保障'}}</view>

                <!-- Price and Cart -->
                <view class="price-cart">
                  <view class="goods-price-info">
                    <text class="symbol">￥</text>
                    <text class="price-num">{{item.retailPrice}}</text>
                    <text class="original-price" wx:if="{{item.counterPrice && item.counterPrice > item.retailPrice}}">￥{{item.counterPrice}}</text>
                  </view>

                  <view class="sales-info">已售{{item.sellVolume || Math.floor(Math.random() * 1000 + 10)}}件</view>

                  <view class="cart-icon" catchtap="addToCart" data-goods-id="{{item.id}}">
                    <image src="../../static/images/ic_menu_shoping_nor.png"></image>
                  </view>
                </view>

                <!-- Tags -->
                <view class="goods-tags">
                  <text class="tag official-tag">产地直发</text>
                  <text class="tag" wx:if="{{item.isHot}}">热销</text>
                  <text class="tag" wx:if="{{item.isNew}}">新品</text>
                  <text class="tag">包邮</text>
                </view>
              </view>
            </navigator>
          </view>
        </view>
      </view>
      <view class="load-more" wx:if="{{allGoodsList.length > 0 && !allGoodsNoMore}}">加载中...</view>
      <view class="no-more" wx:if="{{allGoodsList.length > 0 && allGoodsNoMore}}">—— 已经到底了 ——</view>
      <view class="empty-tip" wx:if="{{allGoodsList.length === 0}}">暂无商品</view>
    </view>
  </view>
</view>
