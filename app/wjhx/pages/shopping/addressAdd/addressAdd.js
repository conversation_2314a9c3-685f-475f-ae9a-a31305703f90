var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');
var app = getApp();
var AddressParse = require('../../../lib/wxParse/zh-address-parse.min.js');

Page({
    data: {
        address: {
            id: null,
            provinceId: 0,
            cityId: 0,
            districtId: 0,
            address: '',
            fullRegion: '',
            name: '',
            mobile: '',
            isDefault: 0
        },
        addressId: 0,
        openSelectRegion: false,
        selectRegionList: [
            {id: 0, name: '省份', parentId: 1, type: 1},
            {id: 0, name: '城市', parentId: 1, type: 2},
            {id: 0, name: '区县', parentId: 1, type: 3}
        ],
        regionType: 1,
        regionList: [],
        selectRegionDone: false,
        pageReady: false,
        isFormComplete: false,
        // 订单参数
        orderParams: null,
        fromCheckout: false,
        // 来源标记，区分是从哪个页面进入的
        source: '' // 'checkout' 表示结算页, 'address' 表示我的地址页面
    },
    bindinputMobile(event) {
        // Ensure address exists
        if (!this.data.address) {
            this.setData({
                address: {
                    id: null,
                    provinceId: 0,
                    cityId: 0,
                    districtId: 0,
                    address: '',
                    fullRegion: '',
                    name: '',
                    mobile: event.detail.value,
                    isDefault: 0
                }
            });
        } else {
        let address = this.data.address;
        address.mobile = event.detail.value;
        this.setData({
            address: address
        });
        }
        
        // Check if form is complete after update
        this.checkFormComplete();
    },
    bindinputName(event) {
        // Ensure address exists
        if (!this.data.address) {
            this.setData({
                address: {
                    id: null,
                    provinceId: 0,
                    cityId: 0,
                    districtId: 0,
                    address: '',
                    fullRegion: '',
                    name: event.detail.value,
                    mobile: '',
                    isDefault: 0
                }
            });
        } else {
        let address = this.data.address;
        address.name = event.detail.value;
        this.setData({
            address: address
        });
        }
        
        // Check if form is complete after update
        this.checkFormComplete();
    },
    bindinputAddress(event) {
        // Ensure address exists
        if (!this.data.address) {
            this.setData({
                address: {
                    id: null,
                    provinceId: 0,
                    cityId: 0,
                    districtId: 0,
                    address: event.detail.value,
                    fullRegion: '',
                    name: '',
                    mobile: '',
                    isDefault: 0
                }
            });
        } else {
        let address = this.data.address;
        address.address = event.detail.value;
        this.setData({
            address: address
        });
        }
        
        // Check if form is complete after update
        this.checkFormComplete();
    },
    bindIsDefault(event) {
        // Prevent immediate UI flickering by adding a small delay
        wx.showLoading({
            title: '正在设置...',
            mask: true
        });

        // Get the new value from the switch
        let isDefault = event.detail.value ? 1 : 0;
        
        console.log('Setting default address to:', isDefault);
        
        // Ensure address exists
        if (!this.data.address) {
            this.setData({
                address: {
                    id: null,
                    provinceId: 0,
                    cityId: 0,
                    districtId: 0,
                    address: '',
                    fullRegion: '',
                    name: '',
                    mobile: '',
                    isDefault: isDefault
                }
            });
        } else {
        // Update the address object
        let address = this.data.address;
        address.isDefault = isDefault;
        
        // Use setTimeout to prevent UI thread blocking and flickering
        setTimeout(() => {
            this.setData({
                address: address
            });
            
            wx.hideLoading();
            
            // Show a brief feedback toast
            wx.showToast({
                title: isDefault ? '已设为默认地址' : '已取消默认地址',
                icon: 'none',
                duration: 1000
            });
        }, 100);
        }
    },
    getAddressDetail() {
        let that = this;
        util.request(api.AddressDetail, {id: that.data.addressId}).then(function (res) {
            if (res.success) {
                that.setData({
                    address: res.data
                });
                
                // Check if form is complete when loading existing address
                that.checkFormComplete();
            }
        });
    },
    setRegionDoneStatus() {
        let that = this;
        
        // Log the current select region list for debugging
        console.log('setRegionDoneStatus - current selectRegionList:', JSON.stringify(that.data.selectRegionList));
        
        // Verify each region item
        for (let i = 0; i < that.data.selectRegionList.length; i++) {
            let item = that.data.selectRegionList[i];
            console.log(`Region Level ${i+1} (${item.type || i+1}): id=${item.id}, name=${item.name}`);
        }
        
        // Check if all 3 levels (province, city, district) have valid IDs
        let province = that.data.selectRegionList[0] && that.data.selectRegionList[0].id > 0;
        let city = that.data.selectRegionList[1] && that.data.selectRegionList[1].id > 0;
        let district = that.data.selectRegionList[2] && that.data.selectRegionList[2].id > 0;
        
        let doneStatus = province && city && district;
        
        console.log(`Region selection status: province=${province}, city=${city}, district=${district} => done=${doneStatus}`);
        
        // Make sure we always update the status
        that.setData({
            selectRegionDone: doneStatus
        });
    },
    chooseRegion() {
        let that = this;
        
        console.log("Starting region selection process");
        
        // Clear any existing selection if needed
        this.setData({
            selectRegionDone: false
        });
        
        // Prevent page stack issues by using animation frame
        setTimeout(() => {
            // Open the region selector
            this.setData({
                openSelectRegion: true
            });

            // Get current address data
            let address = this.data.address;
            
            // Debug the address data
            console.log('Address data when opening region selector:', JSON.stringify(address));
            
            // Check if we already have region data
            if (address && address.provinceId > 0 && address.cityId > 0 && address.districtId > 0) {
                console.log("Using existing region data");
                
                // Create a properly structured selectRegionList
                let selectRegionList = [
                    {
                        id: address.provinceId,
                        name: address.provinceName,
                        parentId: 1,
                        type: 1
                    },
                    {
                        id: address.cityId,
                        name: address.cityName,
                        parentId: address.provinceId,
                        type: 2
                    },
                    {
                        id: address.districtId,
                        name: address.districtName,
                        parentId: address.cityId,
                        type: 3
                    }
                ];

                // Update state with existing region data
                this.setData({
                    selectRegionList: selectRegionList,
                    regionType: 3  // Start at district level
                });

                // Load the district list
                this.getRegionList(address.cityId);
                
                // If we have all region data, mark as done
                if (address.provinceId && address.cityId && address.districtId) {
                    console.log('All regions present, marking as done');
                    this.setData({
                        selectRegionDone: true
                    });
                }
            } else {
                console.log("Starting new region selection");
                
                // Start fresh region selection
                this.setData({
                    selectRegionList: [
                        {id: 0, name: '省份', parentId: 1, type: 1},
                        {id: 0, name: '城市', parentId: 1, type: 2},
                        {id: 0, name: '区县', parentId: 1, type: 3}
                    ],
                    regionType: 1,  // Start at province level
                    selectRegionDone: false
                });
                
                // Load the province list
                this.getRegionList(1);
            }

            // Update done status
            this.setRegionDoneStatus();
            
            // Log the final state
            console.log('Region selector state after initialization:', {
                selectRegionList: JSON.stringify(this.data.selectRegionList),
                regionType: this.data.regionType,
                selectRegionDone: this.data.selectRegionDone
            });
        }, 50);
    },
    chooseLocation() {
        let that = this;
        wx.chooseLocation({
            success: function(res) {
              
                // 成功获取位置信息
                console.log('选择地址成功：', res);
                console.log("---------------")
                // 更新地址信息
                let address = "";
                if(res.address) address+= res.address;
                if(res.name) address+= " "+ res.name;
                that.parseAddressText(address);
                console.log("++++++++++++++++++")
                console.log(address)
                // 尝试解析地址信息
                //that.parseLocationAddress(res.address, res.latitude, res.longitude);
                
                that.setData({
                    address: address
                });
                
                wx.showToast({
                    title: '已选择位置',
                    icon: 'success',
                    duration: 1500
                });
            },
            fail: function(err) {
                console.log('选择地址失败：', err);
                if (err.errMsg !== "chooseLocation:fail cancel") {
                    wx.showToast({
                        title: '获取位置失败',
                        icon: 'none',
                        duration: 1500
                    });
                }
            }
        });
    },
    
    // 解析位置信息，获取省市区信息
    parseLocationAddress(addressText, latitude, longitude) {
        let that = this;
        
        // 调用腾讯地图逆地址解析接口
        // 注意：实际开发中需要替换为您的腾讯地图开发者key
        wx.request({
            url: 'https://apis.map.qq.com/ws/geocoder/v1/',
            data: {
                location: latitude + ',' + longitude,
                key: 'YOUR_TENCENT_MAP_KEY', // 请替换为您的腾讯地图开发者key
                get_poi: 0
            },
            success: function(res) {
                console.log('逆地址解析结果：', res);
                if (res.statusCode === 200 && res.data && res.data.status === 0) {
                    let result = res.data.result;
                    let address = that.data.address;
                    
                    // 保存省市区信息
                    let provinceInfo = result.address_component.province;
                    let cityInfo = result.address_component.city;
                    let districtInfo = result.address_component.district;
                    
                    // 更新地址信息
                    // 注意：这里需要根据您的数据库中的省市区数据进行匹配
                    // 这里只是一个示例，实际项目中需要和您的后端API进行配合
                    
                    // 模拟更新省市区
                    // 实际项目中需要从您的API获取对应的ID
                    address.provinceName = provinceInfo;
                    address.cityName = cityInfo;
                    address.districtName = districtInfo;
                    address.fullRegion = provinceInfo + cityInfo + districtInfo;
                    
                    // 更新详细地址，可能会附加街道信息
                    if (result.address_component.street) {
                        address.address = result.address_component.street + 
                                         (result.address_component.street_number || '');
                    }
                    
                    that.setData({
                        address: address
                    });
                    
                    // 提示用户确认省市区
                    wx.showToast({
                        title: '已自动匹配地区信息',
                        icon: 'none',
                        duration: 2000
                    });
                } else {
                    wx.showToast({
                        title: '地区解析失败，请手动选择',
                        icon: 'none',
                        duration: 2000
                    });
                }
            },
            fail: function(err) {
                console.log('逆地址解析请求失败：', err);
                wx.showToast({
                    title: '地区解析失败，请手动选择',
                    icon: 'none',
                    duration: 2000
                });
            }
        });
    },
    
    onLoad: function (options) {
        console.log('AddressAdd onLoad, options:', options);
        
        // Prevent flickering by setting a flag to indicate page is loading
        wx.showLoading({
            title: '加载中...',
            mask: true
        });
        
        // 确保 address 对象始终初始化
        this.setData({
            address: this.data.address || {
                id: null,
                provinceId: 0,
                cityId: 0,
                districtId: 0,
                address: '',
                fullRegion: '',
                name: '',
                mobile: '',
                isDefault: 0
            }
        });
        
        // 捕获所有URL参数，不仅限于预定义的参数列表
        // 这样可以确保将来添加的参数也能被正确传递
        const orderParams = {};
        let hasOrderParams = false;
        
        for (const key in options) {
            // 排除id参数，因为它是地址ID
            if (key !== 'id' && options.hasOwnProperty(key)) {
                orderParams[key] = options[key];
                hasOrderParams = true;
            }
        }
        
        // 从storage中检查是否有 fromCheckout 标记
        const fromCheckout = wx.getStorageSync('fromCheckout');
        if (fromCheckout) {
            hasOrderParams = true;
        }
        
        // 检查是否有 source 参数，用来区分来源页面
        let source = '';
        if (options.source) {
            source = options.source;
        } else if (fromCheckout) {
            // 如果没有指定来源，但有 fromCheckout 标记，则认为是从结算页来的
            source = 'checkout';
        } else {
            // 尝试从存储中获取来源
            const storedSource = wx.getStorageSync('addressSource');
            if (storedSource) {
                source = storedSource;
            } else {
                // 默认来源为地址管理页面
                source = 'address';
            }
        }
        
        console.log('页面来源:', source);
        
        // 如果有来自下单页面的参数，保存它们
        if (hasOrderParams) {
            console.log('捕获到订单参数:', orderParams);
            this.setData({
                orderParams: orderParams,
                fromCheckout: true,
                source: source
            });
            
            // 保存到本地存储以防页面刷新
            wx.setStorageSync('orderParams', orderParams);
        } else {
            // 设置来源
            this.setData({
                source: source
            });
        }
        
        // 页面初始化 options为页面跳转所带来的参数
        if (options.id) {
            this.setData({
                addressId: options.id
            });
            this.getAddressDetail();
        }

        this.getRegionList(1);
    },
    onReady: function () {
        // Page is ready
        wx.hideLoading();
        
        // Apply hardware acceleration to prevent flickering
        wx.createSelectorQuery()
            .selectAll('.form-card, .smart-fill-section, .default-address-section, .btn-save')
            .fields({ node: true, computedStyle: ['transform'] })
            .exec((res) => {
                if (res && res[0]) {
                    console.log('Optimizing UI elements for smoother rendering');
                }
            });
    },
    onShow: function() {
        // When page shows
        // Check if we came from checkout and clean up the flag
        let fromCheckout = wx.getStorageSync('fromCheckout');
        if (fromCheckout) {
            wx.hideLoading();
            this.setData({
                fromCheckout: true
            });
        }
        
        // 从存储中恢复订单参数（如果存在）
        const storedOrderParams = wx.getStorageSync('orderParams');
        if (storedOrderParams && !this.data.orderParams) {
            console.log('从存储恢复订单参数:', storedOrderParams);
            this.setData({
                orderParams: storedOrderParams,
                fromCheckout: true
            });
        }
        
        // Set the page to a stable state
        setTimeout(() => {
            this.setData({
                pageReady: true
            });
        }, 100);
    },
    onHide: function() {
        // When navigating away, ensure the region selector is closed to prevent UI issues
        if (this.data.openSelectRegion) {
            this.setData({
                openSelectRegion: false
            });
        }
        
        // Mark page as inactive to prevent unwanted updates
        this.setData({
            pageReady: false
        });
        
        // 如果用户取消了地址添加，但我们不是通过保存按钮离开的，
        // 则需要保留订单参数以便下次返回时使用
        if (this.data.fromCheckout && this.data.orderParams) {
            console.log('页面隐藏，保留订单参数');
            wx.setStorageSync('orderParams', this.data.orderParams);
        }
    },
    selectRegionType(event) {
        console.log('selectRegionType called with event:', event);
        
        let that = this;
        let regionTypeIndex = event.target.dataset.regionTypeIndex;
        
        console.log('Selected regionTypeIndex:', regionTypeIndex);
        console.log('Current regionType:', this.data.regionType);
        console.log('Current selectRegionList:', JSON.stringify(this.data.selectRegionList));
        
        let selectRegionList = this.data.selectRegionList;
        
        // Validate the regionTypeIndex
        if (regionTypeIndex < 0 || regionTypeIndex > 2) {
            console.error('Invalid regionTypeIndex:', regionTypeIndex);
            return false;
        }

        // Calculate the region type (1-based: 1=province, 2=city, 3=district)
        let newRegionType = regionTypeIndex + 1;
        
        // Can only go to already selected regions or the next one to select
        if (newRegionType > this.data.regionType || 
            (newRegionType < this.data.regionType && selectRegionList[regionTypeIndex].id === 0)) {
            console.log('Cannot navigate to unselected region or beyond current selection');
            return false;
        }

        console.log('Changing regionType from', this.data.regionType, 'to', newRegionType);
        
        // Update region type
        this.setData({
            regionType: newRegionType
        });

        // Get the region item for this level
        let selectRegionItem = selectRegionList[regionTypeIndex];
        
        console.log('Selected region item for navigation:', JSON.stringify(selectRegionItem));
        
        // If this level has a parent ID, load its children
        if (selectRegionItem.parentId > 0) {
            let parentId = selectRegionItem.parentId;
            
            // If we're going to provinces, use the root parent ID
            if (newRegionType === 1) {
                parentId = 1;
            }
            
            console.log('Loading children for parentId:', parentId);
            this.getRegionList(parentId);
        } else if (selectRegionItem.id > 0) {
            // If there's no parent ID but we have an ID, load children
            console.log('Loading children for id:', selectRegionItem.id);
            this.getRegionList(selectRegionItem.id);
        } else {
            // If neither, start from the beginning
            console.log('No parent or ID available, loading from root');
            this.getRegionList(1);
        }
        
        // Update the done status
        this.setRegionDoneStatus();
    },
    selectRegion(event) {
        // Add debugging
        console.log('selectRegion called with event:', event);
        console.log('Current regionType:', this.data.regionType);
        console.log('Current selectRegionList:', JSON.stringify(this.data.selectRegionList));
        console.log('Current regionList:', JSON.stringify(this.data.regionList));
        
        let that = this;
        let regionIndex = event.target.dataset.regionIndex;
        
        // Validate regionIndex
        if (regionIndex === undefined || regionIndex === null) {
            console.error('Invalid regionIndex:', regionIndex);
            return false;
        }
        
        console.log('Selected regionIndex:', regionIndex);
        
        // Make sure regionList exists and has items
        if (!this.data.regionList || !this.data.regionList.length) {
            console.error('regionList is empty or undefined');
            return false;
        }
        
        // Make sure the selected index is valid
        if (regionIndex >= this.data.regionList.length) {
            console.error('regionIndex out of bounds:', regionIndex, 'regionList length:', this.data.regionList.length);
            return false;
        }
        
        let regionItem = this.data.regionList[regionIndex];
        console.log('Selected regionItem:', JSON.stringify(regionItem));
        
        // Validate the regionItem
        if (!regionItem || !regionItem.id) {
            console.error('Invalid regionItem:', regionItem);
            return false;
        }
        
        let regionType = this.data.regionType;
        console.log('Current regionType (before update):', regionType);
        
        // Make sure we have a valid region type
        if (regionType < 1 || regionType > 3) {
            console.error('Invalid regionType:', regionType);
            return false;
        }
        
        let selectRegionList = this.data.selectRegionList;
        
        // Update the region at the current level
        selectRegionList[regionType - 1] = {
            id: regionItem.id,
            name: regionItem.name,
            parentId: regionItem.parentId,
            type: regionItem.type || regionType
        };
        
        console.log('Updated selectRegionList:', JSON.stringify(selectRegionList));
        
        // If we're not at district level yet, move to next level
        if (regionType < 3) {
            // Move to next level
            this.setData({
                selectRegionList: selectRegionList,
                regionType: regionType + 1
            });
            
            // Get regions for next level
            this.getRegionList(regionItem.id);
        } else {
            // We're at district level, update the selection
            this.setData({
                selectRegionList: selectRegionList
            });
        }
        
        // Reset any regions at levels beyond the current one
        for (let i = regionType; i < 3; i++) {
            selectRegionList[i] = {
                id: 0, 
                name: i === 1 ? '城市' : '区县', 
                parentId: 0,
                type: i + 1
            };
        }
        
        // Update the UI with the modified list
        this.setData({
            selectRegionList: selectRegionList
        });
        
        // Update selected state in the region list
        let regionList = this.data.regionList.map((item) => {
            // If this item is at current level and matches the selected ID
            if (this.data.regionType === (item.type || regionType) && item.id === selectRegionList[this.data.regionType - 1].id) {
                item.selected = true;
            } else {
                item.selected = false;
            }
            return item;
        });
        
        this.setData({
            regionList: regionList
        });
        
        // Check if all regions are selected
        this.setRegionDoneStatus();
        
        // If district (level 3) is selected, show "确定" immediately
        if (regionType === 3) {
            console.log("District selected, should be complete");
            // Force update in case any state issues
            setTimeout(() => {
                this.setData({
                    selectRegionDone: true
                });
            }, 100);
        }
    },
    doneSelectRegion() {
        // Remove debugger
        console.log('doneSelectRegion called');
        console.log('Current selectRegionList:', JSON.stringify(this.data.selectRegionList));
        
        // Direct check for all regions being selected
        let hasProvince = this.data.selectRegionList[0] && this.data.selectRegionList[0].id > 0;
        let hasCity = this.data.selectRegionList[1] && this.data.selectRegionList[1].id > 0;
        let hasDistrict = this.data.selectRegionList[2] && this.data.selectRegionList[2].id > 0;
        
        // Check if all regions are actually selected
        let allSelected = hasProvince && hasCity && hasDistrict;
        
        console.log(`Region status: Province: ${hasProvince}, City: ${hasCity}, District: ${hasDistrict} => Complete: ${allSelected}`);
        
        // Force set the status if needed
        if (allSelected !== this.data.selectRegionDone) {
            console.log('Inconsistency detected, fixing selectRegionDone flag');
            this.setData({
                selectRegionDone: allSelected
            });
        }
        
        // Process even if the flag is wrong but all regions are selected
        if (allSelected) {
            // Ensure address object exists by creating a new one if it's null
            let address = this.data.address || {
                id: null,
                provinceId: 0,
                cityId: 0,
                districtId: 0,
                address: '',
                fullRegion: '',
                name: '',
                mobile: '',
                isDefault: 0
            };
            
            let selectRegionList = this.data.selectRegionList;
            
            // Save selected regions to address
            address.provinceId = selectRegionList[0].id;
            address.cityId = selectRegionList[1].id;
            address.districtId = selectRegionList[2].id;
            address.provinceName = selectRegionList[0].name;
            address.cityName = selectRegionList[1].name;
            address.districtName = selectRegionList[2].name;
            address.fullRegion = selectRegionList.map(item => {
                return item.name;
            }).join('');

            // Show success feedback
            wx.showToast({
                title: '地区已选择',
                icon: 'success',
                duration: 500
            });

            // Close the region selector after a short delay
            setTimeout(() => {
                this.setData({
                    address: address,
                    openSelectRegion: false
                });
                
                console.log('Updated address with region data:', JSON.stringify(address));
                
                // Check if form is complete after region selection
                this.checkFormComplete();
            }, 300);
            
            return true;
        } else {
            // Show a toast to inform user selection is incomplete
            wx.showToast({
                title: '请选择完整的省市区信息',
                icon: 'none',
                duration: 1500
            });
            return false;
        }
    },
    cancelSelectRegion() {
        this.setData({
            openSelectRegion: false,
            regionType: this.data.regionDoneStatus ? 3 : 1
        });
    },
    getRegionList(regionId) {
        if (!regionId) {
            console.error('Invalid regionId:', regionId);
            return;
        }
        
        let that = this;
        let regionType = that.data.regionType;
        
        console.log(`Getting region list for regionId: ${regionId}, regionType: ${regionType}`);
        
        // Show loading 
        wx.showLoading({
            title: '加载中...',
            mask: true
        });
        
        util.request(api.RegionList, {parentId: regionId}).then(function (res) {
            wx.hideLoading();
            console.log('Region API response:', JSON.stringify(res));
            
            if (res.success && res.data && res.data.length) {
                let updatedRegionList = res.data.map(item => {
                    // Make sure each item has all required properties
                    item.type = item.type || regionType;
                    
                    //标记已选择的
                    if (regionType == item.type && 
                        that.data.selectRegionList[regionType - 1] && 
                        that.data.selectRegionList[regionType - 1].id == item.id) {
                        item.selected = true;
                    } else {
                        item.selected = false;
                    }
                    return item;
                });
                
                console.log(`Found ${updatedRegionList.length} regions for region type ${regionType}`);
                
                that.setData({
                    regionList: updatedRegionList
                });
                
                // If only one option, auto-select it
                if (updatedRegionList.length === 1) {
                    console.log(`Auto-selecting the only option for region type ${regionType}`);
                    let event = {
                        target: {
                            dataset: {
                                regionIndex: 0
                            }
                        }
                    };
                    that.selectRegion(event);
                }
            } else {
                console.error('Failed to get region list or empty list:', res);
                wx.hideLoading();
                wx.showToast({
                    title: '获取地区列表失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        }).catch(function(error) {
            wx.hideLoading();
            console.error('Error fetching region list:', error);
            wx.showToast({
                title: '网络错误',
                icon: 'none',
                duration: 2000
            });
        });
    },
    cancelAddress() {
        // 尝试返回到结算页面
        if (this.returnToCheckout()) {
            return;
        }
        
        // 正常返回上一页
        wx.navigateBack();
    },
    saveAddress() {
        console.log('Attempting to save address:', this.data.address);
        let address = this.data.address;
        
        // Check form completion status first
        if (!this.checkFormComplete()) {
            // Generate specific feedback for what's missing
            let missingFields = [];
            
            if (!address.name || address.name.length < 2) {
                missingFields.push('姓名');
            }
            
            if (!address.mobile || address.mobile.length !== 11) {
                missingFields.push('手机号码');
            }
            
            if (address.districtId === 0) {
                missingFields.push('所在地区');
            }
            
            if (!address.address || address.address.trim() === '') {
                missingFields.push('详细地址');
            }
            
            // Show specific missing fields in the error message
            util.showErrorToast(`请完善${missingFields.join('、')}信息`);
            return false;
        }

        // Form is complete, proceed with saving
        wx.showLoading({
            title: '保存中...',
            mask: true
        });
        
        // Create a clean request object
        const requestData = {
            name: address.name,
            mobile: address.mobile,
            provinceId: address.provinceId,
            cityId: address.cityId,
            districtId: address.districtId,
            address: address.address,
            isDefault: address.isDefault
        };
        
        // Only include ID if it's a valid value (not null, undefined, or 0)
        if (address.id && address.id !== 'null' && address.id > 0) {
            requestData.id = address.id;
        }
        
        console.log('Address save request data:', requestData);
        
        let that = this;
        util.request(api.AddressSave, requestData, 'POST').then(function (res) {
            wx.hideLoading();
            
            if (res.success) {
            
                // Fix: Store the addressId properly
                if (res.data && res.data.id) {
                    // Store the numeric ID directly
                    wx.setStorageSync('addressId', parseInt(res.data.id));
                    console.log('Saved addressId to storage:', res.data.id);
                }
                
                wx.showToast({
                    title: '保存成功',
                    icon: 'success',
                    duration: 1000,
                    success: () => {
                        setTimeout(() => {
                            // 尝试返回到结算页面
                            if (that.returnToCheckout()) {
                                return;
                            }
                            
                            // 正常返回上一页
                            wx.navigateBack();
                        }, 1000);
                    }
                });
            } else {
                wx.showModal({
                    title: '保存失败',
                    content: res.message || '请稍后重试',
                    showCancel: false
                });
            }
        }).catch(function(err) {
            wx.hideLoading();
            
            wx.showModal({
                title: '网络错误',
                content: '保存失败，请检查网络连接',
                showCancel: false
            });
        });
    },
    
    // 智能粘贴功能
    smartPaste() {
        let that = this;
        
        // Show loading
        wx.showLoading({
            title: '智能解析中...',
            mask: true
        });
        
        // 确保 address 对象已初始化
        if (!that.data.address) {
            that.setData({
                address: {
                    id: null,
                    provinceId: 0,
                    cityId: 0,
                    districtId: 0,
                    address: '',
                    fullRegion: '',
                    name: '',
                    mobile: '',
                    isDefault: 0
                }
            });
        }
        
        wx.getClipboardData({
            success: function(res) {
                if (res.data && res.data.length > 0) {
                    console.log('获取到剪贴板内容:', res.data);                    
                    // 使用新方法解析剪贴板内容
                    that.parseAddressText(res.data);                    
                    wx.hideLoading();
                } else {
                    wx.hideLoading();
                    wx.showToast({
                        title: '剪贴板为空',
                        icon: 'none',
                        duration: 2000
                    });
                }
            },
            fail: function(err) {
                wx.hideLoading();
                console.error('获取剪贴板失败:', err);
                wx.showToast({
                    title: '获取剪贴板失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        });
    },
    
    // 识别提交 - 改为手动输入模式
    smartScan() {
        // 确保 address 对象已初始化
        if (!this.data.address) {
            this.setData({
                address: {
                    id: null,
                    provinceId: 0,
                    cityId: 0,
                    districtId: 0,
                    address: '',
                    fullRegion: '',
                    name: '',
                    mobile: '',
                    isDefault: 0
                }
            });
        }
        
        wx.showActionSheet({
            itemList: ['从微信聊天记录粘贴', '手动填写', '粘贴微信收货地址'],
            success: (res) => {
                if (res.tapIndex === 0) {
                    // 粘贴
                    this.smartPaste();
                } else if (res.tapIndex === 1) {
                    // 打开弹窗让用户手动输入完整地址
                    wx.showModal({
                        title: '手动输入',
                        editable: true,
                        placeholderText: '请输入姓名、手机号和完整地址，如：张三 13800138000 北京市朝阳区xxx',
                        success: (res) => {
                            if (res.confirm && res.content) {
                                this.parseAddressText(res.content);
                            }
                        }
                    });
                } else if (res.tapIndex === 2) {
                    // 提示用户如何操作
                    wx.showModal({
                        title: '粘贴微信收货地址',
                        content: '请打开微信-我-支付-收货地址-长按地址复制，然后回到小程序粘贴',
                        confirmText: '已复制',
                        success: (res) => {
                            if (res.confirm) {
                                this.smartPaste();
                            }
                        }
                    });
                }
            }
        });
    },
    
    // 解析地址文本
    parseAddressText(text) {
        if (!text) return;
        
        console.log('开始解析地址文本:', text);
        
        // 确保 address 对象存在，如果为 null 则使用默认值初始化
        let address = this.data.address || {
            id: null,
            provinceId: 0,
            cityId: 0,
            districtId: 0,
            address: '',
            fullRegion: '',
            name: '',
            mobile: '',
            isDefault: 0
        };
        
        const originalText = text;
        
        try {
            // 使用 zh-address-parse 库解析地址
            const options = {
                type: 0, // 使用正则方式解析
                textFilter: ['电話', '電話', '联系人', '收货人', '收件人'], // 预清洗的字段
                nameMaxLength: 4 // 查找最大的中文名字长度
            };
            
            const parseResult = AddressParse(text, options);
            console.log('地址解析结果:', parseResult);
            
            // 填充解析结果到地址对象
            if (parseResult) {
                // 处理姓名
                if (parseResult.name && parseResult.name.length >= 2) {
                    address.name = parseResult.name;
                }
                
                // 处理手机号
                if (parseResult.phone && parseResult.phone.length === 11) {
                    address.mobile = parseResult.phone;
                }
                
                // 处理省市区
                if (parseResult.province || parseResult.city || parseResult.area) {
                    // 保存解析出的省市区文本
                    address.provinceName = parseResult.province || '';
                    address.cityName = parseResult.city || '';
                    address.districtName = parseResult.area || '';
                    address.fullRegion = (parseResult.province || '') + 
                                        (parseResult.city || '') + 
                                        (parseResult.area || '');
                    
                    // 尝试查找真实的省市区ID
                    if (parseResult.province) {
                        this.findRegionIds(parseResult.province, parseResult.city, parseResult.area);
                    }
                }
                
                // 处理详细地址
                if (parseResult.detail) {
                    address.address = parseResult.detail;
                }
                
                // 更新数据
                this.setData({
                    address: address
                });
                
                // 检查表单是否完整
                this.checkFormComplete();
                
                // 显示结果
                let filledFields = [];
                if (address.name) filledFields.push('姓名');
                if (address.mobile) filledFields.push('手机号');
                if (parseResult.province || parseResult.city || parseResult.area) filledFields.push('地区');
                if (address.address) filledFields.push('详细地址');
                
                if (filledFields.length > 0) {
                    wx.showToast({
                        title: `已填写${filledFields.join('、')}`,
                        icon: 'success',
                        duration: 1500
                    });
                } else {
                    wx.showModal({
                        title: '识别结果',
                        content: '只识别到部分信息，请手动补充。',
                        showCancel: false
                    });
                }
                
                // 如果提取到了地址但未能完全提取省市区，提示用户完善选择
                if (address.address && (!parseResult.province || !parseResult.city || !parseResult.area)) {
                    setTimeout(() => {
                        wx.showToast({
                            title: '请完善所在地区',
                            icon: 'none',
                            duration: 2000
                        });
                    }, 1600);
                }
                
                // 记录到控制台用于调试
                console.log('智能填充结果:', {
                    原文本: originalText,
                    姓名: address.name,
                    手机号: address.mobile,
                    省份: address.provinceName,
                    城市: address.cityName,
                    区县: address.districtName,
                    详细地址: address.address
                });
            } else {
                // 解析失败
                wx.showModal({
                    title: '识别失败',
                    content: '无法识别地址信息，请手动填写。',
                    showCancel: false
                });
            }
        } catch (error) {
            console.error('地址解析出错:', error);
            
            // 出错时依然尝试使用原有函数逻辑进行解析
            this.parseAddressTextFallback(originalText);
            
            wx.showModal({
                title: '地址解析异常',
                content: '解析过程出现错误，已尝试备用方案。',
                showCancel: false
            });
        }
    },
    
    // 原有的地址解析方法作为备用
    parseAddressTextFallback(text) {
        if (!text) return;
        
        console.log('使用备用方法解析地址文本:', text);
        
        // 确保 address 对象存在，如果为 null 则使用默认值初始化
        let address = this.data.address || {
            id: null,
            provinceId: 0,
            cityId: 0,
            districtId: 0,
            address: '',
            fullRegion: '',
            name: '',
            mobile: '',
            isDefault: 0
        };
        let originalText = text;
        
        // 保存原始文本用于区县提取
        const fullOriginalText = text;
        
        // 预处理文本，移除多余空格，统一分隔符
        text = text.replace(/\s+/g, ' ')
            .replace(/,|，|;|；|:|：|\|/g, ' ')
            .replace(/\(|\)|（|）|《|》|【|】|\[|\]/g, ' ')
            .trim();
        
        console.log('预处理后文本:', text);
        
        // 1. 手机号匹配 - 优先匹配手机号
        const phoneReg = /(?:电话|手机|联系方式|联系电话|手机号)?[\s:：]*?(1[3-9]\d{9})/;
        const phoneMatch = text.match(phoneReg);
        
        if (phoneMatch && phoneMatch[1]) {
            address.mobile = phoneMatch[1];
            text = text.replace(phoneMatch[0], ' ');
            console.log('提取到手机号:', address.mobile);
            console.log('移除手机号后的文本:', text);
        }
        
        // 2. 名字匹配 - 通常在开头或手机号附近
        // 策略：先看是否有明确的"收货人"标记，否则查找2-4个连续汉字
        const nameWithLabelReg = /(?:收货人|收件人|姓名|名字)[:：\s]*([\u4e00-\u9fa5]{2,4})/;
        const nameWithLabelMatch = text.match(nameWithLabelReg);
        
        if (nameWithLabelMatch && nameWithLabelMatch[1]) {
            address.name = nameWithLabelMatch[1];
            text = text.replace(nameWithLabelMatch[0], ' ');
            console.log('通过标签提取到姓名:', address.name);
        } else {
            // 尝试在文本开头或手机号附近找2-4个连续汉字作为姓名
            const namePatterns = [
                /^([\u4e00-\u9fa5]{2,4})\s/,  // 开头的2-4个汉字
                /\s([\u4e00-\u9fa5]{2,4})\s/  // 被空格包围的2-4个汉字
            ];
            
            for (let pattern of namePatterns) {
                const nameMatch = text.match(pattern);
                if (nameMatch && nameMatch[1]) {
                    address.name = nameMatch[1];
                    text = text.replace(nameMatch[1], ' ');
                    console.log('通过位置提取到姓名:', address.name);
                    break;
                }
            }
        }
        
        // 3. 增强提取省市区信息 - 改进匹配模式
        // 整体地址正则匹配 - 尝试一次匹配完整的省市区结构
        const fullAddressPatterns = [
            // 完整省市区模式：xx省xx市xx区/县
            /(北京|上海|天津|重庆|广东|广西|山东|山西|河北|河南|湖北|湖南|江苏|浙江|江西|黑龙江|吉林|辽宁|福建|安徽|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|西藏|宁夏|新疆|香港|澳门)(?:省|市|自治区)?(?:\s*)?([\u4e00-\u9fa5]{2,10})(?:市|地区)?(?:\s*)?([\u4e00-\u9fa5]{2,10})(?:区|县|市)?/,
            // 直辖市+区模式：北京市xx区
            /(北京|上海|天津|重庆)(?:市)?(?:\s*)?([\u4e00-\u9fa5]{2,10})(?:区|县)?/
        ];
        
        let province = '', city = '', district = '';
        let fullAddressMatch = null;
        
        // 尝试匹配完整的省市区格式
        for (let pattern of fullAddressPatterns) {
            const match = text.match(pattern);
            if (match) {
                fullAddressMatch = match;
                province = match[1];
                
                if (pattern === fullAddressPatterns[0]) {
                    // 普通省份-城市-区县模式
                    city = match[2] || '';
                    district = match[3] || '';
                } else if (pattern === fullAddressPatterns[1]) {
                    // 直辖市模式
                    city = match[1]; // 直辖市同时作为省份和城市
                    district = match[2] || '';
                }
                
                console.log('匹配到完整地址格式:', {省: province, 市: city, 区县: district});
                break;
            }
        }
        
        // 如果完整匹配失败，则尝试单独匹配
        if (!fullAddressMatch) {
            // 逐个匹配省/市/区
            const regions = [
                // 直辖市
                { type: 'province', pattern: /(北京|上海|天津|重庆)市?/ },
                // 省/自治区
                { type: 'province', pattern: /(广东|广西|山东|山西|河北|河南|湖北|湖南|江苏|浙江|江西|黑龙江|吉林|辽宁|福建|安徽|四川|贵州|云南|陕西|甘肃|青海|台湾|内蒙古|西藏|宁夏|新疆|香港|澳门)(?:省|自治区)?/ },
                // 城市 - 更强的匹配
                { type: 'city', pattern: /([\u4e00-\u9fa5]{2,6})[市](?=[区县]|$|\s)/ },  // 明确带"市"字
                { type: 'city', pattern: /([\u4e00-\u9fa5]{2,6})市/ },  // 标准城市名
                // 区县 - 更强的匹配
                { type: 'district', pattern: /([\u4e00-\u9fa5]{2,6})[区](?=$|\s)/ },  // 明确带"区"字
                { type: 'district', pattern: /([\u4e00-\u9fa5]{2,6})[县](?=$|\s)/ },  // 明确带"县"字
                { type: 'district', pattern: /([\u4e00-\u9fa5]{2,6})区/ },  // 标准区名
                { type: 'district', pattern: /([\u4e00-\u9fa5]{2,6})县/ }   // 标准县名
            ];
            
            let regionText = '';
            
            // 从文本中提取地址信息，尝试匹配省市区
            for (let region of regions) {
                const matches = text.match(region.pattern);
                if (matches && matches[1]) {
                    if (region.type === 'province' && !province) {
                        province = matches[1];
                        regionText += matches[0];
                        console.log('提取到省份:', province);
                    } else if (region.type === 'city' && !city) {
                        // 避免省市同名的情况
                        if (matches[1] !== province) {
                            city = matches[1];
                            regionText += matches[0];
                            console.log('提取到城市:', city);
                        }
                    } else if (region.type === 'district' && !district) {
                        district = matches[1];
                        regionText += matches[0];
                        console.log('提取到区县:', district);
                    }
                }
            }
            
            // 特殊处理直辖市，如果只识别到直辖市，将其同时作为省和市
            if ((province === '北京' || province === '上海' || province === '天津' || province === '重庆') && !city) {
                city = province;
                console.log('将直辖市同时作为省和市:', city);
            }
        }
        
        // 特殊情况：城市名紧跟区县名，没有明显分隔（如"杭州西湖区"）
        if (province && !city && !district) {
            // 尝试从地址中识别常见城市
            const commonCities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '天津', '重庆', '武汉', '成都', 
                                '西安', '长沙', '苏州', '郑州', '青岛', '大连', '宁波', '厦门', '福州', '济南', 
                                '合肥', '昆明', '南宁', '长春', '哈尔滨', '沈阳', '南昌', '贵阳', '兰州', '太原'];
            
            for (let potentialCity of commonCities) {
                if (text.includes(potentialCity) && potentialCity !== province) {
                    let cityIndex = text.indexOf(potentialCity);
                    // 找到了城市，查看其后是否有区县
                    let afterCityText = text.substring(cityIndex + potentialCity.length);
                    let districtMatch = afterCityText.match(/^([\u4e00-\u9fa5]{2,4})(?:区|县|市)?/);
                    
                    if (districtMatch) {
                        city = potentialCity;
                        district = districtMatch[1];
                        console.log('从连续文本中识别到城市和区县:', {城市: city, 区县: district});
                    } else {
                        city = potentialCity;
                        console.log('从文本中识别到城市:', city);
                    }
                    break;
                }
            }
        }
        
        // 增强区县识别 - 使用多种策略
        if (province && city && !district) {
            // 1. 常见的区县后缀模式
            const districtSuffixes = ['区', '县', '市'];
            const districtPatterns = [
                // 更宽松的区县匹配
                new RegExp(city + '([\u4e00-\u9fa5]{1,6})(?:区|县|市)?'),  // 城市名后紧跟区县名
                new RegExp('([\u4e00-\u9fa5]{1,6})(?:区|县|市)(?=$|\\s)'),  // 末尾或空格前的区县名
                /([\u4e00-\u9fa5]{2,5})(?:街道|镇|乡)/  // 街道/镇/乡可能是区县线索
            ];
            
            for (let pattern of districtPatterns) {
                const districtMatch = fullOriginalText.match(pattern);
                if (districtMatch && districtMatch[1]) {
                    // 检查提取到的区县名是否合理（长度、是否包含城市名等）
                    let potentialDistrict = districtMatch[1];
                    
                    // 避免区县名完全包含城市名
                    if (potentialDistrict !== city && !city.includes(potentialDistrict)) {
                        district = potentialDistrict;
                        console.log('额外匹配到区县:', district);
                        break;
                    }
                }
            }
            
            // 2. 如果还未找到区县，使用更宽松的匹配
            if (!district) {
                // 尝试找到除了省市以外的其他地名
                const cleanedText = fullOriginalText
                    .replace(new RegExp(province + '(?:省|市|自治区)?', 'g'), '')
                    .replace(new RegExp(city + '(?:市|地区)?', 'g'), '')
                    .replace(/\d+号/, '') // 移除门牌号
                    .replace(/\s+/g, '');
                
                // 找出所有可能的地名（2-5个字的连续汉字）
                const potentialPlaces = cleanedText.match(/[\u4e00-\u9fa5]{2,5}/g);
                
                if (potentialPlaces && potentialPlaces.length > 0) {
                    console.log('潜在地名:', potentialPlaces);
                    
                    // 检查是否有地名后跟区县后缀
                    for (let suffix of districtSuffixes) {
                        for (let place of potentialPlaces) {
                            if (cleanedText.includes(place + suffix)) {
                                district = place;
                                console.log('从后缀识别区县:', district);
                                break;
                            }
                        }
                        if (district) break;
                    }
                    
                    // 如果还没找到，选择第一个不是明显非地名的词
                    if (!district) {
                        const nonPlaceWords = ['先生', '女士', '收货', '电话', '手机', '详细', '地址'];
                        for (let place of potentialPlaces) {
                            let isNonPlace = false;
                            for (let nonPlace of nonPlaceWords) {
                                if (place.includes(nonPlace)) {
                                    isNonPlace = true;
                                    break;
                                }
                            }
                            
                            if (!isNonPlace) {
                                district = place;
                                console.log('猜测区县:', district);
                                break;
                            }
                        }
                    }
                }
            }
            
            // 3. 检查常见城市特殊区县
            const cityDistrictMap = {
                '北京': ['朝阳', '海淀', '丰台', '石景山', '西城', '东城', '崇文', '宣武', '通州', '顺义', '房山', '门头沟', '昌平', '大兴', '怀柔', '平谷', '密云', '延庆'],
                '上海': ['黄浦', '静安', '徐汇', '长宁', '普陀', '虹口', '杨浦', '闵行', '宝山', '嘉定', '浦东', '金山', '松江', '青浦', '奉贤', '崇明'],
                '广州': ['天河', '越秀', '海珠', '荔湾', '白云', '黄埔', '番禺', '花都', '南沙', '增城', '从化'],
                '深圳': ['福田', '罗湖', '南山', '盐田', '宝安', '龙岗', '龙华', '坪山', '光明']
            };
            
            if (!district && cityDistrictMap[city]) {
                for (let potentialDistrict of cityDistrictMap[city]) {
                    if (fullOriginalText.includes(potentialDistrict)) {
                        district = potentialDistrict;
                        console.log('从城市特殊区县表识别:', district);
                        break;
                    }
                }
            }
        }
        
        // 如果成功提取了省市区信息
        let regionFound = !!(province || city || district);
        if (regionFound) {
            console.log('提取到地区信息:', {省: province, 市: city, 区: district});
            
            // 临时保存，后面会调用API获取真正的省市区ID
            address.provinceName = province;
            address.cityName = city ? city : '';
            address.districtName = district ? district : '';
            address.fullRegion = (province || '') + (city ? city : '') + (district ? district : '');
            
            // 尝试通过API查找真实的省市区ID
            if (province) {
                this.findRegionIds(province, city, district);
            }
            
            // 从文本中移除已识别的省市区信息
            if (province) text = text.replace(new RegExp(province + '(?:省|市|自治区)?', 'g'), ' ');
            if (city) text = text.replace(new RegExp(city + '(?:市|地区)?', 'g'), ' ');
            if (district) text = text.replace(new RegExp(district + '(?:区|县|市)?', 'g'), ' ');
            text = text.replace(/\s+/g, ' ').trim();
        }
        
        // 4. 处理详细地址
        // 清理文本，移除多余的标记和空格
        text = text.replace(/地址[:：\s]*/g, '')
                   .replace(/详细地址[:：\s]*/g, '')
                   .replace(/详细[:：\s]*/g, '')
                   .replace(/\s+/g, ' ')
                   .trim();
        
        if (text) {
            address.address = text;
            console.log('提取到详细地址:', address.address);
        }
        
        // 更新数据
        this.setData({
            address: address
        });
        
        // Check if form is complete after address parsing
        this.checkFormComplete();
        
        // 显示结果
        let filledFields = [];
        if (address.name) filledFields.push('姓名');
        if (address.mobile) filledFields.push('手机号');
        if (regionFound) filledFields.push('地区');
        if (address.address) filledFields.push('详细地址');
        
        if (filledFields.length > 0) {
            wx.showToast({
                title: `已填写${filledFields.join('、')}`,
                icon: 'success',
                duration: 1500
            });
        } else {
            wx.showModal({
                title: '识别失败',
                content: '无法从文本中识别出有效信息，请尝试手动填写。',
                showCancel: false
            });
        }
        
        // 如果提取到了地址但未能提取省市区，提示用户选择
        if (address.address && !regionFound) {
            setTimeout(() => {
                wx.showToast({
                    title: '请选择所在地区',
                    icon: 'none',
                    duration: 2000
                });
            }, 1600);
        }
        
        // 记录到控制台用于调试
        console.log('智能填充结果:', {
            原文本: originalText,
            姓名: address.name,
            手机号: address.mobile,
            省份: address.provinceName,
            城市: address.cityName,
            区县: address.districtName,
            详细地址: address.address
        });
    },

    // 根据提取的省市区文本查找对应的ID
    findRegionIds(province, city, district) {
        if (!province && !city && !district) return;
        
        console.log(`尝试查找地区ID: 省=${province}, 市=${city}, 区=${district}`);
        
        let that = this;
        
        // 标准化省份名称 - 处理直辖市和自治区等特殊情况
        const normalizeProvince = (name) => {
            if (name === '北京' || name === '上海' || name === '天津' || name === '重庆') {
                return name + '市';
            }
            // 处理自治区等情况
            const specialRegions = {
                '内蒙古': '内蒙古自治区',
                '广西': '广西壮族自治区',
                '宁夏': '宁夏回族自治区',
                '新疆': '新疆维吾尔自治区',
                '西藏': '西藏自治区'
            };
            
            if (specialRegions[name]) {
                return specialRegions[name];
            }
            
            // 普通省份加上"省"
            if (!/省$/.test(name) && !/自治区$/.test(name) && !/市$/.test(name)) {
                return name + '省';
            }
            
            return name;
        };
        
        // 标准化城市名称
        const normalizeCity = (name) => {
            if (!name) return '';
            return /市$/.test(name) ? name : name + '市';
        };
        
        // 标准化区县名称
        const normalizeDistrict = (name) => {
            if (!name) return '';
            if (/区$/.test(name) || /县$/.test(name) || /市$/.test(name)) {
                return name;
            }
            // 默认加"区"，实际上可能是县或者市
            return name + '区';
        };
        
        // 先获取省级列表
        wx.showLoading({
            title: '查找地区信息...',
            mask: true
        });
        
        util.request(api.RegionList, {parentId: 1}).then(function(res) {
            if (res.success && res.data && res.data.length) {
                // 尝试多种匹配策略查找省份
                const normalizedProvince = normalizeProvince(province);
                console.log('标准化后的省份名称:', normalizedProvince);
                
                // 1. 精确匹配
                let provinceItem = res.data.find(item => item.name === normalizedProvince);
                
                // 2. 包含匹配
                if (!provinceItem) {
                    provinceItem = res.data.find(item => 
                        item.name.includes(province) || province.includes(item.name.replace(/省|市|自治区/g, '')));
                }
                
                // 3. 特殊处理直辖市
                if (!provinceItem && (province === '北京' || province === '上海' || province === '天津' || province === '重庆')) {
                    provinceItem = res.data.find(item => item.name.includes(province));
                }
                
                if (provinceItem) {
                    console.log('找到省份:', provinceItem.name, provinceItem.id);
                    
                    // 设置省级ID和名称
                    let address = that.data.address;
                    address.provinceId = provinceItem.id;
                    address.provinceName = provinceItem.name;
                    
                    that.setData({
                        address: address
                    });
                    
                    // 继续查找市级
                    if (city || province === city) {
                        that.findCityId(provinceItem.id, city || province, district);
                    } else {
                        wx.hideLoading();
                        
                        // 如果只有省信息，提示用户完善
                        wx.showToast({
                            title: '已匹配省份，请选择市区',
                            icon: 'none',
                            duration: 2000
                        });
                    }
                } else {
                    wx.hideLoading();
                    console.log('未找到匹配的省份');
                    
                    // 如果有城市信息，尝试直接从城市匹配
                    if (city && city !== province) {
                        // 尝试在所有省份下查找城市
                        that.findCityInAllProvinces(city, district);
                    } else {
                        wx.showToast({
                            title: '请选择所在地区',
                            icon: 'none',
                            duration: 2000
                        });
                    }
                }
            } else {
                wx.hideLoading();
                console.error('获取省级列表失败或列表为空');
            }
        }).catch(function(err) {
            wx.hideLoading();
            console.error('获取省级列表失败:', err);
        });
    },

    // 在所有省份中查找城市
    findCityInAllProvinces(cityName, districtName) {
        let that = this;
        console.log('尝试在所有省份中查找城市:', cityName);
        
        // 1. 先获取所有省份
        util.request(api.RegionList, {parentId: 1}).then(function(res) {
            if (res.success && res.data && res.data.length) {
                // 遍历所有省份查找城市
                let provinceIds = res.data.map(item => item.id);
                let foundCity = false;
                let foundCityItem = null;
                let foundProvinceItem = null;
                
                // 创建一个计数器来跟踪查询进度
                let counter = 0;
                
                // 标准化城市名称
                const normalizedCityName = /市$/.test(cityName) ? cityName : cityName + '市';
                
                // 遍历每个省份查找城市
                provinceIds.forEach(provinceId => {
                    util.request(api.RegionList, {parentId: provinceId}).then(function(cityRes) {
                        counter++;
                        
                        if (cityRes.success && cityRes.data && cityRes.data.length) {
                            // 尝试匹配城市
                            let cityItem = cityRes.data.find(item => 
                                item.name === normalizedCityName || 
                                item.name.includes(cityName) || 
                                cityName.includes(item.name.replace('市', '')));
                            
                            if (cityItem && !foundCity) {
                                foundCity = true;
                                foundCityItem = cityItem;
                                foundProvinceItem = res.data.find(item => item.id === provinceId);
                                
                                console.log('在省份' + foundProvinceItem.name + '下找到城市:', cityItem.name);
                                
                                // 设置省市信息
                                let address = that.data.address;
                                address.provinceId = provinceId;
                                address.provinceName = foundProvinceItem.name;
                                address.cityId = cityItem.id;
                                address.cityName = cityItem.name;
                                
                                that.setData({
                                    address: address
                                });
                                
                                // 继续查找区县
                                if (districtName) {
                                    that.findDistrictId(cityItem.id, districtName);
                                } else {
                                    // 如果只找到省市，提示用户选择区县
                                    wx.hideLoading();
                                    wx.showToast({
                                        title: '已匹配到省市，请选择区县',
                                        icon: 'none',
                                        duration: 2000
                                    });
                                }
                            }
                        }
                        
                        // 所有省份都查询完成但没找到结果
                        if (counter === provinceIds.length && !foundCity) {
                            wx.hideLoading();
                            console.log('在所有省份中未找到匹配的城市');
                            wx.showToast({
                                title: '请选择所在地区',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    }).catch(function(err) {
                        counter++;
                        console.error('查询城市列表失败:', err);
                        
                        // 所有省份都查询完成但没找到结果
                        if (counter === provinceIds.length && !foundCity) {
                            wx.hideLoading();
                            wx.showToast({
                                title: '请选择所在地区',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    });
                });
            } else {
                wx.hideLoading();
                console.error('获取省份列表失败或列表为空');
            }
        }).catch(function(err) {
            wx.hideLoading();
            console.error('获取省份列表失败:', err);
        });
    },

    // 查找市级ID
    findCityId(provinceId, city, district) {
        if (!provinceId || !city) {
            wx.hideLoading();
            return;
        }
        
        let that = this;
        
        // 标准化城市名称
        const normalizedCity = /市$/.test(city) ? city : city + '市';
        console.log('标准化后的城市名称:', normalizedCity);
        
        util.request(api.RegionList, {parentId: provinceId}).then(function(res) {
            if (res.success && res.data && res.data.length) {
                // 多种匹配策略查找城市
                
                // 1. 精确匹配
                let cityItem = res.data.find(item => item.name === normalizedCity);
                
                // 2. 包含匹配
                if (!cityItem) {
                    cityItem = res.data.find(item => 
                        item.name.includes(city) || city.includes(item.name.replace('市', '')));
                }
                
                // 3. 直辖市特殊处理
                if (!cityItem && (city === '北京' || city === '上海' || city === '天津' || city === '重庆')) {
                    // 直辖市的城市列表可能是各区，从这些区中找一个作为"市"级别
                    if (res.data.length > 0) {
                        cityItem = res.data[0]; // 取第一个区作为市级
                        console.log('直辖市特殊处理，选择区域:', cityItem.name);
                    }
                }
                
                if (cityItem) {
                    console.log('找到城市:', cityItem.name, cityItem.id);
                    
                    // 设置市级ID和名称
                    let address = that.data.address;
                    address.cityId = cityItem.id;
                    address.cityName = cityItem.name;
                    
                    that.setData({
                        address: address
                    });
                    
                    // 继续查找区县级
                    if (district) {
                        that.findDistrictId(cityItem.id, district);
                    } else {
                        wx.hideLoading();
                        // 如果只匹配到省市，提示用户选择区县
                        wx.showToast({
                            title: '已匹配省市，请选择区县',
                            icon: 'none',
                            duration: 2000
                        });
                    }
                } else {
                    wx.hideLoading();
                    console.log('未找到匹配的城市');
                    
                    // 更新地区显示
                    let address = that.data.address;
                    address.fullRegion = address.provinceName;
                    
                    that.setData({
                        address: address
                    });
                    
                    wx.showToast({
                        title: '已匹配省份，请选择市区',
                        icon: 'none',
                        duration: 2000
                    });
                }
            } else {
                wx.hideLoading();
                console.error('获取市级列表失败或列表为空');
            }
        }).catch(function(err) {
            wx.hideLoading();
            console.error('获取市级列表失败:', err);
        });
    },

    // 查找区县级ID
    findDistrictId(cityId, district) {
        if (!cityId || !district) {
            wx.hideLoading();
            return;
        }
        
        let that = this;
        
        // 标准化区县名称
        let normalizedDistrict = district;
        if (!/区$/.test(district) && !/县$/.test(district) && !/市$/.test(district)) {
            // 尝试不同的后缀
            normalizedDistrict = district + '区';
        }
        
        console.log('标准化后的区县名称:', normalizedDistrict);
        
        util.request(api.RegionList, {parentId: cityId}).then(function(res) {
            if (res.success && res.data && res.data.length) {
                // 多种匹配策略查找区县
                
                // 1. 精确匹配
                let districtItem = res.data.find(item => item.name === normalizedDistrict);
                
                // 2. 尝试其他后缀 (县/市)
                if (!districtItem && !/区$/.test(normalizedDistrict)) {
                    districtItem = res.data.find(item => 
                        item.name === district + '县' || item.name === district + '市');
                }
                
                // 3. 包含匹配
                if (!districtItem) {
                    districtItem = res.data.find(item => 
                        item.name.includes(district) || district.includes(item.name.replace(/区|县|市/g, '')));
                }
                
                if (districtItem) {
                    console.log('找到区县:', districtItem.name, districtItem.id);
                    
                    // 设置区县级ID和名称
                    let address = that.data.address;
                    address.districtId = districtItem.id;
                    address.districtName = districtItem.name;
                    
                    // 设置完整地区名称
                    address.fullRegion = address.provinceName + address.cityName + address.districtName;
                    
                    that.setData({
                        address: address
                    });
                    
                    // 更新表单完成状态
                    that.checkFormComplete();
                    
                    wx.hideLoading();
                    // 匹配完整地区信息的提示
                    wx.showToast({
                        title: '已匹配地区信息',
                        icon: 'success',
                        duration: 1500
                    });
                } else {
                    wx.hideLoading();
                    console.log('未找到匹配的区县');
                    
                    // 更新地区显示
                    let address = that.data.address;
                    address.fullRegion = address.provinceName + address.cityName;
                    
                    that.setData({
                        address: address
                    });
                    
                    wx.showToast({
                        title: '已匹配省市，请选择区县',
                        icon: 'none',
                        duration: 2000
                    });
                }
            } else {
                wx.hideLoading();
                console.error('获取区县列表失败或列表为空');
            }
        }).catch(function(err) {
            wx.hideLoading();
            console.error('获取区县级列表失败:', err);
        });
    },

    preventTouchMove(e) {
        // Only prevent background scrolling when clicking on the mask
        // Allow scrolling within the region list itself
        if (e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.area === 'mask') {
            return false;
        }
        // Allow the event to propagate for the scroll-view
    },
    // Add a method to handle touchend events that might be more reliable than click/tap events
    handleDoneTouch(e) {
        // Prevent default to avoid double-firing
        if (e && e.preventDefault) {
            e.preventDefault();
        }
        
        try {
            // Check if address exists before proceeding
            if (!this.data.address) {
                console.log('Address is null in handleDoneTouch, initializing it');
                this.setData({
                    address: {
                        id: null,
                        provinceId: 0,
                        cityId: 0,
                        districtId: 0,
                        address: '',
                        fullRegion: '',
                        name: '',
                        mobile: '',
                        isDefault: 0
                    }
                });
            }
            
            // Call the normal done function
            this.doneSelectRegion();
        } catch (error) {
            console.error('Error in handleDoneTouch:', error);
            
            // Show error to user
            wx.showToast({
                title: '选择地区出错，请重试',
                icon: 'none',
                duration: 2000
            });
            
            // Reset region selector to a safe state
            this.setData({
                openSelectRegion: false
            });
        }
    },
    // Add a method to catch UI events and prevent default behavior when needed
    catchTouchEvent(e) {
        // Prevent touch events from affecting parent pages
        if (e && e.preventDefault) {
            e.preventDefault();
        }
        
        return false;
    },
    // Add a method to check if all required fields are filled
    checkFormComplete() {
        const address = this.data.address;
        
        // Return false immediately if address is null or undefined
        if (!address) {
            console.log('Form complete check: false (address is null)');
            this.setData({
                isFormComplete: false
            });
            return false;
        }
        
        // Check if all required fields are filled
        const isComplete = 
            address.name && address.name.length >= 2 && 
            address.mobile && address.mobile.length === 11 &&
            address.provinceId > 0 && address.cityId > 0 && address.districtId > 0 &&
            address.address && address.address.trim() !== '';
        
        console.log('Form complete check:', isComplete, {
            name: address.name && address.name.length >= 2,
            mobile: address.mobile && address.mobile.length === 11,
            region: address.provinceId > 0 && address.cityId > 0 && address.districtId > 0,
            address: address.address && address.address.trim() !== ''
        });
        
        // Update the state only if it's changed
        if (this.data.isFormComplete !== isComplete) {
            this.setData({
                isFormComplete: isComplete
            });
        }
        
        return isComplete;
    },
    // 定义返回到结算页面的方法，确保携带所有参数
    returnToCheckout() {
        // 获取全局实例可能需要的数据
        const app = getApp();
        
        // 只有当来源是结算页时才返回结算页
        if (this.data.fromCheckout && this.data.source === 'checkout') {
            // 获取订单参数，如果this.data.orderParams为空，尝试从storage获
            // 构建跳转URL
            
            let url = '/pages/shopping/checkout/checkout';
            
            // 清除当前页面的相关数据
            this.setData({
                orderParams: null,
                fromCheckout: false,
                source: ''
            });
            
            // 返回订单确认页面
            wx.navigateTo({
              url: url,
              fail: (navigateErr) => {
                  wx.showToast({
                      title: '返回订单页面失败',
                      icon: 'none'
                  });
              }
          });
            
            return true;
        }
        
        return false;
    },
    // 测试示例地址解析
    testSampleAddress() {
        // 测试一个标准的地址样本
        const sampleAddress = '张三 13812345678 北京市朝阳区建国路88号 SOHO现代城 5号楼 1102室';
        
        wx.showModal({
            title: '测试示例',
            content: '将解析示例地址：\n' + sampleAddress,
            success: (res) => {
                if (res.confirm) {
                    // 显示加载中
                    wx.showLoading({
                        title: '解析中...',
                        mask: true
                    });
                    
                    // 延迟一点执行以展示加载效果
                    setTimeout(() => {
                        // 使用地址解析
                        this.parseAddressText(sampleAddress);
                        wx.hideLoading();
                    }, 800);
                }
            }
        });
    }
});