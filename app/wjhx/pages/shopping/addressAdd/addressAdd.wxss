page {
    height: 100%;
    background: #f7f8fc;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.add-address {
    padding: 30rpx;
    box-sizing: border-box;
    min-height: 100%;
    background: #f7f8fc;
}

.form-card {
    background: #fff;
    width: 100%;
    height: auto;
    overflow: hidden;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.02);
}

.form-item {
    padding: 30rpx;
    display: flex;
    align-items: center;
    min-height: 42rpx;
}

.item-label {
    width: 180rpx;
    font-size: 28rpx;
    color: #333;
    font-weight: normal;
}

.input {
    flex: 1;
    height: 42rpx;
    line-height: 42rpx;
    font-size: 28rpx;
    color: #333;
}

input::placeholder {
    color: #c0c0c0;
    font-size: 28rpx;
}

.form-divider {
    height: 1rpx;
    background: #f2f2f2;
    margin: 0 30rpx;
}

.region-item {
    position: relative;
}

.region-input {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.region-placeholder {
    color: #c0c0c0;
    font-size: 28rpx;
}

.region-selected {
    color: #333;
    font-size: 28rpx;
}

.location-icon {
    width: 36rpx;
    height: 36rpx;
}

.map-select-btn {
    margin-left: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50rpx;
    height: 50rpx;
    background-color: rgba(255, 118, 117, 0.1);
    border-radius: 50%;
}

.map-icon {
    width: 30rpx;
    height: 30rpx;
}

/* Smart Fill Section */
.smart-fill-section {
    background: #fff;
    width: 100%;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.02);
    box-sizing: border-box;
    height: auto;
}

.smart-fill-title {
    font-size: 28rpx;
    font-weight: normal;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
}

.smart-fill-content {
    background-color: #f9f9f9;
    border-radius: 10rpx;
    padding: 30rpx;
    position: relative;
}

.smart-fill-hint {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
    display: block;
    margin-bottom: 20rpx;
}

.smart-fill-example {
    background-color: #f0f0f0;
    border-radius: 8rpx;
    padding: 16rpx;
    margin-bottom: 30rpx;
}

.example-text {
    font-size: 24rpx;
    color: #888;
    line-height: 1.4;
    display: block;
}

.smart-fill-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;
    flex-wrap: wrap;
}

.paste-btn, .scan-btn, .test-btn {
    height: 70rpx;
    line-height: 70rpx;
    font-size: 26rpx;
    border-radius: 35rpx;
    margin: 0;
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    max-width: 40%;
    min-width: 160rpx;
}

.paste-btn {
    color: #ffffff;
    background: linear-gradient(to right, #a580ff, #8c65fc);
    box-shadow: 0 2rpx 8rpx rgba(165, 128, 255, 0.3);
    margin-right: 10rpx;
    border: none;
}

.scan-btn {
    color: #666;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    margin-right: 10rpx;
}

.test-btn {
    color: #fff;
    background: linear-gradient(to right, #ff9a9e, #fad0c4);
    box-shadow: 0 2rpx 8rpx rgba(255, 154, 158, 0.3);
    border: none;
}

.action-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 10rpx;
}

/* Default Address Toggle */
.default-address-section {
    background: #fff;
    width: 100%;
    border-radius: 12rpx;
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 80rpx;
    box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.02);
    box-sizing: border-box;
    position: relative;
    z-index: 1;
    overflow: hidden;
    will-change: transform;
}

.default-address-section::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    z-index: -1;
}

.default-label {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

/* Custom switch style to improve visual stability */
.default-address-section switch {
    zoom: 0.8;
    transform: translateZ(0);
    will-change: transform;
}

/* Save Button */
.btn-save-container {
    padding: 0 30rpx;
    position: fixed;
    bottom: 40rpx;
    left: 0;
    width: 100%;
    box-sizing: border-box;
}

.btn-save {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 30rpx;
    color: #fff;
    background: #cccccc;
    border-radius: 10rpx;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-save.btn-active {
    background: linear-gradient(to right, #a580ff, #8165fc);
    box-shadow: 0 2rpx 12rpx rgba(165, 128, 255, 0.3);
    transform: translateY(-2rpx);
}

/* Add a subtle hover effect if supported */
@media (hover: hover) {
    .btn-save.btn-active:hover {
        opacity: 0.9;
        transform: translateY(-4rpx);
    }
}

/* Region Selector Styles */
.region-select {
    width: 100%;
    height: 600rpx;
    background: #fff;
    position: fixed;
    z-index: 999;
    left: 0;
    bottom: 0;
    border-radius: 20rpx 20rpx 0 0;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
    will-change: transform;
}

.region-select.show {
    transform: translateY(0);
}

.region-select .select-header {
    height: 108rpx;
    width: 100%;
    border-bottom: 1px solid #f4f4f4;
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
}

.region-select .region-selected {
    display: flex;
    height: 60rpx;
}

.region-select .region-selected .item {
    max-width: 160rpx;
    margin-right: 30rpx;
    text-align: center;
    line-height: 60rpx;
    height: 100%;
    color: #333;
    font-size: 28rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
}

.region-select .region-selected .item.disabled {
    color: #999;
}

.region-select .region-selected .item.selected {
    color: #a580ff;
    font-weight: 500;
}

.region-select .region-selected .item.selected:after {
    content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 40rpx;
    height: 4rpx;
    margin-left: -20rpx;
    background: #a580ff;
    border-radius: 2rpx;
}

.region-select .done-button {
    height: 60rpx;
    padding: 0 30rpx;
    line-height: 60rpx;
    text-align: center;
    color: #ffffff;
    font-size: 28rpx;
    font-weight: 500;
    background-color: #a580ff;
    border-radius: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(165, 128, 255, 0.3);
}

.region-select .done-button.disabled {
    color: #999;
    background-color: #f5f5f5;
    box-shadow: none;
}

.region-select .select-body {
    height: 492rpx;
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
}

.region-select .region-list {
    height: 100%;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 40rpx; /* Add padding to ensure the last items are visible */
}

.region-select .region-list .item {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    text-align: left;
    color: #333;
    font-size: 28rpx;
    border-bottom: 1px solid #f7f7f7;
}

.region-select .region-list .item.selected {
    color: #a580ff;
    font-weight: 500;
}

.bg-mask {
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.4);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
    will-change: opacity;
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
}

.bg-mask.show {
    opacity: 1;
    visibility: visible;
}
