page {
    height: 100%;
    width: 100%;
    background: #f8f9fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.address-list {
    flex: 1;
    padding: 0 24rpx;
    background: transparent;
    margin-bottom: 140rpx;
    overflow-y: auto;
}

.address-list .item {
    min-height: 160rpx;
    height: auto;
    display: flex;
    align-items: flex-start;
    background: #ffffff;
    padding: 32rpx 28rpx;
    margin-bottom: 16rpx;
    border-radius: 16rpx;
    position: relative;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    transition: all 200ms ease-in-out;
    transform: scale(1);
    border: 2rpx solid transparent;
}

.address-list .item:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.address-list .item.managing {
    border-color: rgba(66, 165, 245, 0.2);
    background: rgba(66, 165, 245, 0.02);
}

.address-list .item:last-child {
    margin-bottom: 16rpx;
}

.address-list .l {
    width: 140rpx;
    height: auto;
    overflow: hidden;
    margin-right: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.address-list .name {
    width: 100%;
    height: auto;
    font-size: 34rpx;
    color: #1a1a1a;
    margin-bottom: 12rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-weight: 600;
    line-height: 1.2;
}

.address-list .default {
    width: fit-content;
    min-width: 60rpx;
    height: 36rpx;
    line-height: 34rpx;
    text-align: center;
    font-size: 22rpx;
    color: #ffffff;
    background: linear-gradient(135deg, #42A5F5, #1976D2);
    border: none;
    border-radius: 18rpx;
    padding: 0 12rpx;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(66, 165, 245, 0.3);
}

.address-list .c {
    flex: 1;
    height: auto;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 4rpx;
}

.address-list .mobile {
    height: auto;
    font-size: 32rpx;
    line-height: 1.3;
    overflow: hidden;
    color: #2c2c2c;
    margin-bottom: 12rpx;
    font-weight: 500;
    letter-spacing: 0.5rpx;
}

.address-list .address {
    min-height: 40rpx;
    height: auto;
    max-height: 84rpx;
    font-size: 28rpx;
    line-height: 42rpx;
    color: #666666;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    opacity: 0.9;
}

.address-list .r {
    width: 64rpx;
    height: 64rpx;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin-left: 20rpx;
    padding-top: 4rpx;
}

.address-list .item.managing .r {
    width: 140rpx;
    height: auto;
    overflow: visible;
    align-items: stretch;
    padding-top: 0;
}

.address-list .del {
    display: block;
    width: 52rpx;
    height: 52rpx;
    transition: all 200ms ease-in-out;
    opacity: 0.7;
}

.address-list .del:active {
    transform: scale(0.9);
    opacity: 1;
}

.shipping-tag {
    position: absolute;
    top: 30rpx;
    left: 30rpx;
    font-size: 24rpx;
    color: #42A5F5;
    background-color: rgba(26, 250, 41, 0.1);
    padding: 4rpx 12rpx;
    border-radius: 6rpx;
    z-index: 2;
}

/* Add padding to the left section when shipping tag is present */
.shipping-tag + .l {
    margin-top: 36rpx;
}

.add-address {
    background: linear-gradient(135deg, #42A5F5, #1976D2);
    text-align: center;
    width: calc(100% - 48rpx);
    height: 96rpx;
    line-height: 96rpx;
    position: fixed;
    border-radius: 48rpx;
    border: none;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
    bottom: 32rpx;
    left: 24rpx;
    right: 24rpx;
    box-shadow: 0 8rpx 24rpx rgba(66, 165, 245, 0.4);
    transition: all 200ms ease-in-out;
    transform: scale(1);
    letter-spacing: 1rpx;
}

.add-address:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(66, 165, 245, 0.6);
    background: linear-gradient(135deg, #1976D2, #0D47A1);
}

.empty-view {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 40rpx;
    opacity: 0.8;
}

.empty-view .icon {
    height: 200rpx;
    width: 200rpx;
    margin-bottom: 32rpx;
    opacity: 0.6;
    transition: all 200ms ease-in-out;
}

.empty-view .text {
    width: auto;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #999999;
    text-align: center;
    opacity: 0.8;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 24rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
    position: sticky;
    top: 0;
    z-index: 10;
}

.page-title {
    font-size: 36rpx;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: 0.5rpx;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.manage-btn, .add-btn {
    color: #42A5F5;
    font-size: 28rpx;
    font-weight: 500;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    transition: all 200ms ease-in-out;
    background: rgba(66, 165, 245, 0.08);
}

.manage-btn:active, .add-btn:active {
    background: rgba(66, 165, 245, 0.15);
    transform: scale(0.95);
}

.address-content {
    display: flex;
    flex: 1;
    align-items: flex-start;
}

.sub-text {
    font-size: 24rpx;
    color: #cccccc;
    margin-top: 12rpx;
    text-align: center;
    opacity: 0.8;
}

.add-text {
    font-size: 32rpx;
    font-weight: 600;
    letter-spacing: 1rpx;
}

.address-list .action-btns {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    height: 100%;
    width: 100%;
    gap: 8rpx;
}

.address-list .action-btn {
    font-size: 22rpx;
    padding: 10rpx 12rpx;
    border-radius: 12rpx;
    text-align: center;
    width: 100%;
    white-space: nowrap;
    font-weight: 500;
    transition: all 200ms ease-in-out;
    transform: scale(1);
    letter-spacing: 0.5rpx;
}

.address-list .action-btn:active {
    transform: scale(0.95);
}

.address-list .action-btn.edit {
    color: #1976D2;
    background-color: rgba(25, 118, 210, 0.12);
}

.address-list .action-btn.edit:active {
    background-color: rgba(25, 118, 210, 0.2);
}

.address-list .action-btn.delete {
    color: #D32F2F;
    background-color: rgba(211, 47, 47, 0.12);
}

.address-list .action-btn.delete:active {
    background-color: rgba(211, 47, 47, 0.2);
}

.address-list .action-btn.shipping {
    color: #388E3C;
    background-color: rgba(56, 142, 60, 0.12);
}

.address-list .action-btn.shipping:active {
    background-color: rgba(56, 142, 60, 0.2);
}
