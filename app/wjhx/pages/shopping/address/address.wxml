<view class="container">
    <view class="page-header">
        <view class="page-title">收货地址</view>
        <view class="header-actions">
            <view class="manage-btn" bindtap="manageAddress">{{isManaging ? '完成' : '管理'}}</view>
            <view class="add-btn" bindtap="addressAddOrUpdate" data-address-id="0">新增地址</view>
        </view>
    </view>

    <view class="address-list" wx:if="{{ addressList.length > 0 }}">
        <view class="item {{isManaging ? 'managing' : ''}}" wx:for="{{addressList}}" wx:key="{{item.id}}" bindtap="{{isManaging ? '' : 'selectAddress'}}" data-address-id="{{item.id}}">
            <view class="address-content">
                <view class="l">
                    <view class="name">{{item.name}}</view>
                    <view class="default" wx:if="{{item.isDefault}}">默认</view>
                </view>
                <view class="c">
                    <view class="mobile">{{item.mobile}}</view>
                    <view class="address">{{item.fullRegion + item.address}}</view>
                </view>
            </view>
            <view class="r">
                <image wx:if="{{!isManaging}}" catchtap="addressAddOrUpdate" data-address-id="{{item.id}}" class="del" src="http://yanxuan.nosdn.127.net/hxm/yanxuan-wap/p/20161201/style/img/icon-normal/address-edit-7fee7b0d63.png"></image>
                <block wx:if="{{isManaging}}">
                    <view class="action-btns">
                        <view class="action-btn edit" catchtap="addressAddOrUpdate" data-address-id="{{item.id}}">编辑</view>
                        <view class="action-btn delete" catchtap="deleteAddress" data-address-id="{{item.id}}">删除</view>
                        <view class="action-btn shipping" catchtap="setDefaultShipping" data-address-id="{{item.id}}" wx:if="{{!item.isDefault}}">设为默认</view>
                    </view>
                </block>
            </view>
        </view>
    </view>

    <view class="empty-view" wx:if="{{ addressList.length <= 0 }}">
        <image class="icon" src="http://yanxuan.nosdn.127.net/hxm/yanxuan-wap/p/20161201/style/img/icon-normal/noAddress-26d570cefa.png"></image>
        <text class="text">暂无收货地址</text>
        <text class="sub-text">添加地址后可快速选择收货信息</text>
    </view>

    <view class="add-address" bindtap="addressAddOrUpdate" data-address-id="0">
        <text class="add-text">添加新地址</text>
    </view>
</view>
