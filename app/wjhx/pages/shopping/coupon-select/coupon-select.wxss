/* ===== 页面基础样式 ===== */
page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    color: #333333;
}

.page-wrapper {
    width: 100%;
    min-height: 100vh;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f8f9fa 30%);
    padding-bottom: 120rpx;
}

/* ===== 顶部区域样式 ===== */
.header-section {
    position: relative;
    padding: 60rpx 30rpx 40rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

.page-title {
    text-align: center;
    position: relative;
    z-index: 2;
}

.title-text {
    display: block;
    font-size: 48rpx;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8rpx;
    letter-spacing: 2rpx;
}

.title-subtitle {
    display: block;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
    letter-spacing: 1rpx;
}

/* ===== 主要内容区域 ===== */
.main-content {
    flex: 1;
    padding: 24rpx 20rpx 40rpx;
    background: #f8f9fa;
    border-radius: 32rpx 32rpx 0 0;
    margin-top: -32rpx;
    position: relative;
    z-index: 8;
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
}

/* ===== 分组标题 ===== */
.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin: 32rpx 0 16rpx;
    padding-left: 16rpx;
    border-left: 6rpx solid #667eea;
}

/* ===== 优惠券项目样式 ===== */
.coupon-item {
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    border: 2rpx solid transparent;
    transition: all 200ms ease-in-out;
}

.coupon-item:active {
    transform: scale(0.98);
}

.coupon-item.selected {
    border-color: #667eea;
    box-shadow: 0 6rpx 30rpx rgba(102, 126, 234, 0.2);
}

.coupon-item.disabled {
    opacity: 0.6;
    background: #f5f5f5;
}

.coupon-content {
    display: flex;
    align-items: center;
    padding: 24rpx;
}

/* ===== 优惠券左侧金额区域 ===== */
.coupon-left {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    height: 80rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.coupon-amount {
    font-size: 28rpx;
    font-weight: 700;
    color: #ffffff;
    line-height: 1;
}

.coupon-type {
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 4rpx;
}

/* ===== 优惠券信息区域 ===== */
.coupon-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.coupon-name {
    font-size: 30rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 8rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.coupon-desc {
    font-size: 24rpx;
    color: #666666;
    margin-bottom: 4rpx;
}

.coupon-expire {
    font-size: 22rpx;
    color: #999999;
}

/* ===== 选择状态 ===== */
.coupon-check {
    flex-shrink: 0;
    margin-left: 16rpx;
}

.check-icon {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid #cccccc;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: transparent;
    transition: all 200ms ease-in-out;
}

.check-icon.checked {
    background: #667eea;
    border-color: #667eea;
    color: #ffffff;
}

/* ===== 不可用原因 ===== */
.coupon-disabled-reason {
    flex-shrink: 0;
    margin-left: 16rpx;
}

.reason-text {
    font-size: 22rpx;
    color: #999999;
    background: rgba(153, 153, 153, 0.1);
    padding: 8rpx 12rpx;
    border-radius: 12rpx;
}

/* ===== 空状态 ===== */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;
}

.empty-icon {
    font-size: 120rpx;
    margin-bottom: 24rpx;
    opacity: 0.6;
}

.empty-text {
    font-size: 28rpx;
    color: #999999;
}

/* ===== 底部确认按钮 ===== */
.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 20rpx 24rpx;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    border-top: 1rpx solid #f0f0f0;
    z-index: 1000;
}

.confirm-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #ffffff;
    padding: 20rpx 40rpx;
    border-radius: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
    transition: all 200ms ease-in-out;
}

.confirm-btn:active {
    transform: scale(0.95);
    box-shadow: 0 3rpx 10rpx rgba(102, 126, 234, 0.5);
}

.btn-text {
    letter-spacing: 2rpx;
}

/* ===== 响应式设计 ===== */
@media screen and (max-width: 750rpx) {
    .header-section {
        padding: 50rpx 20rpx 30rpx;
    }

    .title-text {
        font-size: 42rpx;
    }

    .main-content {
        padding: 20rpx 16rpx 32rpx;
    }

    .coupon-content {
        padding: 20rpx;
    }

    .coupon-left {
        width: 100rpx;
        height: 70rpx;
    }

    .coupon-amount {
        font-size: 26rpx;
    }

    .coupon-name {
        font-size: 28rpx;
    }

    .bottom-bar {
        padding: 16rpx 20rpx;
    }

    .confirm-btn {
        padding: 16rpx 32rpx;
        font-size: 28rpx;
    }
}