var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');

Page({
  data: {
    availableCoupons: [],
    unavailableCoupons: [],
    selectedCouponId: null,
    selectedCoupon: null,
    orderAmount: 0
  },

  onLoad: function (options) {
    const orderAmount = parseFloat(options.orderAmount) || 0;
    const currentCouponId = parseInt(options.couponId) || null;
    
    this.setData({
      orderAmount: orderAmount,
      selectedCouponId: currentCouponId
    });

    this.loadCoupons();
  },

  // 加载优惠券列表
  loadCoupons: function () {
    const that = this;
    wx.showLoading({
      title: '加载中...',
    });

    util.request(api.CouponList, {
      status: 'available',
      page: 1,
      size: 100
    }).then(function (res) {
      wx.hideLoading();
      if (res.success && res.data) {
        const allCoupons = res.data;
        const availableCoupons = [];
        const unavailableCoupons = [];

        allCoupons.forEach(coupon => {
          // 确保优惠券有name字段
          if (!coupon.name && coupon.title) {
            coupon.name = coupon.title;
          }

          // 确保ID是数字类型
          coupon.id = parseInt(coupon.id);

          // 解析时间字符串，确保有效期显示正确
          that.formatCouponTime(coupon);

          if (coupon.minAmount <= that.data.orderAmount) {
            availableCoupons.push(coupon);
          } else {
            unavailableCoupons.push(coupon);
          }
        });
        
        console.log('加载的优惠券数据:', {
          availableCoupons: availableCoupons,
          unavailableCoupons: unavailableCoupons
        });

        that.setData({
          availableCoupons: availableCoupons,
          unavailableCoupons: unavailableCoupons
        });

        // 如果当前选中的优惠券存在，设置选中状态
        if (that.data.selectedCouponId) {
          const selectedCoupon = availableCoupons.find(c => c.id === that.data.selectedCouponId);
          if (selectedCoupon) {
            that.setData({
              selectedCoupon: selectedCoupon
            });
          }
        }
      }
    }).catch(function (error) {
      wx.hideLoading();
      util.showErrorToast('加载优惠券失败');
      console.error('Failed to load coupons:', error);
    });
  },

  // 选择不使用优惠券
  selectNoCoupon: function () {
    this.setData({
      selectedCouponId: null,
      selectedCoupon: null
    });
  },

  // 选择优惠券
  selectCoupon: function (e) {
    const coupon = e.currentTarget.dataset.coupon;
    console.log('选择优惠券:', coupon);
    
    // 验证优惠券是否满足使用条件
    if (coupon.minAmount > this.data.orderAmount) {
      wx.showToast({
        title: `需满${coupon.minAmount}元才能使用`,
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      selectedCouponId: coupon.id,
      selectedCoupon: coupon
    });
  },

  // 确认选择
  confirmSelection: function () {
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];

    console.log('确认选择优惠券:', {
      selectedCouponId: this.data.selectedCouponId,
      selectedCoupon: this.data.selectedCoupon
    });

    // 保存优惠券ID到storage，确保数据持久化
    const couponId = this.data.selectedCouponId ? parseInt(this.data.selectedCouponId) : 0;
    if (couponId > 0) {
      wx.setStorageSync('couponId', couponId);
      console.log('保存优惠券ID到storage:', couponId);
    } else {
      wx.removeStorageSync('couponId');
      console.log('清除storage中的优惠券ID');
    }

    if (prevPage) {
      // 设置标志，避免checkout页面重新加载数据
      wx.setStorageSync('skipCheckoutReload', true);
      
      // 更新上一页的优惠券选择
      const updateData = {
        couponId: couponId
      };
      
      // 如果选择了优惠券，设置优惠券信息
      if (this.data.selectedCoupon) {
        updateData.checkedCoupon = {
          id: this.data.selectedCoupon.id,
          name: this.data.selectedCoupon.name || this.data.selectedCoupon.title,
          title: this.data.selectedCoupon.title,
          amount: this.data.selectedCoupon.amount,
          minAmount: this.data.selectedCoupon.minAmount
        };
        console.log('设置选中的优惠券:', updateData.checkedCoupon);
      } else {
        updateData.checkedCoupon = null;
        console.log('清除优惠券选择');
      }
      
      prevPage.setData(updateData);
      console.log('更新checkout页面数据:', updateData);
      
      // 等待数据更新完成后再重新计算价格
      setTimeout(() => {
        if (prevPage.recalculatePrice) {
          console.log('调用重新计算价格方法，当前couponId:', prevPage.data.couponId);
          prevPage.recalculatePrice();
        }
      }, 100);
    }

    wx.navigateBack();
  },

  /**
   * 格式化优惠券时间显示
   */
  formatCouponTime: function (coupon) {
    // 解析时间字符串
    let startTimeStr = '';
    let endTimeStr = '';

    if (coupon.remark && coupon.remark.includes(',')) {
      const times = coupon.remark.split(',');
      startTimeStr = times[0] || '';
      endTimeStr = times[1] || '';
    } else if (coupon.remark) {
      // 如果没有逗号，可能只有结束时间
      endTimeStr = coupon.remark;
    } else {
      // 如果没有remark字段，尝试从原始时间字段格式化
      startTimeStr = coupon.startTime ? coupon.startTime : '';
      endTimeStr = coupon.endTime ? coupon.endTime : '';
    }

    // 使用简洁的时间格式
    if (startTimeStr && endTimeStr) {
      const startFormatted = this.formatDate(startTimeStr);
      const endFormatted = this.formatDate(endTimeStr);
      coupon.remark = `${startFormatted}-${endFormatted}`;
    } else if (endTimeStr) {
      coupon.remark = `至${this.formatDate(endTimeStr)}`;
    } else {
      coupon.remark = '永久有效';
    }
  },

  /**
   * 格式化日期 - 使用简洁格式
   */
  formatDate: function (dateStr) {
    if (!dateStr) return '';

    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;

      const now = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      // 如果是当年，只显示月/日
      if (year === now.getFullYear()) {
        return `${month}/${day}`;
      } else {
        // 如果是其他年份，显示年/月/日
        return `${year}/${month}/${day}`;
      }
    } catch (e) {
      console.error('日期格式化失败:', e);
      return dateStr;
    }
  }
});