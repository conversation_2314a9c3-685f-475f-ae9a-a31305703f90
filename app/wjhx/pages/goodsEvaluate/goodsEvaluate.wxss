/* 商品评价页面样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

/* 积分奖励提示 */
.reward-tip {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.tip-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.tip-content {
  flex: 1;
}

.tip-title {
  display: block;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.tip-desc {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
}

/* 商品信息 */
.goods-info {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.goods-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.goods-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.goods-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-price {
  font-size: 32rpx;
  color: #ff6b6b;
  font-weight: bold;
}

/* 评价表单 */
.evaluate-form {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 24rpx;
}

/* 星级评分 */
.rating-section {
  margin-bottom: 40rpx;
}

.star-rating {
  display: flex;
  align-items: center;
}

.star {
  font-size: 48rpx;
  color: #ddd;
  margin-right: 8rpx;
  transition: color 0.2s ease;
}

.star.active {
  color: #ffb400;
}

/* 评价内容 */
.comment-section {
  margin-bottom: 40rpx;
  position: relative;
}

.comment-input {
  width: 100%;
  min-height: 200rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  box-sizing: border-box;
}

.comment-input:focus {
  border-color: #ff6b6b;
}

.char-count {
  position: absolute;
  bottom: -30rpx;
  right: 0;
  font-size: 24rpx;
  color: #999;
}

/* 图片上传 */
.image-section {
  margin-bottom: 40rpx;
}

.image-upload {
  width: 100%;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 2rpx solid #e5e5e5;
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff4757;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.add-image {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
}

.image-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
  line-height: 1.4;
}

/* 提交按钮 */
.submit-section {
  margin-top: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.submit-btn:disabled {
  background: #ccc;
  box-shadow: none;
}

.submit-btn:not(:disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}
