<!-- 商品评价页面 -->
<view class="container">
  <!-- 积分奖励提示 -->
  <view class="reward-tip">
    <view class="tip-icon">🎁</view>
    <view class="tip-content">
      <text class="tip-title">评价有奖</text>
      <text class="tip-desc">16字好评+配图送100积分</text>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="goods-info" wx:if="{{goodsInfo}}">
    <image class="goods-image" src="{{goodsInfo.listPicUrl}}" mode="aspectFill"></image>
    <view class="goods-detail">
      <text class="goods-name">{{goodsInfo.goodsName}}</text>
      <text class="goods-spec" wx:if="{{goodsInfo.specifications}}">{{goodsInfo.specifications}}</text>
      <text class="goods-price">¥{{goodsInfo.retailPrice}}</text>
    </view>
  </view>

  <!-- 评价表单 -->
  <view class="evaluate-form">
    <!-- 星级评分 -->
    <view class="rating-section">
      <text class="section-title">商品评分</text>
      <view class="star-rating">
        <view 
          class="star {{index < rating ? 'active' : ''}}" 
          wx:for="{{5}}" 
          wx:key="index"
          bindtap="setRating"
          data-rating="{{index + 1}}"
        >
          ★
        </view>
      </view>
    </view>

    <!-- 文字评价 -->
    <view class="comment-section">
      <text class="section-title">评价内容</text>
      <textarea 
        class="comment-input"
        placeholder="请输入您的评价，16字以上好评可获得100积分奖励"
        maxlength="500"
        bindinput="onCommentInput"
        value="{{comment}}"
      ></textarea>
      <view class="char-count">{{comment.length}}/500</view>
    </view>

    <!-- 图片上传 -->
    <view class="image-section">
      <text class="section-title">上传图片</text>
      <view class="image-upload">
        <view class="image-list">
          <view class="image-item" wx:for="{{imageList}}" wx:key="index">
            <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
            <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">×</view>
          </view>
          <view class="add-image" wx:if="{{imageList.length < 9}}" bindtap="chooseImage">
            <text class="add-icon">+</text>
            <text class="add-text">添加图片</text>
          </view>
        </view>
        <text class="image-tip">最多上传9张图片，配图评价可获得额外积分奖励</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" bindtap="submitEvaluate" disabled="{{submitting}}">
        {{submitting ? '提交中...' : '提交评价'}}
      </button>
    </view>
  </view>
</view>
