var app = getApp();
var util = require('../../utils/util.js');
var api = require('../../config/api.js');

Page({
  data: {
    orderId: 0,
    goodsId: 0,
    goodsInfo: null,
    rating: 5, // 默认5星
    comment: '',
    imageList: [],
    submitting: false
  },

  onLoad: function (options) {
    this.setData({
      orderId: options.orderId || 0,
      goodsId: options.goodsId || 0
    });
    
    if (options.goodsInfo) {
      try {
        const goodsInfo = JSON.parse(decodeURIComponent(options.goodsInfo));
        this.setData({
          goodsInfo: goodsInfo
        });
      } catch (e) {
        console.error('解析商品信息失败:', e);
      }
    }
  },

  // 设置星级评分
  setRating: function(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({
      rating: rating
    });
  },

  // 评价内容输入
  onCommentInput: function(e) {
    this.setData({
      comment: e.detail.value
    });
  },

  // 选择图片
  chooseImage: function() {
    const that = this;
    const maxCount = 9 - this.data.imageList.length;
    
    wx.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        const tempFilePaths = res.tempFilePaths;
        const newImageList = that.data.imageList.concat(tempFilePaths);
        that.setData({
          imageList: newImageList
        });
      },
      fail: function(err) {
        console.error('选择图片失败:', err);
        util.showErrorToast('选择图片失败');
      }
    });
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.imageList[index],
      urls: this.data.imageList
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const imageList = this.data.imageList;
    imageList.splice(index, 1);
    this.setData({
      imageList: imageList
    });
  },

  // 上传图片到服务器
  uploadImages: function() {
    const that = this;
    const imageList = this.data.imageList;
    
    if (imageList.length === 0) {
      return Promise.resolve([]);
    }

    const uploadPromises = imageList.map(imagePath => {
      return new Promise((resolve, reject) => {
        wx.uploadFile({
          url: api.FileUpload,
          filePath: imagePath,
          name: 'file',
          header: {
            'X-Wjhx-Token': wx.getStorageSync('token') || ''
          },
          success: function(res) {
            try {
              const data = JSON.parse(res.data);
              if (data.success || data.errno === 0) {
                resolve(data.data.url || data.data);
              } else {
                reject(new Error(data.errmsg || data.message || '上传失败'));
              }
            } catch (e) {
              reject(new Error('上传响应解析失败'));
            }
          },
          fail: function(err) {
            reject(err);
          }
        });
      });
    });

    return Promise.all(uploadPromises);
  },

  // 提交评价
  submitEvaluate: function() {
    const that = this;
    
    // 验证评价内容
    if (!this.data.comment.trim()) {
      util.showErrorToast('请输入评价内容');
      return;
    }

    if (this.data.comment.trim().length < 5) {
      util.showErrorToast('评价内容至少5个字符');
      return;
    }

    this.setData({
      submitting: true
    });

    wx.showLoading({
      title: '提交中...'
    });

    // 先上传图片
    this.uploadImages().then(function(imageUrls) {
      // 检查是否符合奖励条件
      const isQualifiedForReward = that.data.comment.trim().length >= 16 && imageUrls.length > 0;
      
      // 提交评价
      return util.request(api.CommentPost, {
        orderId: that.data.orderId,
        goodsId: that.data.goodsId,
        rating: that.data.rating,
        content: that.data.comment.trim(),
        picList: imageUrls,
        typeId: 0, // 商品评价类型
        valueId: that.data.goodsId
      }, 'POST');
    }).then(function(res) {
      wx.hideLoading();

      if (res.success || res.errno === 0) {
        // 检查是否获得积分奖励
        const isQualifiedForReward = that.data.comment.trim().length >= 16 && that.data.imageList.length > 0;

        if (isQualifiedForReward) {
          wx.showModal({
            title: '评价成功',
            content: '感谢您的评价！您已获得100积分奖励，请到个人中心查看。',
            showCancel: false,
            confirmText: '知道了',
            success: function() {
              that.goBack();
            }
          });
        } else {
          wx.showToast({
            title: '评价成功',
            icon: 'success',
            duration: 2000,
            success: function() {
              setTimeout(() => {
                that.goBack();
              }, 2000);
            }
          });
        }
      } else {
        util.showErrorToast(res.errmsg || res.message || '评价失败');
      }
    }).catch(function(error) {
      wx.hideLoading();
      console.error('提交评价失败:', error);
      util.showErrorToast('提交失败，请重试');
    }).finally(function() {
      that.setData({
        submitting: false
      });
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  }
});
