var app = getApp();
var util = require('../../utils/util.js');
var api = require('../../config/api.js');

Page({
  data: {
    orderId: 0,
    goodsId: 0,
    goodsInfo: null,
    rating: 5, // 默认5星
    comment: '',
    imageList: [],
    submitting: false
  },

  onLoad: function (options) {
    this.setData({
      orderId: options.orderId || 0,
      goodsId: options.goodsId || 0
    });
    
    if (options.goodsInfo) {
      try {
        const goodsInfo = JSON.parse(decodeURIComponent(options.goodsInfo));
        this.setData({
          goodsInfo: goodsInfo
        });
      } catch (e) {
        console.error('解析商品信息失败:', e);
      }
    }
  },

  // 设置星级评分
  setRating: function(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({
      rating: rating
    });
  },

  // 评价内容输入
  onCommentInput: function(e) {
    this.setData({
      comment: e.detail.value
    });
  },

  // 选择图片
  chooseImage: function() {
    const that = this;
    const maxCount = 9 - this.data.imageList.length;
    
    wx.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        const tempFilePaths = res.tempFilePaths;
        const newImageList = that.data.imageList.concat(tempFilePaths);
        that.setData({
          imageList: newImageList
        });
      },
      fail: function(err) {
        console.error('选择图片失败:', err);
        util.showErrorToast('选择图片失败');
      }
    });
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.imageList[index],
      urls: this.data.imageList
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const imageList = this.data.imageList;
    imageList.splice(index, 1);
    this.setData({
      imageList: imageList
    });
  },

  // 上传图片到服务器
  uploadImages: function() {
    const that = this;
    const imageList = this.data.imageList;

    if (imageList.length === 0) {
      return Promise.resolve([]);
    }

    console.log('开始上传图片，数量:', imageList.length);

    const uploadPromises = imageList.map((imagePath, index) => {
      return new Promise((resolve, reject) => {
        console.log(`上传第${index + 1}张图片:`, imagePath);

        wx.uploadFile({
          url: api.FileUpload,
          filePath: imagePath,
          name: 'file',
          header: {
            'X-Weshop-Token': wx.getStorageSync('token') || ''
          },
          success: function(res) {
            console.log(`第${index + 1}张图片上传响应:`, res);

            try {
              const data = JSON.parse(res.data);
              console.log(`第${index + 1}张图片解析后数据:`, data);

              // 兼容多种响应格式
              if (data.success === true || data.errno === 0 || data.code === 0) {
                const imageUrl = data.data?.url || data.data?.path || data.data || data.url;
                if (imageUrl) {
                  console.log(`第${index + 1}张图片上传成功:`, imageUrl);
                  resolve(imageUrl);
                } else {
                  console.error(`第${index + 1}张图片上传失败: 响应中没有找到图片URL`);
                  reject(new Error('响应中没有找到图片URL'));
                }
              } else {
                const errorMsg = data.errmsg || data.message || data.msg || '上传失败';
                console.error(`第${index + 1}张图片上传失败:`, errorMsg);
                reject(new Error(errorMsg));
              }
            } catch (e) {
              console.error(`第${index + 1}张图片响应解析失败:`, e, '原始响应:', res.data);
              reject(new Error('上传响应解析失败: ' + e.message));
            }
          },
          fail: function(err) {
            console.error(`第${index + 1}张图片上传请求失败:`, err);
            reject(new Error('上传请求失败: ' + (err.errMsg || err.message || '网络错误')));
          }
        });
      });
    });

    return Promise.all(uploadPromises);
  },

  // 提交评价
  submitEvaluate: function() {
    const that = this;

    // 验证评价内容
    if (!this.data.comment.trim()) {
      util.showErrorToast('请输入评价内容');
      return;
    }

    if (this.data.comment.trim().length < 5) {
      util.showErrorToast('评价内容至少5个字符');
      return;
    }

    this.setData({
      submitting: true
    });

    wx.showLoading({
      title: '提交中...'
    });

    console.log('开始提交评价，图片数量:', this.data.imageList.length);

    // 暂时跳过图片上传，直接提交文字评价
    // TODO: 等文件上传API配置好后再启用图片上传功能

    const submitData = {
      orderId: that.data.orderId,
      goodsId: that.data.goodsId,
      rating: that.data.rating,
      content: that.data.comment.trim(),
      picList: [], // 暂时为空，等API配置好后再启用
      typeId: 0, // 商品评价类型
      valueId: that.data.goodsId
    };

    console.log('提交评价数据:', submitData);

    // 直接提交评价（暂时不上传图片）
    util.request(api.CommentPost, submitData, 'POST').then(function(res) {
      wx.hideLoading();
      console.log('评价提交响应:', res);

      if (res.success || res.errno === 0) {
        // 检查是否获得积分奖励（暂时只基于文字长度，等图片上传功能完善后再加上图片条件）
        const isQualifiedForReward = that.data.comment.trim().length >= 16;

        if (isQualifiedForReward) {
          wx.showModal({
            title: '评价成功',
            content: '感谢您的评价！您已获得100积分奖励，请到个人中心查看。\n\n注：图片上传功能正在完善中，敬请期待！',
            showCancel: false,
            confirmText: '知道了',
            success: function() {
              that.goBack();
            }
          });
        } else {
          wx.showToast({
            title: '评价成功',
            icon: 'success',
            duration: 2000,
            success: function() {
              setTimeout(() => {
                that.goBack();
              }, 2000);
            }
          });
        }
      } else {
        const errorMsg = res.errmsg || res.message || res.msg || '评价失败';
        console.error('评价提交失败:', errorMsg);
        util.showErrorToast(errorMsg);
      }
    }).catch(function(error) {
      wx.hideLoading();
      console.error('提交评价失败:', error);

      let errorMsg = '提交失败，请重试';
      if (error && error.message) {
        errorMsg = error.message;
      }

      util.showErrorToast(errorMsg);
    }).finally(function() {
      that.setData({
        submitting: false
      });
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  }
});
