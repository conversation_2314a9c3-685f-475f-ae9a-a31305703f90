/* 登录页面容器 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  font-family: PingFangSC-Light, helvetica, 'Heiti SC';
}

/* 背景装饰元素 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.bg-circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.bg-circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 主要内容区域 */
.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 40rpx 40rpx;
  position: relative;
  z-index: 2;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 80rpx;
  animation: slideInDown 0.8s ease-out;
}

.logo-container {
  margin-bottom: 40rpx;
}

.logo-icon {
  font-size: 120rpx;
  line-height: 1;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.main-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  line-height: 1.2;
  display: block;
}

.sub-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  line-height: 1.4;
  display: block;
}

/* 登录卡片 */
.login-card {
  width: 100%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.8s ease-out 0.2s both;
}

.card-content {
  padding: 60rpx 40rpx;
}

/* 授权信息区域 */
.auth-info {
  display: flex;
  align-items: center;
  margin-bottom: 60rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.auth-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.auth-text {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.auth-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.3;
  display: block;
}

.auth-desc {
  font-size: 24rpx;
  color: #666666;
  font-weight: 300;
  line-height: 1.4;
  display: block;
}

/* 按钮区域 */
.button-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 主要按钮样式 */
.btn-primary {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #07C160 0%, #06AD56 100%);
  border: none;
  border-radius: 48rpx;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.3);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding: 0;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:active::before {
  left: 100%;
}

.btn-primary-hover {
  transform: scale(0.98);
  box-shadow: 0 6rpx 20rpx rgba(7, 193, 96, 0.4);
}

/* 次要按钮样式 */
.btn-secondary {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 44rpx;
  color: #666666;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding: 0;
}

.btn-secondary-hover {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 按钮内容布局 */
.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  width: 100%;
  height: 100%;
}

.btn-icon {
  font-size: 32rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  font-size: inherit;
  font-weight: inherit;
  line-height: 1;
  display: flex;
  align-items: center;
}

/* 返回页面提示 */
.return-info {
  margin-top: 40rpx;
  text-align: center;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  animation: fadeIn 1s ease-out 0.4s both;
}

.return-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.4;
  display: block;
}

/* 底部提示 */
.footer-tips {
  margin-top: 60rpx;
  text-align: center;
  animation: fadeIn 1s ease-out 0.6s both;
}

.tips-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  line-height: 1.4;
  display: block;
}

/* 动画定义 */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .login-content {
    padding: 40rpx 30rpx 30rpx;
  }

  .header-section {
    margin-bottom: 60rpx;
  }

  .logo-icon {
    font-size: 100rpx;
  }

  .main-title {
    font-size: 42rpx;
  }

  .sub-title {
    font-size: 26rpx;
  }

  .card-content {
    padding: 50rpx 30rpx;
  }

  .auth-info {
    padding: 24rpx;
    margin-bottom: 50rpx;
  }

  .auth-icon {
    font-size: 42rpx;
    margin-right: 20rpx;
  }

  .auth-title {
    font-size: 28rpx;
  }

  .auth-desc {
    font-size: 22rpx;
  }

  .btn-primary {
    height: 88rpx;
    font-size: 30rpx;
  }

  .btn-secondary {
    height: 80rpx;
    font-size: 26rpx;
  }

  .btn-icon {
    font-size: 28rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(40, 44, 52, 0.95);
  }

  .auth-info {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .auth-title {
    color: #ffffff;
  }

  .auth-desc {
    color: rgba(255, 255, 255, 0.7);
  }

  .btn-secondary {
    color: #ffffff;
  }
}

/* 无障碍访问优化 */
.btn-primary:focus,
.btn-secondary:focus {
  outline: 4rpx solid rgba(7, 193, 96, 0.5);
  outline-offset: 4rpx;
}

/* 性能优化 */
.login-container,
.bg-circle,
.login-card,
.btn-primary,
.btn-secondary {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 新增样式：个人信息完善部分 */
.profile-section {
  margin-top: 20rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.profile-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

.profile-text {
  flex: 1;
}

.profile-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.profile-desc {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 头像选择部分 */
.avatar-section {
  margin-bottom: 0;
  padding: 15rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-label {
  display: block;
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 15rpx;
  font-weight: 600;
  text-align: center;
}

.avatar-wrapper {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto;
  border-radius: 50%;
  overflow: hidden;
  border: 3rpx solid #e0e0e0;
  background: #f5f5f5;
  padding: 0;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-wrapper::after {
  border: none;
}

.avatar-wrapper:hover {
  border-color: #667eea;
  transform: scale(1.05);
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.avatar-tip {
  color: #ffffff;
  font-size: 22rpx;
  text-align: center;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 昵称输入部分 */
.nickname-section {
  margin-bottom: 0;
  padding: 15rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.nickname-input {
  width: 100%;
  height: 70rpx;
  background: #ffffff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #333333;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.nickname-input:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  outline: none;
}

.nickname-input::placeholder {
  color: #999999;
}

/* 降级方案样式 */
.fallback-section {
  margin-top: 10rpx;
  text-align: center;
}

.fallback-button {
  background: #f5f5f5;
  color: #666666;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 22rpx;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.fallback-button:hover {
  background: #667eea;
  color: #ffffff;
}

.fallback-tip {
  display: block;
  font-size: 18rpx;
  color: #999999;
  margin-top: 8rpx;
  line-height: 1.3;
}

/* 按钮禁用状态 */
.btn-primary[disabled] {
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%) !important;
  color: #999999 !important;
  border-color: #e0e0e0 !important;
  transform: none !important;
  cursor: not-allowed;
  box-shadow: none !important;
}

.btn-primary[disabled]:hover {
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%) !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-section {
  animation: fadeInUp 0.6s ease-out;
}

.avatar-section {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.nickname-section {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

/* 焦点状态优化 */
.nickname-input:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  outline: none;
  transform: translateY(-2rpx);
}

/* 头像悬停效果优化 */
.avatar-wrapper:active {
  transform: scale(0.98);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #ffffff;
  padding: 40rpx;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .avatar-wrapper {
    width: 140rpx;
    height: 140rpx;
  }

  .profile-header {
    padding: 24rpx;
  }

  .profile-title {
    font-size: 28rpx;
  }

  .profile-desc {
    font-size: 24rpx;
  }
}
