<wxs src="../../utils/format.wxs" module="format" />
<wxs module="utils">
  function getImageUrl(url, index, current) {
    return url;
  }

  function getPrevIndex(current, length) {
    return (current - 1 + length) % length;
  }

  function getNextIndex(current, length) {
    return (current + 1) % length;
  }

  function getItemClass(index, currentIndex, itemsStatus, isChanging) {
    if (!itemsStatus) return 'inactive';

    var status = itemsStatus[index] || 'inactive';
    var classes = status;

    if (isChanging) {
      classes += ' changing';
    }

    return classes;
  }

  module.exports = {
    getImageUrl: getImageUrl,
    getPrevIndex: getPrevIndex,
    getNextIndex: getNextIndex,
    getItemClass: getItemClass
  };
</wxs>

<!-- 轮播容器 -->
<view class="carousel-container {{isChanging ? 'is-changing' : ''}}" style="height: {{height}};">
  <!-- 调试信息 -->
  <view class="debug-info" wx:if="{{false}}">
    <text>Items: {{items.length}}, Current: {{currentIndex}}</text>
  </view>

  <!-- 前一张图片预览 -->
  <view class="carousel-prev" wx:if="{{showSideImages && items.length > 1}}" catchtap="prev">
    <view class="side-image-container" wx:if="{{items[utils.getPrevIndex(currentIndex, items.length)]}}">
      <image
        src="{{format.formatImageUrl(items[utils.getPrevIndex(currentIndex, items.length)].imageUrl)}}"
        mode="aspectFill"
        class="side-image side-image-prev"
      ></image>
      <view class="side-image-name"></view>
    </view>
  </view>

  <!-- 主轮播区域 -->
  <view class="carousel-main">
    <block wx:for="{{items}}" wx:key="id" wx:for-index="index">
      <view
        class="carousel-item {{utils.getItemClass(index, currentIndex, itemsStatus, isChanging)}}"
        data-index="{{index}}"
        bindtap="onItemTap"
        bind:touchstart="onTouchStart"
        bind:touchmove="onTouchMove"
        bind:touchend="onTouchEnd"
      >
        <image
          src="{{format.formatImageUrl(item.imageUrl)}}"
          mode="aspectFill"
          class="carousel-image {{currentIndex === index ? 'current' : ''}}"
          lazy-load="{{true}}"
        ></image>

        <!-- 商品信息覆盖层 -->
        <!-- <view class="product-info-overlay {{currentIndex === index ? 'active' : ''}}">
          
          <view class="product-name">{{item.name}}</view>

          <view class="product-details" wx:if="{{item.price || item.brief}}">
            <text class="product-price" wx:if="{{item.price && item.price > 0}}">¥{{item.price}}</text>
            <text class="product-brief" wx:if="{{item.brief}}">{{item.brief}}</text>
          </view>
        </view> -->
      </view>
    </block>
  </view>

  <!-- 下一张图片预览 -->
  <view class="carousel-next" wx:if="{{showSideImages && items.length > 1}}" catchtap="next">
    <view class="side-image-container" wx:if="{{items[utils.getNextIndex(currentIndex, items.length)]}}">
      <image
        src="{{format.formatImageUrl(items[utils.getNextIndex(currentIndex, items.length)].imageUrl)}}"
        mode="aspectFill"
        class="side-image side-image-next"
      ></image>
      <view class="side-image-name"></view>
    </view>
  </view>

  <!-- 指示点 -->
  <view class="indicator-dots" wx:if="{{indicatorDots && items.length > 1}}">
    <block wx:for="{{items}}" wx:key="*this" wx:for-index="index">
      <view
        class="indicator-dot {{currentIndex === index ? 'active' : ''}}"
        style="background-color: {{currentIndex === index ? indicatorActiveColor : indicatorColor}}"
        data-index="{{index}}"
        catchtap="onIndicatorTap"
      ></view>
    </block>
  </view>

  <!-- 左右切换按钮 -->
  <view class="carousel-nav" wx:if="{{items.length > 1}}">
    <!-- <view class="nav-btn prev-btn" catchtap="prev">
      <view class="nav-arrow left"></view>
    </view>
    <view class="nav-btn next-btn" catchtap="next">
      <view class="nav-arrow right"></view>
    </view> -->
  </view>
</view>
