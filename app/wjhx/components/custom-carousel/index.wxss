/* components/custom-carousel/index.wxss */

/* 轮播容器 */
.carousel-container {
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center; /* 中心对齐 */
  align-items: center;
  overflow: visible; /* 改为visible以允许阴影溢出 */
  min-height: 400rpx; /* 确保最小高度 */
  margin-bottom: 20rpx; /* 添加底部间距 */
  z-index: 50000; /* 提高z-index确保在顶层 */
  transform: translateZ(0); /* 硬件加速 */
  will-change: transform; /* 优化性能 */
}

/* 前一张图的容器 */
.carousel-prev {
  position: absolute;
  left: -30rpx;
  width: 20%;
  height: 70%;
  z-index: 50001;
  overflow: visible;
  display: flex;
  justify-content: flex-start; /* 靠左对齐 */
  align-items: center;
  background-color: transparent;
  pointer-events: auto;
}

/* 下一张图的容器 */
.carousel-next {
  position: absolute;
  right: -30rpx;
  width: 20%;
  height: 70%;
  z-index: 50001;
  overflow: visible;
  display: flex;
  justify-content: flex-end; /* 靠右对齐 */
  align-items: center;
  background-color: transparent;
  pointer-events: auto;
}

/* 侧边图片容器 */
.side-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 侧边图片样式 */
.side-image {
  width: 100%;
  height: 80%;
  object-fit: cover;
  transition: all 0.3s ease;
  opacity: 0.8;
  filter: brightness(0.9);
}

.side-image-prev {
  border-radius: 0 50rpx 50rpx 0;
  margin-left: 0; /* 确保靠左边缘 */
  transform-origin: left center; /* 设置变形原点在左侧 */
}

.side-image-next {
  border-radius: 50rpx 0 0 50rpx;
  margin-right: 0; /* 确保靠右边缘 */
  transform-origin: right center; /* 设置变形原点在右侧 */
}

/* 侧边图片名称 */
.side-image-name {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20rpx;
  color: white;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90%;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5rpx);
}

/* 侧边图片悬停效果 */
.side-image:active {
  opacity: 1;
  filter: brightness(1);
  transform: scale(0.95);
}

.side-image-container:active .side-image-name {
  background: rgba(0, 0, 0, 0.9);
}

/* 主轮播区域 */
.carousel-main {
  width: 60%; /* 减少中间区域宽度 */
  height: 80%;
  position: relative;
  z-index: 50002;
  min-height: 400rpx; /* 确保最小高度 */
  padding: 0; /* 移除内边距 */
  overflow: hidden; /* 添加隐藏溢出，确保滑动效果在容器内 */
}

/* 轮播项 */
.carousel-item {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1.0); /* 使用更流畅的缓动函数 */
  opacity: 0;
  transform: translateX(100%); /* 默认从右侧进入 */
  pointer-events: none;
  min-height: 400rpx; /* 确保最小高度 */
  will-change: transform, opacity; /* 优化性能 */
}

/* 前一个项目 - 向左滑出 */
.carousel-item.prev {
  opacity: 0;
  transform: translateX(-100%); /* 向左滑出 */
  z-index: 50001;
}

/* 活动项 */
.carousel-item.active {
  opacity: 1;
  transform: translateX(0); /* 居中显示 */
  z-index: 50003;
  pointer-events: auto;
}

/* 下一个项目 - 向右等待 */
.carousel-item.next {
  opacity: 0;
  transform: translateX(100%); /* 向右等待 */
  z-index: 50001;
}

/* 非活动项 */
.carousel-item.inactive {
  opacity: 0;
  /* 不设置transform，因为inactive状态取决于它是prev还是next */
  visibility: hidden; /* 完全隐藏非活动项 */
}

/* 切换中的状态 */
.carousel-item.changing {
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1.0);
}

/* 轮播图片 */
.carousel-image {
  width: 100%;
  height: 100%;
  border-radius: 60rpx;
  object-fit: cover;
  transition: all 0.4s ease;
  min-height: 350rpx; /* 确保最小高度 */
  transform: translateZ(0); /* 硬件加速 */
  will-change: transform; /* 优化性能 */
  border: 8rpx solid white;
}

/* 当前显示的图片 */
.carousel-image.current {
  /* border: 10rpx solid white; */
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
}

/* 商品信息覆盖层 */
.product-info-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  border-radius: 0 0 52rpx 52rpx; /* 匹配图片的圆角 */
  padding: 40rpx 30rpx 30rpx;
  color: white;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.4s ease;
  pointer-events: none;
}

/* 激活状态的商品信息覆盖层 */
.product-info-overlay.active {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* 商品名称 */
.product-name {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1.3;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 商品详情容器 */
.product-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12rpx;
}

/* 商品价格 */
.product-price {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b35;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  background: rgba(255, 255, 255, 0.9);
  color: #ff6b35;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 商品简介 */
.product-brief {
  font-size: 24rpx;
  opacity: 0.9;
  flex: 1;
  text-align: right;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 指示点容器 */
.indicator-dots {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  z-index: 50010;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}

/* 指示点 */
.indicator-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin: 0 6rpx;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.5);
}

/* 活动指示点 */
.indicator-dot.active {
  width: 24rpx;
  height: 12rpx;
  border-radius: 6rpx;
  background-color: white;
}

/* 导航按钮 */
.carousel-nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 50004;
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
  opacity: 0.5;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px); /* 模糊背景效果 */
}

.nav-btn:active {
  opacity: 0.8;
  transform: translateY(-50%) scale(0.95);
  background-color: rgba(0, 0, 0, 0.4);
}

.prev-btn {
  left: 10rpx;
}

.next-btn {
  right: 10rpx;
}

.nav-arrow {
  width: 24rpx;
  height: 24rpx;
  border-top: 5rpx solid white;
  border-right: 5rpx solid white;
  transition: all 0.2s ease;
}

.nav-arrow.left {
  transform: rotate(-135deg);
}

.nav-arrow.right {
  transform: rotate(45deg);
}

/* 动画效果 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}
