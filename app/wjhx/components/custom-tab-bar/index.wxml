<cover-view class="tab-bar">
  <cover-view class="tab-bar-border"></cover-view>
  <cover-view wx:for="{{list}}" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <cover-image class="tab-icon {{item.isHighlighted ? 'highlighted-icon' : ''}}" src="{{selected === index ? item.selectedIconPath : item.iconPath}}" style="{{item.iconSize ? 'width:' + item.iconSize + 'rpx; height:' + item.iconSize + 'rpx;' : ''}}"></cover-image>
    <cover-view class="tab-text {{item.isHighlighted ? 'highlighted-text' : ''}}" style="{{item.fontSize ? 'font-size:' + item.fontSize + 'rpx;' : ''}}">{{item.text}}</cover-view>
  </cover-view>
</cover-view> 