<!--components/custom-navbar/index.wxml-->
<view class="custom-navbar" style="height: {{navbarHeight}}px; background: {{gradientBackground}}; opacity: {{opacity}};">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 导航栏内容 -->
  <view class="navbar-content" style="height: {{titleBarHeight}}px;">
    <!-- 左侧返回按钮区域 -->
    <view class="navbar-left" style="width: {{leftWidth}}px;">
      <view wx:if="{{showBack}}" class="back-button" bindtap="onBackTap">
        <text class="back-icon" style="color: {{textColor}};">‹</text>
        <text wx:if="{{showBackText}}" class="back-text" style="color: {{textColor}};">返回</text>
      </view>
    </view>
    
    <!-- 中间标题区域 -->
    <view class="navbar-center">
      <text class="navbar-title" style="color: {{textColor}};">{{title}}</text>
    </view>
    
    <!-- 右侧操作区域 -->
    <view class="navbar-right" style="width: {{rightWidth}}px;">
      <slot name="right"></slot>
    </view>
  </view>
</view>
