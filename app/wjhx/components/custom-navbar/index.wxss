/* components/custom-navbar/index.wxss */

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 9999;
  box-sizing: border-box;
  transition: opacity 0.3s ease;
}

.status-bar {
  width: 100%;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.navbar-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-button {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 50rpx;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  transition: background-color 0.3s ease;
}

.back-button:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.back-icon {
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
  margin-right: 8rpx;
}

.back-text {
  font-size: 28rpx;
  font-weight: 500;
}

.navbar-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.navbar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 渐变背景样式预设 */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-sunset {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-ocean {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-forest {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.gradient-dark {
  background: linear-gradient(135deg, #434343 0%, #000000 100%);
}

/* 透明样式 */
.transparent {
  background: transparent;
}

/* 半透明样式 */
.semi-transparent {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20rpx);
}
