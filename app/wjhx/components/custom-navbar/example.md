# 自定义导航栏使用示例

## 示例1：商品详情页（渐变背景）

### 页面配置 (goods.json)
```json
{
  "navigationStyle": "custom",
  "usingComponents": {
    "custom-navbar": "/components/custom-navbar/index"
  },
  "enablePullDownRefresh": false
}
```

### 页面模板 (goods.wxml)
```xml
<!-- 自定义导航栏 -->
<custom-navbar 
  title="商品详情"
  gradient-background="linear-gradient(135deg, #434343 0%, #000000 100%)"
  text-color="#ffffff"
  opacity="{{navOpacity}}"
  bind:back="onNavBack">
  <view slot="right" bindtap="onShareTap">
    <text style="color: #ffffff; font-size: 28rpx;">分享</text>
  </view>
</custom-navbar>

<!-- 页面内容需要添加顶部间距 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  <!-- 页面内容 -->
</view>
```

### 页面逻辑 (goods.js)
```javascript
Page({
  data: {
    navOpacity: 1,
    navbarHeight: 0
  },

  onLoad: function(options) {
    this.initNavbar();
  },

  // 初始化导航栏
  initNavbar: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const titleBarHeight = 44;
    const navbarHeight = statusBarHeight + titleBarHeight;
    
    this.setData({
      navbarHeight: navbarHeight
    });
  },

  // 导航栏返回事件
  onNavBack: function() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 分享按钮点击事件
  onShareTap: function() {
    // 处理分享逻辑
  }
});
```

## 示例2：商品列表页（森林渐变）

### 页面配置 (allGoods.json)
```json
{
  "navigationStyle": "custom",
  "usingComponents": {
    "custom-navbar": "/components/custom-navbar/index"
  },
  "enablePullDownRefresh": true
}
```

### 页面模板 (allGoods.wxml)
```xml
<!-- 自定义导航栏 -->
<custom-navbar 
  title="所有商品"
  gradient-background="linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)"
  text-color="#ffffff"
  show-back="{{true}}"
  bind:back="onNavBack">
  <view slot="right" bindtap="goToSearch">
    <image style="width: 40rpx; height: 40rpx;" src="../../static/images/search.png"></image>
  </view>
</custom-navbar>

<view class="container" style="padding-top: {{navbarHeight}}px;">
  <!-- 商品列表内容 -->
</view>
```

## 示例3：半透明导航栏

### 页面模板
```xml
<!-- 半透明导航栏，适用于有背景图的页面 -->
<custom-navbar 
  title="个人中心"
  gradient-background="rgba(0, 0, 0, 0.3)"
  text-color="#ffffff"
  opacity="{{0.9}}"
  bind:back="onNavBack">
</custom-navbar>
```

## 示例4：动态透明度（滚动时变化）

### 页面模板
```xml
<custom-navbar 
  title="首页"
  gradient-background="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
  text-color="#ffffff"
  opacity="{{scrollOpacity}}"
  bind:back="onNavBack">
</custom-navbar>

<scroll-view 
  class="scroll-container" 
  scroll-y="{{true}}"
  bindscroll="onPageScroll"
  style="padding-top: {{navbarHeight}}px;">
  <!-- 页面内容 -->
</scroll-view>
```

### 页面逻辑
```javascript
Page({
  data: {
    scrollOpacity: 0.3, // 初始透明度
    navbarHeight: 0
  },

  onPageScroll: function(e) {
    const scrollTop = e.detail.scrollTop;
    // 根据滚动距离计算透明度
    let opacity = Math.min(1, Math.max(0.3, scrollTop / 200));
    
    this.setData({
      scrollOpacity: opacity
    });
  }
});
```

## 示例5：完全透明导航栏

### 页面模板
```xml
<!-- 完全透明，只显示文字和按钮 -->
<custom-navbar 
  title="图片浏览"
  gradient-background="transparent"
  text-color="#ffffff"
  bind:back="onNavBack">
</custom-navbar>
```

## 常用渐变色配置

```javascript
// 深色系
"linear-gradient(135deg, #434343 0%, #000000 100%)"

// 蓝色系
"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"

// 粉色系
"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"

// 海洋系
"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"

// 森林系
"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)"

// 日落系
"linear-gradient(135deg, #fa709a 0%, #fee140 100%)"

// 半透明
"rgba(0, 0, 0, 0.3)"

// 完全透明
"transparent"
```

## 注意事项

1. **页面配置**：必须设置 `"navigationStyle": "custom"`
2. **内容间距**：页面内容需要添加 `padding-top` 避免被导航栏遮挡
3. **状态栏适配**：组件会自动适配不同设备的状态栏高度
4. **颜色搭配**：建议导航栏颜色与页面主色调保持一致
5. **性能优化**：避免频繁更新透明度，建议使用节流函数
