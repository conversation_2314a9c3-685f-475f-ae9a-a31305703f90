# TabBar 页面导航返回修复

## 问题描述
使用 custom-navbar 组件的页面无法正确返回到 tabBar 页面（如 me 页面）。

## 根本原因
**TabBar 页面导航限制**：
- `pages/ucenter/me/me` 是一个 tabBar 页面
- 从 tabBar 页面跳转到普通页面后，不能使用 `wx.navigateBack()` 返回 tabBar 页面
- 必须使用 `wx.switchTab()` 来跳转到 tabBar 页面

## 解决方案

### 1. 智能返回逻辑
检查上一页是否为 tabBar 页面，根据情况选择合适的返回方式：

```javascript
onNavBack: function() {
  const pages = getCurrentPages();
  
  if (pages.length > 1) {
    // 检查上一页是否是 tabBar 页面
    const prevPage = pages[pages.length - 2];
    
    if (prevPage.route === 'pages/ucenter/me/me') {
      // 如果上一页是 me 页面（tabBar页面），使用 switchTab
      wx.switchTab({
        url: '/pages/ucenter/me/me'
      });
    } else {
      // 否则正常返回
      wx.navigateBack({
        delta: 1
      });
    }
  } else {
    // 如果是第一页，跳转到个人中心页面
    wx.switchTab({
      url: '/pages/ucenter/me/me'
    });
  }
}
```

### 2. 修复的页面
- **充值页面** (`pages/ucenter/recharge/recharge.js`)
- **订单页面** (`pages/ucenter/order/order.js`)

## 技术细节

### TabBar 页面识别
通过检查页面栈中上一页的 `route` 属性来判断：
```javascript
const prevPage = pages[pages.length - 2];
if (prevPage.route === 'pages/ucenter/me/me') {
  // 是 tabBar 页面，使用 switchTab
}
```

### 导航方式选择
1. **普通页面返回**：使用 `wx.navigateBack({ delta: 1 })`
2. **TabBar 页面返回**：使用 `wx.switchTab({ url: '/pages/ucenter/me/me' })`

## 项目中的 TabBar 页面
根据 `app.json` 配置，项目中的 tabBar 页面包括：
- `pages/index/index` - 首页
- `pages/allGoods/allGoods` - 商品
- `pages/cart/cart` - 购物车
- `pages/ucenter/me/me` - 我的

## 测试场景

### ✅ 正常场景
1. **me 页面 → 充值页面 → 返回**：正确返回到 me 页面
2. **me 页面 → 订单页面 → 返回**：正确返回到 me 页面
3. **普通页面 → 充值页面 → 返回**：正确返回到上一页

### ✅ 边界场景
1. **直接访问充值页面**：返回到 me 页面
2. **直接访问订单页面**：返回到 me 页面

## 最佳实践

### 1. 通用返回逻辑模板
```javascript
onNavBack: function() {
  const pages = getCurrentPages();
  
  if (pages.length > 1) {
    const prevPage = pages[pages.length - 2];
    
    // 检查是否是 tabBar 页面
    const tabBarPages = [
      'pages/index/index',
      'pages/allGoods/allGoods', 
      'pages/cart/cart',
      'pages/ucenter/me/me'
    ];
    
    if (tabBarPages.includes(prevPage.route)) {
      wx.switchTab({ url: `/${prevPage.route}` });
    } else {
      wx.navigateBack({ delta: 1 });
    }
  } else {
    // 默认返回到首页或相关 tabBar 页面
    wx.switchTab({ url: '/pages/index/index' });
  }
}
```

### 2. 页面特定逻辑
对于特定的业务页面，可以根据业务逻辑选择默认的返回目标：
- **用户中心相关页面**：默认返回 me 页面
- **商品相关页面**：默认返回首页或商品页面
- **购物相关页面**：默认返回购物车页面

## 注意事项

1. **页面栈检查**：始终检查页面栈长度，避免数组越界
2. **路由匹配**：确保路由路径完全匹配
3. **用户体验**：选择最符合用户预期的返回目标
4. **错误处理**：为异常情况提供合理的默认行为

## 相关文件
- `/components/custom-navbar/index.js` - 组件核心逻辑
- `/pages/ucenter/recharge/recharge.js` - 充值页面返回逻辑
- `/pages/ucenter/order/order.js` - 订单页面返回逻辑
- `/app.json` - TabBar 配置
