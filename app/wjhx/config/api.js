const host = 'http://localhost:9999'
//const host = 'https://www.sxwjsm.com'

const ApiRootUrl = host + '/weshop-wjhx/wechat/';

const BaseUrl = host + '/weshop-wjhx/wechat/';
module.exports = {
  IndexUrl: BaseUrl + 'home/index', //首页数据接口
  CatalogList: BaseUrl + 'catalog/index',  //分类目录全部分类数据接口
  CatalogCurrent: BaseUrl + 'catalog/current',  //分类目录当前分类数据接口

  AuthLoginByWeixin: BaseUrl + 'auth/login', //微信登录
  ScanLoginConfirm: BaseUrl + 'auth/scanLoginConfirm', //扫码登录确认
  EstablishPromotionRelation: BaseUrl + 'user/establishPromotionRelation', //建立推广关系
  UpdateNickname: BaseUrl + 'user/updateNickname',
  UpdateAvatar:BaseUrl + 'user/updateAvatar',
  UserInfo: BaseUrl + 'user/info', // 获取用户信息
  UserBalance: BaseUrl + 'user/balance', // 获取用户余额
  UserPoints: BaseUrl + 'points/info', // 获取用户积分信息
  BindMobile: BaseUrl + 'auth/bindMobile', // 绑定手机号
  GeneratePromotionQrCode: BaseUrl + 'user/generatePromotionQrCode', // 生成推广二维码
  GetPromotionStats: BaseUrl + 'user/getPromotionStats', // 获取推广统计
  GetPromotionUserDetail: BaseUrl + 'user/getPromotionUserDetail', // 获取推广用户详情
  GetEarningsStats: BaseUrl + 'user/getEarningsStats', // 获取收益统计
  SubmitWithdraw: BaseUrl + 'user/submitWithdraw', // 提交提现申请
  GetDailyEarningsStats: BaseUrl + 'user/getDailyEarningsStats', // 获取每日收益统计
  GetMonthlyEarningsStats: BaseUrl + 'user/getMonthlyEarningsStats', // 获取月度收益统计
  GetDayOrderDetail: BaseUrl + 'user/getDayOrderDetail', // 获取每日订单明细
  GetMonthOrderDetail: BaseUrl + 'user/getMonthOrderDetail', // 获取月度订单明细

  GoodsCount: BaseUrl + 'goods/count',  //统计商品总数
  GoodsList: BaseUrl + 'goods/list',  //获得商品列表
  GoodsCategory: BaseUrl + 'goods/category',  //获得分类数据
  GoodsDetail: BaseUrl + 'goods/detail',  //获得商品的详情
  GoodsValidateSpec: BaseUrl + 'goods/validateSpec',  //验证规格组合
  GoodsNew: BaseUrl + 'goods/new',  //新品
  GoodsHot: BaseUrl + 'goods/hot',  //热门
  GoodsRelated: BaseUrl + 'goods/related',  //商品详情页的关联商品（大家都在看）

  BrandList: BaseUrl + 'brand/list',  //品牌列表
  BrandDetail: BaseUrl + 'brand',  //品牌详情

  CartList: BaseUrl + 'cart/index', //获取购物车的数据
  CartAdd: ApiRootUrl + 'cart/add', // 添加商品到购物车
  CartUpdate: ApiRootUrl + 'cart/update', // 更新购物车的商品
  CartDelete: ApiRootUrl + 'cart/delete', // 删除购物车的商品
  CartChecked: BaseUrl + 'cart/checked', // 选择或取消选择商品
  CartGoodsCount: BaseUrl + 'cart/goods-count', // 获取购物车商品件数
  CartCheckout: BaseUrl + 'cart/checkout', // 下单前信息确认

  OrderSubmit: ApiRootUrl + 'order/submit', // 提交订单
  OrderCardSubmit: ApiRootUrl + 'order/submitCardOrder', // 提交订单
  PayPrepayId: ApiRootUrl + 'pay/prepay', //获取微信统一下单prepay_id
  PayNotifySuc: ApiRootUrl + 'pay/notifySuc',

  CollectList: BaseUrl + 'collect/list',  //收藏列表
  CollectAddOrDelete: BaseUrl + 'collect/add-or-delete',  //添加或取消收藏

  CommentList: BaseUrl + 'comment/list',  //评论列表
  CommentCount: BaseUrl + 'comment/count',  //评论总数
  CommentPost: ApiRootUrl + 'comment/post',   //发表评论
  CommentLike: ApiRootUrl + 'comment/like',   //评论点赞
  CommentReply: ApiRootUrl + 'comment/reply',   //回复评论
  FileUpload: ApiRootUrl + 'file/upload',   //文件上传

  TopicList: BaseUrl + 'topic/list',  //专题列表
  TopicDetail: BaseUrl + 'topic',  //专题详情
  TopicRelated: BaseUrl + 'topic/related',  //相关专题

  SearchIndex: BaseUrl + 'search/index',  //搜索页面数据
  SearchResult: BaseUrl + 'search/result',  //搜索数据
  SearchHelper: BaseUrl + 'search/helper',  //搜索帮助
  SearchClearHistory: BaseUrl + 'search/clear-history',  //搜索帮助

  AddressList: BaseUrl + 'address/list',  //收货地址列表
  AddressDetail: BaseUrl + 'address/detail',  //收货地址详情
  AddressSave: ApiRootUrl + 'address/save',  //保存收货地址
  AddressDelete: ApiRootUrl + 'address/delete',  //保存收货地址
  AddressUpdate: ApiRootUrl + 'address/update',
  AddressSetDefault: ApiRootUrl + 'address/setDefault',

  RegionList: BaseUrl + 'region/list',  //获取区域列表

  OrderList: BaseUrl + 'order/list',  //订单列表
  OrderDetail: BaseUrl + 'order/detail',  //订单详情
  OrderCancel: ApiRootUrl + 'order/cancel',  //取消订单
  OrderExpress: BaseUrl + 'order/express', //物流详情
  OrderIdByCardId: BaseUrl + 'order/queryByCardId', //物流详情
  OrderConfirm: ApiRootUrl + 'order/confirm',  //取消订单

  FootprintList: BaseUrl + 'footprint/list',  //足迹列表
  FootprintDelete: BaseUrl + 'footprint',  //删除足迹

  CardSave: BaseUrl + 'card/save',//兑换卡保存
  CardList: BaseUrl + 'card/list',//兑换卡保存

  // 订阅消息相关
  SubscribeMsgTmplIds: BaseUrl + 'subscribe/tmplIds', // 获取订阅消息模板ID
  SubscribeMsgSend: BaseUrl + 'subscribe/send', // 发送订阅消息
  MsgNotice: ApiRootUrl + 'user/messageNotice',

  // 充值相关接口
  RechargeConfig: BaseUrl + 'recharge/config', // 获取充值配置
  RechargeCreate: BaseUrl + 'recharge/create', // 创建充值订单
  RechargePrepay: BaseUrl + 'recharge/prepay', // 获取充值预支付信息
  RechargeRecords: BaseUrl + 'recharge/records', // 获取充值记录
  RechargeCancel: BaseUrl + 'recharge/cancel', // 取消充值订单

  // 优惠券相关接口
  CouponStats: BaseUrl + 'coupon/stats', // 获取优惠券统计
  CouponList: BaseUrl + 'coupon/list', // 获取优惠券列表
  CouponUse: BaseUrl + 'coupon/use', // 使用优惠券
  CouponRules: BaseUrl + 'coupon-config/rules', // 获取优惠券使用规则
  
  // 购物车相关接口
  CartAdvancedCheckout: ApiRootUrl + 'cart/advanced-checkout', // 高级结算接口
  CartCheckoutWithPoints: BaseUrl + 'cart/checkout-with-points', // 积分结算接口

  // 积分相关接口
  UserPoints: BaseUrl + 'points/info', // 获取用户积分信息
  PointsRecords: BaseUrl + 'points/records', // 获取积分记录
  PointsConfig: BaseUrl + 'points/config', // 获取积分配置

  // 客服联系方式配置
  CustomerService: {
    phone: '136 2929 5757', // 客服电话
    wechat: 'WJSY-029-888', // 客服微信号
    workTime: '工作时间：7:00-24:00' // 工作时间说明
  },

  // 管理员相关接口
  AdminOrderList: BaseUrl + 'admin/order/list', // 管理员订单列表
  AdminOrderDetail: BaseUrl + 'order/detail', // 管理员订单详情（复用现有接口）
  AdminOrderDeliver: BaseUrl + 'admin/order/deliver', // 管理员发货
  AdminOrderCancel: BaseUrl + 'admin/order/cancel', // 管理员取消订单
  AdminUserList: BaseUrl + 'admin/user/list2', // 管理员用户列表
  AdminUserDetail: BaseUrl + 'admin/user/detail', // 管理员用户详情
  AdminStats: BaseUrl + 'admin/user/stats', // 管理员统计数据
};
