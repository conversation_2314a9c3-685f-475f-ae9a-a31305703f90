# 0元支付功能测试指南

## 测试准备

### 1. 数据准备
```sql
-- 创建测试用户（如果不存在）
INSERT INTO user (wechat_open_id, nickname, balance, points) 
VALUES ('test_openid_001', '测试用户', 100.00, 10000);

-- 创建测试商品
INSERT INTO goods (name, retail_price, market_price, list_pic_url, is_on_sale) 
VALUES ('测试商品', 50.00, 60.00, 'test.jpg', 1);

-- 创建测试优惠券（50元优惠券）
INSERT INTO user_coupon (user_id, name, amount, min_amount, status, start_time, end_time) 
VALUES (1, '50元优惠券', 50.00, 0.00, 'available', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY));
```

### 2. 积分配置
确保积分配置允许全额抵扣：
- 兑换比例：100积分 = 1元
- 最大使用比例：100%
- 最小使用积分：100

## 测试场景

### 场景1：优惠券全额抵扣
**步骤：**
1. 登录测试用户
2. 选择50元商品加入购物车
3. 进入结算页面
4. 选择50元优惠券
5. 提交订单
6. 验证直接跳转到支付成功页面

**预期结果：**
- 实际支付金额显示为0元
- 无微信支付弹窗
- 直接显示支付成功
- 订单状态为"待发货"

### 场景2：积分全额抵扣
**步骤：**
1. 确保用户有5000积分（可抵扣50元）
2. 选择50元商品
3. 开启积分抵扣开关
4. 设置使用5000积分
5. 提交订单

**预期结果：**
- 积分抵扣50元
- 实际支付金额为0元
- 直接完成订单

### 场景3：余额全额抵扣
**步骤：**
1. 确保用户余额有50元
2. 选择50元商品
3. 开启余额抵扣开关
4. 设置使用50元余额
5. 提交订单

**预期结果：**
- 余额抵扣50元
- 实际支付金额为0元
- 直接完成订单

### 场景4：组合抵扣
**步骤：**
1. 选择100元商品
2. 使用50元优惠券
3. 开启积分抵扣，使用5000积分（50元）
4. 提交订单

**预期结果：**
- 优惠券抵扣50元
- 积分抵扣50元
- 实际支付金额为0元
- 直接完成订单

### 场景5：礼券兑换（type=0）
**步骤：**
1. 用户有可用礼券
2. 选择礼券兑换商品
3. 提交订单

**预期结果：**
- 实际支付金额为0元
- 直接完成订单
- 跳转到兑换成功页面

## 验证要点

### 前端验证
1. **价格显示**：实际支付金额正确显示为0.00元
2. **支付流程**：无微信支付弹窗
3. **页面跳转**：直接跳转到支付成功页面
4. **用户体验**：整个流程流畅，无异常提示

### 后端验证
1. **订单状态**：订单状态为"待发货"
2. **支付状态**：支付状态为"已支付"
3. **金额计算**：各项抵扣金额正确
4. **日志记录**：有相应的处理日志

### 数据库验证
```sql
-- 检查订单状态
SELECT id, order_sn, actual_price, order_status, pay_status 
FROM `order` 
WHERE user_id = 1 
ORDER BY add_time DESC 
LIMIT 5;

-- 检查优惠券使用状态
SELECT id, name, amount, status, used_time 
FROM user_coupon 
WHERE user_id = 1 
ORDER BY used_time DESC 
LIMIT 5;

-- 检查积分记录
SELECT id, user_id, points, type, description, create_time 
FROM points_record 
WHERE user_id = 1 
ORDER BY create_time DESC 
LIMIT 5;

-- 检查余额变动
SELECT balance FROM user WHERE id = 1;
```

## 异常测试

### 异常场景1：金额计算错误
**模拟：** 前端显示0元，但后端计算不为0
**预期：** 后端重新计算，以后端结果为准

### 异常场景2：重复提交
**模拟：** 快速点击提交按钮
**预期：** 只创建一个订单，避免重复处理

### 异常场景3：优惠券失效
**模拟：** 提交时优惠券已过期
**预期：** 给出相应错误提示

## 性能测试

### 响应时间
- 0元订单处理时间应该更快（无需调用微信支付API）
- 页面跳转应该更流畅

### 并发测试
- 多个用户同时提交0元订单
- 验证系统稳定性

## 回归测试

确保修改不影响正常支付流程：

### 正常支付测试
1. 选择商品，不使用任何优惠
2. 提交订单
3. 验证正常调用微信支付
4. 完成支付流程

### 部分抵扣测试
1. 选择100元商品
2. 使用50元优惠券
3. 实际支付50元
4. 验证正常调用微信支付

## 测试报告模板

```
测试场景：[场景名称]
测试时间：[时间]
测试人员：[姓名]

测试步骤：
1. [步骤1]
2. [步骤2]
...

实际结果：
- [结果1]
- [结果2]
...

是否通过：[通过/失败]
问题描述：[如有问题，详细描述]
```

通过以上测试，可以确保0元支付功能正常工作，同时不影响现有的支付流程。