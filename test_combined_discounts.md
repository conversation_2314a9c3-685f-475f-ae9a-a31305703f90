# 组合抵扣功能测试指南

## 功能概述

结账页面现在支持余额抵扣、优惠券、积分抵扣同时使用，具有以下特性：

### ✅ 核心功能
1. **三种抵扣方式同时显示**：无论是否选择了其他抵扣方式，三种抵扣选项都会显示
2. **独立的最大可用金额计算**：每种抵扣方式的最大可用金额基于订单总额独立计算
3. **智能抵扣优先级**：优惠券 > 积分 > 余额，确保用户获得最大优惠
4. **实时价格计算**：任何抵扣方式的变更都会实时更新最终价格

### ✅ 用户体验优化
1. **一键优惠功能**：自动选择最优优惠券并启用所有可用抵扣
2. **组合抵扣提示**：清晰显示使用了多少种优惠方式和总节省金额
3. **智能错误提示**：当抵扣不可用时给出明确提示
4. **使用最大按钮**：快速使用最大可用余额或积分

## 测试步骤

### 1. 基础显示测试
- [ ] 有余额时，余额抵扣卡片正常显示
- [ ] 有积分时，积分抵扣卡片正常显示  
- [ ] 有可用优惠券时，优惠券选择正常显示
- [ ] 三种抵扣方式可以同时显示

### 2. 独立使用测试
- [ ] 仅使用余额抵扣，价格计算正确
- [ ] 仅使用积分抵扣，价格计算正确
- [ ] 仅使用优惠券，价格计算正确

### 3. 组合使用测试
- [ ] 余额 + 积分组合使用
- [ ] 余额 + 优惠券组合使用
- [ ] 积分 + 优惠券组合使用
- [ ] 三种方式同时使用

### 4. 边界情况测试
- [ ] 余额不足时的处理
- [ ] 积分不足时的处理
- [ ] 优惠券不满足条件时的处理
- [ ] 抵扣金额超过订单总额时的处理

### 5. 交互功能测试
- [ ] 一键优惠功能正常工作
- [ ] 使用最大余额按钮正常工作
- [ ] 使用最大积分按钮正常工作
- [ ] 组合抵扣提示正确显示

## 关键修改点

### 1. 显示逻辑修改
```javascript
// 修改前：积分抵扣卡片只在有最大可用积分时显示
wx:if="{{userPoints > 0 && maxUsablePoints > 0}}"

// 修改后：只要有积分就显示
wx:if="{{userPoints > 0}}"
```

### 2. 最大可用金额计算
```javascript
// 修改前：基于剩余金额计算最大可用积分/余额
const remainingAmount = baseOrderAmount - usedAmount;

// 修改后：基于订单总额独立计算
const newMaxUsableBalance = Math.min(userBalance, baseOrderAmount);
const newMaxUsablePoints = Math.min(userPoints, maxPointsByAmount);
```

### 3. 抵扣计算优化
- 增加了详细的抵扣明细记录
- 优化了抵扣优先级处理
- 增加了组合抵扣信息统计

### 4. 用户体验增强
- 增加了智能错误提示
- 优化了一键优惠功能
- 增加了使用最大积分功能
- 增强了组合抵扣状态显示

## 预期效果

用户现在可以：
1. 同时看到所有可用的抵扣方式
2. 自由组合使用不同的抵扣方式
3. 通过一键优惠快速使用所有优惠
4. 清楚了解每种抵扣方式的使用情况和节省金额

## 注意事项

1. **抵扣优先级**：系统会按照优惠券 > 积分 > 余额的顺序计算抵扣，确保用户获得最大优惠
2. **金额限制**：总抵扣金额不会超过订单总额
3. **实时计算**：任何抵扣方式的变更都会触发价格重新计算
4. **错误处理**：当抵扣不可用时会给出明确提示并重置状态
