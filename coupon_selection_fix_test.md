# 优惠券选择功能修复测试

## 修复内容

### 1. 数据结构统一
- 确保优惠券选择页面返回的数据结构与checkout页面期望的一致
- 在confirmSelection方法中正确构建checkedCoupon对象
- 同时设置name和title字段，确保显示兼容性

### 2. 页面生命周期优化
- 添加skipCheckoutReload标志，避免从优惠券选择页面返回时重新加载数据
- 防止onShow方法覆盖手动设置的优惠券选择

### 3. 调试日志增强
- 在关键步骤添加console.log，便于问题排查
- 记录优惠券选择、确认、数据更新等关键操作

## 测试步骤

### 准备工作
1. 确保数据库中有测试优惠券数据：
```sql
-- 执行测试数据脚本
source database/test_coupon_data.sql;
```

2. 确保用户已登录并有购物车商品

### 测试用例1：正常选择优惠券
1. 进入checkout页面
2. 点击"优惠券"卡片，应该跳转到优惠券选择页面
3. 查看控制台日志，确认优惠券列表正确加载
4. 选择一张可用的优惠券（如"10元消费券"）
5. 点击"确认选择"按钮
6. 返回checkout页面，验证：
   - 优惠券卡片显示选中的优惠券名称（不再显示"请选择优惠券"）
   - 费用明细中显示优惠券抵扣金额
   - 实付金额正确计算

### 测试用例2：取消优惠券选择
1. 在checkout页面已选择优惠券的状态下
2. 再次点击优惠券卡片
3. 选择"不使用优惠券"
4. 点击"确认选择"
5. 返回checkout页面，验证：
   - 优惠券卡片显示"请选择优惠券"
   - 费用明细中不显示优惠券抵扣
   - 实付金额恢复到原价

### 测试用例3：切换优惠券
1. 选择一张优惠券（如"5元消费券"）
2. 确认后返回checkout页面
3. 再次进入优惠券选择页面
4. 验证之前选择的优惠券仍然被选中
5. 选择另一张优惠券（如"10元消费券"）
6. 确认后验证新优惠券正确显示和计算

### 测试用例4：不满足条件的优惠券
1. 在商品总价较低的情况下（如30元）
2. 进入优惠券选择页面
3. 验证高门槛优惠券（如"50元满200减券"）显示在"不可用优惠券"列表中
4. 尝试选择可用的优惠券，确认功能正常

## 预期结果

### 成功标准
1. ✅ 优惠券选择页面能正确加载和显示优惠券列表
2. ✅ 选择优惠券后，checkout页面正确显示优惠券名称
3. ✅ 费用计算正确，包含优惠券抵扣
4. ✅ 可以正常取消和重新选择优惠券
5. ✅ 页面跳转流畅，无数据丢失

### 调试信息检查
在浏览器控制台中应该看到以下日志：
```
加载的优惠券数据: {availableCoupons: [...], unavailableCoupons: [...]}
选择优惠券: {id: 1, name: "10元消费券", ...}
确认选择优惠券: {selectedCouponId: 1, selectedCoupon: {...}}
设置选中的优惠券: {id: 1, name: "10元消费券", ...}
更新checkout页面数据: {couponId: 1, checkedCoupon: {...}}
checkout页面onShow，当前数据: {couponId: 1, checkedCoupon: {...}}
跳过重新加载checkout数据
```

## 故障排除

### 问题1：优惠券列表为空
- 检查数据库中是否有测试数据
- 检查用户登录状态
- 检查API接口返回

### 问题2：选择后仍显示"请选择优惠券"
- 检查confirmSelection方法中的数据构建
- 检查checkout页面的数据绑定
- 检查是否被onShow方法覆盖

### 问题3：价格计算错误
- 检查recalculatePrice方法
- 检查后端CartAdvancedCheckout接口
- 验证优惠券金额和最低消费金额

### 问题4：页面跳转异常
- 检查页面路径配置
- 检查app.json中的页面注册
- 验证导航参数传递

## 回归测试

修复完成后，还需要测试以下功能确保没有破坏：
1. 积分抵扣功能
2. 余额抵扣功能
3. 地址选择功能
4. 订单提交功能
5. 多种抵扣方式组合使用