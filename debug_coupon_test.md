# 优惠券选择功能调试指南

## 问题描述
在优惠券选择页面选择优惠券后，返回checkout页面仍显示"请选择优惠券"，表示选择未成功。

## 调试步骤

### 1. 检查数据库中是否有测试优惠券数据
```sql
-- 查询用户优惠券数据
SELECT id, user_id, title, amount, min_amount, status, 
       DATE_FORMAT(start_time, '%Y-%m-%d') as start_date,
       DATE_FORMAT(end_time, '%Y-%m-%d') as end_date
FROM weshop_user_coupon 
WHERE user_id = 1 AND status = 0
ORDER BY create_time DESC;
```

### 2. 测试优惠券API接口
在浏览器开发者工具中测试：
```javascript
// 测试获取优惠券列表
fetch('/weshop-wjhx/wechat/coupon/list?status=available', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  }
})
.then(response => response.json())
.then(data => console.log('优惠券列表:', data));
```

### 3. 检查前端调试日志
在微信开发者工具的控制台中查看以下日志：
- 优惠券选择页面的"加载的优惠券数据"
- 选择优惠券时的"选择优惠券"日志
- 确认选择时的"确认选择优惠券"和"设置选中的优惠券"日志
- checkout页面的"checkout页面onShow，当前数据"日志

### 4. 验证数据传递
检查以下数据流：
1. 优惠券选择页面加载优惠券列表
2. 用户选择优惠券，selectedCoupon被正确设置
3. 确认选择时，正确构建updateData对象
4. 返回checkout页面时，checkedCoupon被正确更新

### 5. 常见问题排查

#### 问题1：优惠券列表为空
- 检查数据库中是否有测试数据
- 检查用户登录状态和权限
- 检查API接口是否正常返回数据

#### 问题2：选择优惠券后数据未更新
- 检查confirmSelection方法中的updateData构建逻辑
- 检查prevPage.setData是否被正确调用
- 检查checkout页面的数据绑定是否正确

#### 问题3：优惠券名称显示问题
- 检查后端是否正确设置了name字段
- 检查前端显示逻辑：`{{checkedCoupon.name || checkedCoupon.title}}`

## 修复方案

### 方案1：确保数据结构一致
在优惠券选择页面的confirmSelection方法中，确保传递的数据结构正确：
```javascript
updateData.checkedCoupon = {
  id: this.data.selectedCoupon.id,
  name: this.data.selectedCoupon.name || this.data.selectedCoupon.title,
  title: this.data.selectedCoupon.title,
  amount: this.data.selectedCoupon.amount,
  minAmount: this.data.selectedCoupon.minAmount
};
```

### 方案2：添加调试日志
在关键位置添加console.log来跟踪数据流：
- 优惠券加载时
- 优惠券选择时
- 确认选择时
- checkout页面数据更新时

### 方案3：检查页面生命周期
确保checkout页面的onShow方法不会覆盖手动设置的优惠券数据。

## 测试验证

### 测试用例1：基本选择流程
1. 进入checkout页面，点击优惠券卡片
2. 在优惠券选择页面选择一张优惠券
3. 点击确认选择
4. 验证返回checkout页面后优惠券信息正确显示

### 测试用例2：取消选择
1. 在优惠券选择页面选择"不使用优惠券"
2. 点击确认选择
3. 验证返回checkout页面后显示"请选择优惠券"

### 测试用例3：重新选择
1. 选择一张优惠券后返回
2. 再次进入优惠券选择页面
3. 验证之前选择的优惠券仍然被选中
4. 选择其他优惠券并确认
5. 验证新选择的优惠券正确显示